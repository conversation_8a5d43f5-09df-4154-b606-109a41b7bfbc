package com.example.demo.ui.channel_num;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v7.app.AppCompatActivity;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.example.demo.R;

public class ChannelNumManageActivity extends AppCompatActivity implements View.OnClickListener {

    private TextView tvChannelNum;
    private TextView tvChannelVersion;
    private EditText etChannelNum;
    private EditText etChannelVersion;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_channel_num_manage);
        initView();
        setData();
    }

    private void setData() {
        String channelNum = "渠道号：" + ChannelNumManage.channelNum;
        String channelVersion = "渠道版本号：" + ChannelNumManage.channelVersion;
        tvChannelNum.setText(channelNum);
        tvChannelVersion.setText(channelVersion);
    }

    private void initView() {
        tvChannelNum = findViewById(R.id.a_channel_channelNum_tv);
        tvChannelVersion = findViewById(R.id.a_channel_channelVersion_tv);
        etChannelNum = findViewById(R.id.a_channel_channelNum_et);
        etChannelVersion = findViewById(R.id.a_channel_channelVersion_et);
        findViewById(R.id.a_channel_save_btn).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.a_channel_save_btn:
                ChannelNumManage.channelNum = etChannelNum.getText().toString().trim();
                ChannelNumManage.channelVersion = etChannelVersion.getText().toString().trim();
                setData();
                break;
        }
    }
}
