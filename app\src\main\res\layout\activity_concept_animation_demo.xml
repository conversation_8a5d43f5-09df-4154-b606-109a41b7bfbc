<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_light_bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 顶部标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="第1个演示：礼物盒"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onBackground"
                android:gravity="center"
                android:layout_marginEnd="48dp" />

        </LinearLayout>

        <!-- 旁白说明 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/md_theme_light_tertiaryContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🎬 动画演示"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onTertiaryContainer"
                    android:gravity="center"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_narration"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="看！这里有3组礼物盒，每组有4个"
                    android:textSize="14sp"
                    android:textColor="@color/md_theme_light_onTertiaryContainer"
                    android:gravity="center" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 分组展示区域 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="👀 观看动画演示"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSurface"
                    android:gravity="center"
                    android:layout_marginBottom="16dp" />

                <!-- 分组容器 -->
                <LinearLayout
                    android:id="@+id/container_groups"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:layout_marginBottom="16dp" />

                <!-- 播放按钮 -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_play"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:layout_gravity="center"
                    android:text="▶️ 播放动画"
                    android:textSize="14sp"
                    app:cornerRadius="24dp"
                    app:backgroundTint="@color/md_theme_light_secondary" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 公式显示 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/md_theme_light_primaryContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="✨ 乘法公式"
                    android:textSize="14sp"
                    android:textColor="@color/md_theme_light_onPrimaryContainer"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_formula"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3 × 4 = 12"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onPrimaryContainer"
                    android:visibility="invisible" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 问题和答案区域 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/md_theme_light_secondaryContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:id="@+id/tv_question"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="总共有多少个？"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSecondaryContainer"
                    android:gravity="center"
                    android:layout_marginBottom="16dp"
                    android:visibility="invisible" />

                <!-- 答案选项 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_answer1"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="12"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:cornerRadius="24dp"
                        app:backgroundTint="@color/md_theme_light_primary"
                        android:layout_marginEnd="8dp"
                        android:visibility="invisible" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_answer2"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="14"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:cornerRadius="24dp"
                        app:backgroundTint="@color/md_theme_light_primary"
                        android:layout_marginStart="4dp"
                        android:layout_marginEnd="4dp"
                        android:visibility="invisible" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_answer3"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="10"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:cornerRadius="24dp"
                        app:backgroundTint="@color/md_theme_light_primary"
                        android:layout_marginStart="8dp"
                        android:visibility="invisible" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 总计显示区域（预留） -->
        <LinearLayout
            android:id="@+id/container_total"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="24dp"
            android:visibility="gone" />

        <!-- 控制按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_next"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:text="下一个演示 ▶️"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="28dp"
                app:backgroundTint="@color/md_theme_light_primary"
                android:textColor="@android:color/white"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_complete"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:text="完成学习 ✅"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="28dp"
                app:backgroundTint="@color/md_theme_light_tertiary"
                android:textColor="@android:color/white"
                android:layout_marginStart="8dp"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
