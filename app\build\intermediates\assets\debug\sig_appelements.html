<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=0"><meta content="yes" name="apple-mobile-web-app-capable"><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><meta name="renderer" content="webkit"><link rel="icon" href="data:image/ico;base64,aWNv"><title>新版6要素6662</title><style>html, body, #app {
      margin: 0;
      width: 100vw;
      height: 100vh;
      overflow: hidden;
      background: transparent !important;
    }
    #error_mask {
      display: none;
      flex-direction: column;
      width: 100vw;
      height: 100vh;
      align-items: center;
      justify-content: center;
      background: rgba(0,0,0,0.6);
    }
    #error_mask .close {
      z-index: 9999;
      width: 28px;
      height: 28px;
      background-color: #e33122;
      color: #fff;
      padding: 4px;
      border-radius: 50%;
      top: 10px;
      right: 10px;
    }
    #error_mask .error {
      font-size: 20px;
      color: #e33122;
      margin-bottom: 20px;
    }</style><div id="error_mask"><p class="error">六要素加载失败~</p><svg class="close" id="close" fill="currentColor" t="1595475933716" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2011" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M103.688 938.576a28 28 0 0 1-19.8-47.8L898.152 76.52a28 28 0 1 1 39.6 39.592L123.488 930.4a27.92 27.92 0 0 1-19.8 8.176z"></path><path d="M917.952 938.576a27.928 27.928 0 0 1-19.8-8.2L83.888 116.112a28 28 0 0 1 39.6-39.592l814.264 814.256a28 28 0 0 1-19.8 47.8z"></path></svg></div><script>function iframeSendSrc(src) {
      var iframe = document.createElement('iframe')
      iframe.setAttribute('src', src)
      document.documentElement.appendChild(iframe)
      iframe.parentNode.removeChild(iframe)
      iframe = null
    }

    setTimeout(() => {
      if (!window.INIT) {
        const errorMask = document.getElementById('error_mask')
        const closeDom = document.getElementById('close')
        errorMask.style.display = 'flex'
        closeDom.onclick = function () {
          iframeSendSrc('sigmobAd://closeFourElements')
        }
      }
    }, 3000)</script><link href="https://n.sigmob.cn/mraid/prod/6662/6662.a336d5e1.js" rel="preload" as="script"><link href="https://n.sigmob.cn/mraid/prod/6662/6662.c7506a07.css" rel="preload" as="style"><link href="https://n.sigmob.cn/mraid/prod/6662/6662.c7506a07.css" rel="stylesheet"><script>var sigmob=window.sigmob={},privacyInfo={},iframeSendSrc=function(i){var o=document.createElement("iframe");o.setAttribute("src",i),document.documentElement.appendChild(o),o.parentNode.removeChild(o)},initPrivacyInfo=(sigmob.getPrivacyInfo=function(){return privacyInfo},sigmob.closeFourElements=function(){iframeSendSrc("sigmobAd://closeFourElements")},sigmob.useCustomClose=function(){iframeSendSrc("sigmobAd://useCustomClose")},sigmob.buttonClick=function(i,o,n){iframeSendSrc(n?"sigmobAd://buttonClick?url="+n+"&x="+i+"&y="+o:"sigmobAd://buttonClick?x="+i+"&y="+o)},function(){window.sigPrivacy?privacyInfo=window.sigPrivacy.getPrivacyInfo():console.error("sigPrivacy注册失败===")});initPrivacyInfo();</script></head><body><div id="app"></div><script src="https://n.sigmob.cn/mraid/prod/6662/6662.a336d5e1.js"></script></body></html>