{"logs": [{"outputFile": "com.example.concentration.app-mergeDebugResources-48:/values-v26/values-v26.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\eb7e0db8c5a1e03615b67a657c6a7904\\transformed\\jetified-kssdk-ct-3.3.76.1\\res\\values-v26\\values-v26.xml", "from": {"startLines": "2,6", "startColumns": "4,4", "startOffsets": "55,307", "endLines": "5,6", "endColumns": "12,110", "endOffsets": "302,413"}, "to": {"startLines": "17,21", "startColumns": "4,4", "startOffsets": "889,1141", "endLines": "20,21", "endColumns": "12,110", "endOffsets": "1136,1247"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\38fdf4e56534b3c4c6a8297bf7eeddee\\transformed\\appcompat-1.6.1\\res\\values-v26\\values-v26.xml", "from": {"startLines": "2,3,4,8,12,16", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,130,217,381,557,796", "endLines": "2,3,7,11,15,16", "endColumns": "74,86,12,12,12,92", "endOffsets": "125,212,376,552,791,884"}}]}]}