package com.example.concentration;

import android.content.Context;
import android.content.SharedPreferences;

public class ThemeManager {
    private static final String PREFS_NAME = "theme_prefs";
    private static final String KEY_CURRENT_THEME = "current_theme";
    private static final String KEY_UNLOCKED_THEMES = "unlocked_themes";
    
    private static ThemeManager instance;
    private SharedPreferences prefs;
    private Context context;
    
    // 主题类型
    public enum ThemeType {
        DEFAULT("default", "默认主题", "🎯", 0),
        ANIMAL("animal", "动物世界", "🦁", 5),
        SPACE("space", "太空探索", "🚀", 15),
        CARTOON("cartoon", "卡通乐园", "🎨", 30);
        
        private final String id;
        private final String name;
        private final String emoji;
        private final int unlockRequirement; // 需要完成的训练次数
        
        ThemeType(String id, String name, String emoji, int unlockRequirement) {
            this.id = id;
            this.name = name;
            this.emoji = emoji;
            this.unlockRequirement = unlockRequirement;
        }
        
        public String getId() { return id; }
        public String getName() { return name; }
        public String getEmoji() { return emoji; }
        public int getUnlockRequirement() { return unlockRequirement; }
    }
    
    private ThemeManager(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        
        // 默认解锁第一个主题
        if (!isThemeUnlocked(ThemeType.DEFAULT)) {
            unlockTheme(ThemeType.DEFAULT);
        }
    }
    
    public static ThemeManager getInstance(Context context) {
        if (instance == null) {
            instance = new ThemeManager(context);
        }
        return instance;
    }
    
    // 获取当前主题
    public ThemeType getCurrentTheme() {
        String themeId = prefs.getString(KEY_CURRENT_THEME, ThemeType.DEFAULT.getId());
        for (ThemeType theme : ThemeType.values()) {
            if (theme.getId().equals(themeId)) {
                return theme;
            }
        }
        return ThemeType.DEFAULT;
    }
    
    // 设置当前主题
    public void setCurrentTheme(ThemeType theme) {
        if (isThemeUnlocked(theme) || SettingsActivity.isTestModeEnabled(context)) {
            prefs.edit().putString(KEY_CURRENT_THEME, theme.getId()).apply();
        }
    }
    
    // 检查主题是否已解锁
    public boolean isThemeUnlocked(ThemeType theme) {
        // 检查测试模式
        if (SettingsActivity.isTestModeEnabled(context)) {
            return true; // 测试模式下所有主题都解锁
        }

        String unlockedThemes = prefs.getString(KEY_UNLOCKED_THEMES, "");
        return unlockedThemes.contains(theme.getId());
    }
    
    // 解锁主题
    public void unlockTheme(ThemeType theme) {
        String unlockedThemes = prefs.getString(KEY_UNLOCKED_THEMES, "");
        if (!unlockedThemes.contains(theme.getId())) {
            unlockedThemes += theme.getId() + ",";
            prefs.edit().putString(KEY_UNLOCKED_THEMES, unlockedThemes).apply();
        }
    }
    
    // 检查是否可以解锁新主题
    public void checkAndUnlockThemes(int totalGames) {
        for (ThemeType theme : ThemeType.values()) {
            if (!isThemeUnlocked(theme) && totalGames >= theme.getUnlockRequirement()) {
                unlockTheme(theme);
            }
        }
    }
    
    // 获取主题的主要颜色
    public int getPrimaryColor() {
        switch (getCurrentTheme()) {
            case ANIMAL:
                return context.getResources().getColor(R.color.theme_animal_primary);
            case SPACE:
                return context.getResources().getColor(R.color.theme_space_primary);
            case CARTOON:
                return context.getResources().getColor(R.color.theme_cartoon_primary);
            default:
                return context.getResources().getColor(R.color.md_theme_light_primary);
        }
    }
    
    // 获取主题的次要颜色
    public int getSecondaryColor() {
        switch (getCurrentTheme()) {
            case ANIMAL:
                return context.getResources().getColor(R.color.theme_animal_secondary);
            case SPACE:
                return context.getResources().getColor(R.color.theme_space_secondary);
            case CARTOON:
                return context.getResources().getColor(R.color.theme_cartoon_secondary);
            default:
                return context.getResources().getColor(R.color.md_theme_light_secondary);
        }
    }
    
    // 获取主题的背景颜色
    public int getBackgroundColor() {
        switch (getCurrentTheme()) {
            case ANIMAL:
                return context.getResources().getColor(R.color.theme_animal_background);
            case SPACE:
                return context.getResources().getColor(R.color.theme_space_background);
            case CARTOON:
                return context.getResources().getColor(R.color.theme_cartoon_background);
            default:
                return context.getResources().getColor(R.color.md_theme_light_surface);
        }
    }
    
    // 获取主题的故事背景
    public String getThemeStory() {
        switch (getCurrentTheme()) {
            case ANIMAL:
                return "🦁 帮助小动物们找到回家的路！每个数字都是一个小动物，按顺序引导它们回到温暖的家。";
            case SPACE:
                return "🚀 太空探险开始了！收集散落在宇宙中的能量水晶，按顺序激活星际传送门。";
            case CARTOON:
                return "🎨 进入奇幻的卡通世界！帮助小精灵收集魔法数字宝石，解开古老的魔法阵。";
            default:
                return "🎯 专注力训练开始！按顺序点击数字，提升你的注意力和反应速度。";
        }
    }
}
