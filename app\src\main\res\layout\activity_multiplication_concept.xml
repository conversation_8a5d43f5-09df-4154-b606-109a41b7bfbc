<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_light_bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 顶部标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="24dp">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="🎯 阶段1：基础概念认知"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onBackground"
                android:gravity="center"
                android:layout_marginEnd="48dp" />

        </LinearLayout>

        <!-- 进度显示 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:id="@+id/tv_progress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="学习进度: 0/3 (0%)"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onBackground"
                    android:gravity="center" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 课程1：视觉化乘法演示 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_visual_demo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🐣"
                    android:textSize="32sp"
                    android:layout_marginEnd="16dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="课程1：视觉化乘法演示"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_onSurface" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="用小动物排列理解乘法含义"
                        android:textSize="14sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant"
                        android:layout_marginTop="4dp" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="▶️"
                    android:textSize="24sp"
                    android:textColor="@color/md_theme_light_primary" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 课程2：互动点数游戏 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_interactive_count"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="👆"
                    android:textSize="32sp"
                    android:layout_marginEnd="16dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="课程2：互动点数游戏"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_onSurface" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="点击数数，建立乘法与加法联系"
                        android:textSize="14sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant"
                        android:layout_marginTop="4dp" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="▶️"
                    android:textSize="24sp"
                    android:textColor="@color/md_theme_light_primary" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 课程3已合并到课程1，不再需要单独的卡片 -->

        <!-- 开始学习按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_start_learning"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="完成所有课程后解锁"
            android:textSize="18sp"
            android:textStyle="bold"
            app:cornerRadius="28dp"
            app:backgroundTint="@color/md_theme_light_primary"
            android:textColor="@android:color/white"
            android:enabled="false" />

    </LinearLayout>

</ScrollView>
