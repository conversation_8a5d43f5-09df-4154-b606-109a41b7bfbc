package com.example.concentration;

/**
 * 记忆游戏模式枚举
 * 定义两种不同的记忆训练模式
 */
public enum MemoryGameMode {
    NUMBER_CONTENT("数字内容", "记住数字序列的内容", "number_content"),
    POSITION_MEMORY("位置记忆", "记住数字在网格中的位置", "position_memory");
    
    private final String displayName;
    private final String description;
    private final String key;
    
    MemoryGameMode(String displayName, String description, String key) {
        this.displayName = displayName;
        this.description = description;
        this.key = key;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getKey() {
        return key;
    }
    
    public static MemoryGameMode fromKey(String key) {
        for (MemoryGameMode mode : values()) {
            if (mode.key.equals(key)) {
                return mode;
            }
        }
        return NUMBER_CONTENT; // 默认模式
    }
}
