package com.example.demo.ui;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.support.v4.app.FragmentActivity;

import com.example.demo.R;

public class NewsFeedFragmentActivity extends FragmentActivity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_news_feed);

        //新闻资讯
        com.by.mob.ByNewsFeedManager.replaceFrameLayout(this, "1531874289605689405", "", "", new com.by.mob.ByNewsFeedManager.IReplaceListener() {
            @Override
            public void onFail(String msg) {

            }

            @Override
            public void getContentPage(Fragment feedPage) {
                getSupportFragmentManager().beginTransaction()
                        .replace(R.id.fragment_container, feedPage)
                        .commitAllowingStateLoss();
            }
        });
    }
}
