package com.example.concentration;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.button.MaterialButton;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class MemoryCardGameActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private TextView tvTitle, tvScore, tvTimer, tvProgress;
    private RecyclerView recyclerCards;
    private MaterialButton btnRestart;
    
    private int currentNumber;
    private SharedPreferences prefs;
    private List<MemoryCard> cards;
    private MemoryCardAdapter adapter;

    private int score = 0;
    private long startTime;
    private Handler timerHandler = new Handler();
    private Runnable timerRunnable;

    private MemoryCard firstFlippedCard = null;
    private MemoryCard secondFlippedCard = null;
    private boolean isProcessing = false;
    private int matchedPairs = 0;
    private SoundManager soundManager;
    private AnimalImageManager animalImageManager;
    private VoiceManager voiceManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_card_game);
        
        currentNumber = getIntent().getIntExtra("number", 1);
        prefs = getSharedPreferences("multiplication_prefs", MODE_PRIVATE);
        soundManager = SoundManager.getInstance(this);
        animalImageManager = new AnimalImageManager();
        voiceManager = VoiceManager.getInstance(this);

        initViews();
        setupClickListeners();
        setupGame();
        updateProgress(); // 初始化进度显示
        startTimer();

        // 播放游戏规则说明
        String rules = "欢迎来到记忆卡片游戏！请找到相同的乘法算式和答案，点击两张卡片进行配对。";
        voiceManager.speakWithCallback(rules, null);
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        tvTitle = findViewById(R.id.tv_title);
        tvScore = findViewById(R.id.tv_score);
        tvTimer = findViewById(R.id.tv_timer);
        tvProgress = findViewById(R.id.tv_progress);
        recyclerCards = findViewById(R.id.recycler_cards);
        btnRestart = findViewById(R.id.btn_restart);
        
        tvTitle.setText(currentNumber + " 的乘法记忆卡片");
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        btnRestart.setOnClickListener(v -> restartGame());
    }
    
    private void setupGame() {
        cards = new ArrayList<>();

        // 生成动物图片
        List<AnimalImageManager.CardAnimal> cardAnimals = animalImageManager.generateCardAnimals(18);
        int animalIndex = 0;

        // 创建乘法表卡片对
        for (int i = 1; i <= 9; i++) {
            // 问题卡片
            MemoryCard questionCard = new MemoryCard();
            questionCard.id = i * 2 - 1;
            questionCard.content = currentNumber + " × " + i;
            questionCard.isQuestion = true;
            questionCard.matchId = i;
            questionCard.animalEmoji = cardAnimals.get(animalIndex).emoji;
            questionCard.backgroundColor = cardAnimals.get(animalIndex).backgroundColor;
            cards.add(questionCard);
            animalIndex++;

            // 答案卡片
            MemoryCard answerCard = new MemoryCard();
            answerCard.id = i * 2;
            answerCard.content = String.valueOf(currentNumber * i);
            answerCard.isQuestion = false;
            answerCard.matchId = i;
            answerCard.animalEmoji = cardAnimals.get(animalIndex).emoji;
            answerCard.backgroundColor = cardAnimals.get(animalIndex).backgroundColor;
            cards.add(answerCard);
            animalIndex++;
        }

        // 打乱卡片顺序
        Collections.shuffle(cards);
        
        // 设置RecyclerView
        recyclerCards.setLayoutManager(new GridLayoutManager(this, 4));
        adapter = new MemoryCardAdapter(cards, this::onCardClick);
        recyclerCards.setAdapter(adapter);
        
        startTime = System.currentTimeMillis();
    }
    
    private void onCardClick(MemoryCard card) {
        if (isProcessing || card.isFlipped || card.isMatched) {
            return;
        }

        // 翻转卡片
        card.isFlipped = true;
        soundManager.playClickSound();
        adapter.notifyDataSetChanged();

        if (firstFlippedCard == null) {
            // 第一张卡片
            firstFlippedCard = card;
        } else if (secondFlippedCard == null) {
            // 第二张卡片
            secondFlippedCard = card;
            isProcessing = true;

            // 检查是否匹配
            timerHandler.postDelayed(() -> checkMatch(), 1000);
        }
    }

    private void checkMatch() {
        if (firstFlippedCard.matchId == secondFlippedCard.matchId) {
            // 匹配成功
            firstFlippedCard.isMatched = true;
            secondFlippedCard.isMatched = true;
            score += 10;
            matchedPairs++;

            soundManager.playCorrectSound();
            tvScore.setText("得分: " + score);
            updateProgress();

            if (matchedPairs == 9) {
                // 游戏完成
                gameCompleted();
            }
        } else {
            // 匹配失败，翻回去
            firstFlippedCard.isFlipped = false;
            secondFlippedCard.isFlipped = false;
            soundManager.playWrongSound();
        }

        // 重置状态
        firstFlippedCard = null;
        secondFlippedCard = null;
        isProcessing = false;
        adapter.notifyDataSetChanged();
    }
    
    private void startTimer() {
        timerRunnable = new Runnable() {
            @Override
            public void run() {
                long elapsed = System.currentTimeMillis() - startTime;
                int seconds = (int) (elapsed / 1000);
                int minutes = seconds / 60;
                seconds = seconds % 60;
                
                tvTimer.setText(String.format("时间: %02d:%02d", minutes, seconds));
                timerHandler.postDelayed(this, 1000);
            }
        };
        timerHandler.post(timerRunnable);
    }
    
    private void restartGame() {
        score = 0;
        matchedPairs = 0;
        firstFlippedCard = null;
        secondFlippedCard = null;
        isProcessing = false;
        tvScore.setText("得分: " + score);
        setupGame();
    }
    
    private void gameCompleted() {
        timerHandler.removeCallbacks(timerRunnable);
        
        long gameTime = System.currentTimeMillis() - startTime;
        
        // 返回结果
        Intent result = new Intent();
        result.putExtra("progress_gain", 15); // 记忆卡片游戏获得15%进度
        result.putExtra("game_time", gameTime);
        setResult(RESULT_OK, result);
        finish();
    }

    private void updateProgress() {
        tvProgress.setText("进度: " + matchedPairs + "/9 对");

        // 显示过关标准提示
        if (matchedPairs == 0) {
            tvProgress.append(" (找到9对相同的乘法算式即可过关)");
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (timerHandler != null) {
            timerHandler.removeCallbacks(timerRunnable);
        }
        if (voiceManager != null) {
            voiceManager.stop();
        }
        if (soundManager != null) {
            soundManager.stopBackgroundMusic();
        }
    }

}
