package com.example.concentration;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.material.button.MaterialButton;

public class CelebrationActivity extends AppCompatActivity {

    private TextView tvSubTitle;
    private TextView tvTimeResult;
    private TextView tvBestRecord;
    private MaterialButton btnPlayAgain;
    private MaterialButton btnBackToMain;
    private ViewGroup celebrationContainer;

    // 水晶奖励相关
    private View cardCrystalReward;
    private TextView tvCrystalEmoji;
    private TextView tvCrystalTitle;
    private TextView tvCrystalDescription;
    
    private String gameType;
    private long gameTime;
    private boolean isSuccess;
    private int gridSize;
    
    private GameDataManager gameDataManager;
    private SoundManager soundManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_celebration);
        
        // 初始化管理器
        gameDataManager = new GameDataManager(this);
        soundManager = SoundManager.getInstance(this);
        
        // 获取传递的数据
        Intent intent = getIntent();
        gameType = intent.getStringExtra("game_type");
        gameTime = intent.getLongExtra("time", 0);
        isSuccess = intent.getBooleanExtra("success", false) || intent.getBooleanExtra("is_success", false);
        gridSize = intent.getIntExtra("grid_size", 3);

        // 获取水晶奖励数据
        int crystalReward = intent.getIntExtra("crystal_reward", 0);
        String crystalDescription = intent.getStringExtra("crystal_description");
        boolean newCrystalGenerated = intent.getBooleanExtra("new_crystal_generated", false);
        
        initViews();
        setupContent();
        setupClickListeners();
        startCelebrationAnimation();
    }
    
    private void initViews() {
        tvSubTitle = findViewById(R.id.tv_sub_title);
        tvTimeResult = findViewById(R.id.tv_time_result);
        tvBestRecord = findViewById(R.id.tv_best_record);
        btnPlayAgain = findViewById(R.id.btn_play_again);
        btnBackToMain = findViewById(R.id.btn_back_to_main);
        celebrationContainer = (ViewGroup) findViewById(R.id.celebration_container);

        // 水晶奖励相关
        cardCrystalReward = findViewById(R.id.card_crystal_reward);
        tvCrystalEmoji = findViewById(R.id.tv_crystal_emoji);
        tvCrystalTitle = findViewById(R.id.tv_crystal_title);
        tvCrystalDescription = findViewById(R.id.tv_crystal_description);

        // 不再隐藏视图，直接显示所有内容
    }
    
    private void setupContent() {
        if (isSuccess) {
            // 成功页面
            tvSubTitle.setText("🌟 挑战成功！");
            
            if ("schulte".equals(gameType)) {
                // 舒尔特方格
                String timeText = formatTime(gameTime);
                tvTimeResult.setText("完成时间: " + timeText);

                long bestTime = gameDataManager.getBestTime(gridSize);
                if (bestTime > 0) {
                    String bestTimeText = formatTime(bestTime);
                    tvBestRecord.setText("最佳记录: " + bestTimeText);

                    // 检查是否创造新记录
                    if (gameTime <= bestTime) {
                        tvBestRecord.setText("🏆 新记录: " + timeText);
                        playNewRecordAnimation();
                    }
                } else {
                    tvBestRecord.setText("🏆 首次完成!");
                }

                // 显示水晶奖励
                setupCrystalReward();

            } else if ("memory_game".equals(gameType)) {
                // 记忆游戏
                String timeText = formatTime(gameTime);
                tvTimeResult.setText("完成时间: " + timeText);
                tvBestRecord.setText("游戏完成!");
            } else if ("color_training".equals(gameType)) {
                // 颜色识别训练 - 总是显示成功
                tvSubTitle.setText("🎉 训练完成");

                String timeText = formatTime(gameTime);
                tvTimeResult.setText("完成时间: " + timeText);

                // 显示最佳记录
                SharedPreferences prefs = getSharedPreferences("color_training_stats", MODE_PRIVATE);
                long bestTime = prefs.getLong("best_completion_time", Long.MAX_VALUE);

                if (bestTime != Long.MAX_VALUE) {
                    String bestTimeText = formatTime(bestTime);
                    tvBestRecord.setText("🏆 最佳记录: " + bestTimeText);
                } else {
                    tvBestRecord.setText("🏆 首次完成!");
                }

                // 显示水晶奖励
                setupCrystalReward();
            }

        } else if (!"color_training".equals(gameType)) {
            // 失败页面 - 但不包括颜色训练
            tvSubTitle.setText("😔 挑战失败");
            tvTimeResult.setText("再接再厉！");
            tvBestRecord.setText("继续努力，你一定可以的！");
        }
    }
    
    private void setupClickListeners() {
        btnPlayAgain.setOnClickListener(v -> {
            soundManager.playClickSound();
            playAgain();
        });
        
        btnBackToMain.setOnClickListener(v -> {
            soundManager.playClickSound();
            backToMain();
        });
    }
    
    private void startCelebrationAnimation() {
        if (isSuccess) {
            // 只播放音效，不要烟花动画
            soundManager.playCompleteSound();
        }
        // 失败时也不需要动画
    }
    
    private void playSuccessAnimation() {
        // 副标题放大动画
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(tvSubTitle, "scaleX", 0f, 1.5f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(tvSubTitle, "scaleY", 0f, 1.5f, 1f);
        ObjectAnimator rotation = ObjectAnimator.ofFloat(tvSubTitle, "rotation", 0f, 360f);

        AnimatorSet titleAnimation = new AnimatorSet();
        titleAnimation.playTogether(scaleX, scaleY, rotation);
        titleAnimation.setDuration(1000);
        titleAnimation.setInterpolator(new AccelerateDecelerateInterpolator());
        titleAnimation.start();
        
        // 延迟显示其他元素
        new Handler().postDelayed(() -> {
            animateViewIn(tvSubTitle, 200);
            animateViewIn(tvTimeResult, 400);
            animateViewIn(tvBestRecord, 600);
            animateViewIn(btnPlayAgain, 800);
            animateViewIn(btnBackToMain, 1000);

            // 不播放烟花效果
        }, 500);
    }
    
    private void playFailureAnimation() {
        // 失败动画 - 摇摆效果
        ObjectAnimator shake = ObjectAnimator.ofFloat(tvSubTitle, "translationX", 0, 25, -25, 25, -25, 15, -15, 6, -6, 0);
        shake.setDuration(1000);
        shake.start();
        
        // 延迟显示其他元素
        new Handler().postDelayed(() -> {
            animateViewIn(tvSubTitle, 200);
            animateViewIn(tvTimeResult, 400);
            animateViewIn(tvBestRecord, 600);
            animateViewIn(btnPlayAgain, 800);
            animateViewIn(btnBackToMain, 1000);
        }, 500);
    }
    
    private void playNewRecordAnimation() {
        // 新记录特效 - 闪烁金色效果
        ValueAnimator colorAnimator = ValueAnimator.ofArgb(
            getColor(R.color.md_theme_light_onSurface),
            getColor(R.color.md_theme_light_primary),
            getColor(R.color.md_theme_light_onSurface)
        );
        colorAnimator.setDuration(500);
        colorAnimator.setRepeatCount(3);
        colorAnimator.addUpdateListener(animation -> {
            tvBestRecord.setTextColor((Integer) animation.getAnimatedValue());
        });
        colorAnimator.start();
        
        // 放大效果
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(tvBestRecord, "scaleX", 1f, 1.2f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(tvBestRecord, "scaleY", 1f, 1.2f, 1f);
        scaleX.setDuration(500);
        scaleX.setRepeatCount(3);
        scaleY.setDuration(500);
        scaleY.setRepeatCount(3);

        AnimatorSet scaleAnimation = new AnimatorSet();
        scaleAnimation.playTogether(scaleX, scaleY);
        scaleAnimation.start();
    }
    
    private void animateViewIn(View view, long delay) {
        view.setAlpha(0f);
        view.setTranslationY(50f);

        view.animate()
            .alpha(1f)
            .translationY(0f)
            .setDuration(300)
            .setStartDelay(delay)
            .setInterpolator(new AccelerateDecelerateInterpolator())
            .start();
    }


    
    private void playAgain() {
        if ("schulte".equals(gameType)) {
            Intent intent = new Intent(this, CountdownActivity.class);
            intent.putExtra("grid_size", gridSize);
            startActivity(intent);
        } else if ("color_training".equals(gameType)) {
            Intent intent = new Intent(this, ColorTrainingActivity.class);
            startActivity(intent);
        } else if ("memory_game".equals(gameType)) {
            Intent intent = new Intent(this, MemoryGameMainActivity.class);
            startActivity(intent);
        }
        finish();
    }
    
    private void backToMain() {
        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        startActivity(intent);
        finish();
    }

    private String formatTime(long timeInMillis) {
        int minutes = (int) (timeInMillis / 60000);
        int seconds = (int) ((timeInMillis % 60000) / 1000);
        int millis = (int) (timeInMillis % 1000) / 10; // 显示两位毫秒

        return String.format("%02d:%02d.%02d", minutes, seconds, millis);
    }

    private void setupCrystalReward() {
        Intent intent = getIntent();
        int crystalReward = intent.getIntExtra("crystal_reward", 0);
        String crystalDescription = intent.getStringExtra("crystal_description");
        boolean newCrystalGenerated = intent.getBooleanExtra("new_crystal_generated", false);

        if (crystalReward > 0) {
            // 显示水晶奖励卡片
            cardCrystalReward.setVisibility(View.VISIBLE);

            // 设置水晶emoji（根据游戏类型）
            if ("schulte".equals(gameType)) {
                tvCrystalEmoji.setText("🔷");
            } else if ("memory_game".equals(gameType)) {
                tvCrystalEmoji.setText("🔮");
            } else if ("color_training".equals(gameType)) {
                tvCrystalEmoji.setText("💎");
            }

            // 设置奖励文本
            if (newCrystalGenerated) {
                tvCrystalTitle.setText("🎉 获得完整水晶！");
                tvCrystalDescription.setText("恭喜！碎片已合成完整水晶");
            } else {
                tvCrystalTitle.setText("获得水晶碎片！");
                tvCrystalDescription.setText("+" + crystalReward + "个" + (crystalDescription != null ? crystalDescription : "水晶碎片"));
            }
        } else {
            cardCrystalReward.setVisibility(View.GONE);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (soundManager != null) {
            soundManager.stopBackgroundMusic();
        }
    }
}
