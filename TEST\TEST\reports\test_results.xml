<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="ConcentrationApp" tests="15">
    <testcase classname="MainActivityTest" name="testMainLayoutLoading"/>
    <testcase classname="MainActivityTest" name="testGameCardClicks"/>
    <testcase classname="GameUITest" name="testSchulteGridUI"/>
    <testcase classname="GameUITest" name="testMemoryGameUI"/>
    <testcase classname="SettingsUITest" name="testSettingsLayout"/>
    <testcase classname="ScoreCalculationTest" name="testSchulteScoring"/>
    <testcase classname="ProgressSaveTest" name="testProgressPersistence"/>
    <testcase classname="CrystalSystemTest" name="testCrystalRewards"/>
    <testcase classname="GameRulesTest" name="testMultiplicationRules"/>
    <testcase classname="GameFlowTest" name="testCompleteGameFlow"/>
    <testcase classname="NavigationTest" name="testAppNavigation"/>
    <testcase classname="TutorialTest" name="testTutorialSystem"/>
    <testcase classname="InvalidInputTest" name="testInvalidGameInputs"/>
    <testcase classname="BoundaryTest" name="testBoundaryConditions"/>
    <testcase classname="ExceptionHandlingTest" name="testExceptionHandling"/>
  </testsuite>
</testsuites>
