<?xml version='1.0' encoding='UTF-8' standalone='yes' ?><hierarchy rotation="0"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1344,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1344,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,140][1344,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="com.example.concentration:id/action_bar_root" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,140][1344,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,140][1344,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.ScrollView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" bounds="[0,140][1344,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,140][1344,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[56,196][1288,364]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="com.example.concentration:id/btn_back" class="android.widget.ImageButton" package="com.example.concentration" content-desc="返回" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[56,196][224,364]" xpos="[0,0][0,0]" /><node index="1" text="&#127912; 颜色识别训练" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[224,224][1120,336]" xpos="[0,0][0,0]" /><node index="2" text="&#128161;" resource-id="com.example.concentration:id/btn_tutorial" class="android.widget.Button" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[1120,196][1288,364]" xpos="[0,0][0,0]" /></node><node index="1" text="" resource-id="" class="androidx.cardview.widget.CardView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[56,448][1288,697]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[56,448][1288,697]" xpos="[0,0][0,0]"><node index="0" text="&#128142;" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[112,516][217,628]" xpos="[0,0][0,0]" /><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[259,504][952,641]" xpos="[0,0][0,0]"><node index="0" text="颜色水晶" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[259,504][455,570]" xpos="[0,0][0,0]" /><node index="1" text="&#128142; 0个 + 0/30碎片" resource-id="com.example.concentration:id/tv_crystal_progress" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[259,584][607,641]" xpos="[0,0][0,0]" /></node><node index="2" text="完成训练获得碎片" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[952,549][1232,596]" xpos="[0,0][0,0]" /></node></node><node index="2" text="" resource-id="" class="androidx.cardview.widget.CardView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[56,753][1288,1203]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[56,753][1288,1203]" xpos="[0,0][0,0]"><node index="0" text="&#128202; 训练统计" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[126,823][474,908]" xpos="[0,0][0,0]" /><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[126,964][1218,1133]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[126,964][490,1133]" xpos="[0,0][0,0]"><node index="0" text="0%" resource-id="com.example.concentration:id/tv_best_score" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[247,964][368,1076]" xpos="[0,0][0,0]" /><node index="1" text="最佳准确率" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[203,1076][413,1133]" xpos="[0,0][0,0]" /></node><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[490,964][854,1133]" xpos="[0,0][0,0]"><node index="0" text="0.0s" resource-id="com.example.concentration:id/tv_avg_time" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[591,964][752,1076]" xpos="[0,0][0,0]" /><node index="1" text="平均反应时间" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[546,1076][798,1133]" xpos="[0,0][0,0]" /></node><node index="2" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[854,964][1218,1133]" xpos="[0,0][0,0]"><node index="0" text="0" resource-id="com.example.concentration:id/tv_total_games" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[1011,964][1061,1076]" xpos="[0,0][0,0]" /><node index="1" text="总训练次数" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[931,1076][1141,1133]" xpos="[0,0][0,0]" /></node></node></node></node><node index="3" text="" resource-id="" class="androidx.cardview.widget.CardView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[56,1287][1288,1764]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[56,1287][1288,1764]" xpos="[0,0][0,0]"><node index="0" text="&#127919; 选择难度" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[126,1357][474,1442]" xpos="[0,0][0,0]" /><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[126,1498][1218,1694]" xpos="[0,0][0,0]"><node index="0" text="简单&#10;3X3" resource-id="com.example.concentration:id/btn_easy" class="android.widget.Button" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[126,1498][462,1694]" xpos="[0,0][0,0]" /><node index="1" text="中等&#10;4X4" resource-id="com.example.concentration:id/btn_medium" class="android.widget.Button" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[504,1498][840,1694]" xpos="[0,0][0,0]" /><node index="2" text="困难&#10;5X5" resource-id="com.example.concentration:id/btn_hard" class="android.widget.Button" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[882,1498][1218,1694]" xpos="[0,0][0,0]" /></node></node></node><node index="4" text="" resource-id="" class="androidx.cardview.widget.CardView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[56,1848][1288,2619]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[56,1848][1288,2619]" xpos="[0,0][0,0]"><node index="0" text="&#127918; 游戏模式" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[126,1918][474,2003]" xpos="[0,0][0,0]" /><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[126,2059][1218,2255]" xpos="[0,0][0,0]"><node index="0" text="&#127912;&#10;匹配" resource-id="com.example.concentration:id/btn_color_match" class="android.widget.Button" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[126,2059][471,2255]" xpos="[0,0][0,0]" /><node index="1" text="&#128221;&#10;命名" resource-id="com.example.concentration:id/btn_color_name" class="android.widget.Button" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[499,2059][844,2255]" xpos="[0,0][0,0]" /><node index="2" text="⚡&#10;干扰" resource-id="com.example.concentration:id/btn_interference" class="android.widget.Button" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[872,2059][1218,2255]" xpos="[0,0][0,0]" /></node><node index="2" text="&#128640; 开始训练" resource-id="com.example.concentration:id/btn_start" class="android.widget.Button" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[126,2325][1218,2549]" xpos="[0,0][0,0]" /></node></node><node index="5" text="" resource-id="" class="androidx.cardview.widget.CardView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][0,0]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][0,0]" xpos="[0,0][0,0]" /></node></node></node></node></node></node></node><node index="1" text="" resource-id="android:id/statusBarBackground" class="android.view.View" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1344,140]" xpos="[0,0][0,0]" /></node></hierarchy>