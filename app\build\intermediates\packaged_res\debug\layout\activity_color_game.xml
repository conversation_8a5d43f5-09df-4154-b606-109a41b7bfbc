<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/light_gradient_background"
    android:padding="16dp">

    <!-- 顶部状态栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <!-- 计时器 -->
        <TextView
            android:id="@+id/tv_timer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="⏱️ 00.000"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_primary"
            android:background="@drawable/timer_background_light"
            android:padding="12dp" />

        <!-- 目标时间 -->
        <TextView
            android:id="@+id/tv_target_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🎯 目标: 30秒"
            android:textSize="14sp"
            android:textColor="@color/md_theme_light_secondary"
            android:layout_marginStart="8dp"
            android:padding="8dp" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <!-- 分数 -->
        <TextView
            android:id="@+id/tv_score"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🏆 0/10"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_primary"
            android:background="@drawable/timer_background_light"
            android:padding="8dp"
            android:layout_marginEnd="8dp" />



    </LinearLayout>

    <!-- 目标颜色区域 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_instruction"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🎯 找到这个颜色"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_primary"
                android:layout_marginBottom="16dp" />

            <!-- 目标颜色显示 -->
            <FrameLayout
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginBottom="12dp">

                <!-- 目标颜色块 -->
                <View
                    android:id="@+id/view_target_color"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/target_background" />

                <!-- 目标形状 (用于形状模式) -->
                <TextView
                    android:id="@+id/tv_target_shape"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="●"
                    android:textSize="80sp"
                    android:gravity="center"
                    android:visibility="gone" />

            </FrameLayout>

            <!-- 目标颜色名称 -->
            <TextView
                android:id="@+id/tv_target_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="红色"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onSurface" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 颜色选择网格 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🎨 选择颜色"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_primary"
                android:layout_marginBottom="16dp" />

            <!-- 颜色网格容器 -->
            <GridLayout
                android:id="@+id/grid_colors"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:columnCount="3"
                android:rowCount="3"
                android:layout_gravity="center" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</LinearLayout>
