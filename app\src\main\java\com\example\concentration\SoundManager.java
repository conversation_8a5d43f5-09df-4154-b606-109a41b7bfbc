package com.example.concentration;

import android.content.Context;
import android.media.AudioAttributes;
import android.media.SoundPool;
import android.media.MediaPlayer;
import android.media.ToneGenerator;
import android.media.AudioManager;
import android.content.res.AssetFileDescriptor;
import java.io.IOException;

public class SoundManager {
    private static SoundManager instance;
    private SoundPool soundPool;
    private MediaPlayer backgroundPlayer;
    private Context context;
    private ToneGenerator toneGenerator;

    // 音效ID
    private int clickSoundId = -1;
    private int correctSoundId = -1;
    private int wrongSoundId = -1;
    private int completeSoundId = -1;
    
    private boolean soundEnabled = true;
    private boolean musicEnabled = true;
    
    private SoundManager(Context context) {
        this.context = context.getApplicationContext();
        initializeSoundPool();
        initializeToneGenerator();
    }
    
    public static SoundManager getInstance(Context context) {
        if (instance == null) {
            instance = new SoundManager(context);
        }
        return instance;
    }
    
    private void initializeSoundPool() {
        AudioAttributes audioAttributes = new AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_GAME)
            .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
            .build();
            
        soundPool = new SoundPool.Builder()
            .setMaxStreams(5)
            .setAudioAttributes(audioAttributes)
            .build();
            
        // 加载音效文件（这里使用系统音效作为占位符）
        loadSounds();
    }
    
    private void initializeToneGenerator() {
        try {
            toneGenerator = new ToneGenerator(AudioManager.STREAM_MUSIC, 80);
        } catch (RuntimeException e) {
            e.printStackTrace();
            toneGenerator = null;
        }
    }

    private void loadSounds() {
        // 暂时使用ToneGenerator生成音效，后续可以添加音效文件
        // 这里设置音效ID为正数，表示可以使用ToneGenerator
        clickSoundId = 1;
        correctSoundId = 2;
        wrongSoundId = 3;
        completeSoundId = 4;
    }
    
    public void playClickSound() {
        if (soundEnabled && toneGenerator != null) {
            toneGenerator.startTone(ToneGenerator.TONE_PROP_BEEP, 100);
        }
    }

    public void playCorrectSound() {
        if (soundEnabled && toneGenerator != null) {
            toneGenerator.startTone(ToneGenerator.TONE_PROP_ACK, 200);
        }
    }

    public void playWrongSound() {
        if (soundEnabled && toneGenerator != null) {
            toneGenerator.startTone(ToneGenerator.TONE_PROP_NACK, 300);
        }
    }

    public void playCompleteSound() {
        if (soundEnabled && toneGenerator != null) {
            // 播放一个愉快的完成音效
            toneGenerator.startTone(ToneGenerator.TONE_CDMA_ALERT_CALL_GUARD, 500);
        }
    }
    
    public void startBackgroundMusic() {
        if (!musicEnabled) return;
        
        // 这里也需要实际的背景音乐文件
        /*
        try {
            if (backgroundPlayer == null) {
                AssetFileDescriptor afd = context.getAssets().openFd("music/background.mp3");
                backgroundPlayer = new MediaPlayer();
                backgroundPlayer.setDataSource(afd.getFileDescriptor(), afd.getStartOffset(), afd.getLength());
                backgroundPlayer.setLooping(true);
                backgroundPlayer.setVolume(0.3f, 0.3f);
                backgroundPlayer.prepare();
            }
            if (!backgroundPlayer.isPlaying()) {
                backgroundPlayer.start();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        */
    }
    
    public void stopBackgroundMusic() {
        if (backgroundPlayer != null && backgroundPlayer.isPlaying()) {
            backgroundPlayer.pause();
        }
    }
    
    public void setSoundEnabled(boolean enabled) {
        this.soundEnabled = enabled;
    }
    
    public void setMusicEnabled(boolean enabled) {
        this.musicEnabled = enabled;
        if (!enabled) {
            stopBackgroundMusic();
        } else {
            startBackgroundMusic();
        }
    }
    
    public void release() {
        if (soundPool != null) {
            soundPool.release();
            soundPool = null;
        }
        if (backgroundPlayer != null) {
            backgroundPlayer.release();
            backgroundPlayer = null;
        }
        if (toneGenerator != null) {
            toneGenerator.release();
            toneGenerator = null;
        }
    }
}
