<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:gravity="center_vertical"
    android:background="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_game_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="游戏类型"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_onSurface" />

        <TextView
            android:id="@+id/tv_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="游戏结果"
            android:textSize="14sp"
            android:textColor="@color/md_theme_light_onSurfaceVariant"
            android:layout_marginTop="2dp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="end">

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="用时"
            android:textSize="14sp"
            android:textColor="@color/md_theme_light_primary"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_timestamp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="时间戳"
            android:textSize="12sp"
            android:textColor="@color/md_theme_light_onSurfaceVariant"
            android:layout_marginTop="2dp" />

    </LinearLayout>

</LinearLayout>
