<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:fillViewport="true">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 顶部功能栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:paddingHorizontal="16dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/btn_rewards"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🏆"
                android:textSize="28sp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="8dp"
                android:contentDescription="奖励中心"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/tv_username"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onPrimary"
                android:maxLength="5"
                android:ellipsize="end"
                android:singleLine="true"
                android:layout_marginEnd="12dp"
                android:visibility="gone" />

        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/btn_settings"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="⚙️"
            android:textSize="28sp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="8dp"
            android:contentDescription="设置" />

    </LinearLayout>

    <!-- APP标题 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="24dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🧠 疯狂大脑"
            android:textSize="36sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_onPrimary"
            android:gravity="center" />

    </LinearLayout>

    <!-- 副标题区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginBottom="40dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🧠 疯狂大脑"
            android:textSize="36sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_onPrimary"
            android:visibility="gone"
            android:layout_marginBottom="12dp"
            android:shadowColor="@color/black_overlay"
            android:shadowDx="2"
            android:shadowDy="2"
            android:shadowRadius="4" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="✨ 神经元拉拉小手，冲啊！ ✨"
            android:textSize="18sp"
            android:textColor="@color/md_theme_light_onPrimary"
            android:shadowColor="@color/black_overlay"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="2" />


    </LinearLayout>

    <!-- 现代化训练模块卡片 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 舒尔特方格现代化卡片 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_schulte"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp"
            android:foreground="?android:attr/selectableItemBackground">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/gradient_background_secondary"
                android:padding="24dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🎯"
                        android:textSize="32sp"
                        android:layout_marginEnd="12dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="舒尔特方格"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onPrimary"
                            android:shadowColor="@color/black_overlay"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="2" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="提升注意力集中度和视觉搜索能力"
                            android:textSize="16sp"
                            android:textColor="@color/md_theme_light_onPrimary"
                            android:alpha="0.9"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>
                </LinearLayout>


            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 数字序列记忆卡片 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_sequence_memory"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="4dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@color/md_theme_light_surface"
                android:padding="24dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🧮"
                        android:textSize="32sp"
                        android:layout_marginEnd="12dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="数字序列记忆"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onSurface" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="训练短期记忆和数字处理能力"
                            android:textSize="16sp"
                            android:textColor="@color/md_theme_light_onSurfaceVariant"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="开始训练"
                        android:textSize="16sp"
                        android:textColor="@color/md_theme_light_onPrimary"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 颜色识别训练卡片 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_color_training"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="4dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@color/md_theme_light_surface"
                android:padding="24dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🌈"
                        android:textSize="32sp"
                        android:layout_marginEnd="12dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="颜色识别训练"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onSurface" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="提升视觉处理和反应速度"
                            android:textSize="16sp"
                            android:textColor="@color/md_theme_light_onSurfaceVariant"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>
                </LinearLayout>

                <!-- 开始按钮 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="开始训练"
                        android:textSize="16sp"
                        android:textColor="@color/md_theme_light_onPrimary"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 九九乘法表学习卡片 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_multiplication_table"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="4dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/gradient_multiplication_bg"
                android:padding="24dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="✖️"
                        android:textSize="32sp"
                        android:layout_marginEnd="12dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="九九乘法表"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onPrimary"
                            android:shadowColor="@color/black_overlay"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="2" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="从零开始学习乘法，寓教于乐"
                            android:textSize="16sp"
                            android:textColor="@color/md_theme_light_onPrimary"
                            android:alpha="0.9"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>
                </LinearLayout>

                <!-- 开始按钮 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="开始学习"
                        android:textSize="16sp"
                        android:textColor="@color/md_theme_light_onPrimary"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>

    <!-- 现代化底部统计信息 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        app:cardCornerRadius="20dp"
        app:cardElevation="6dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@color/md_theme_light_surface"
            android:padding="20dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📅"
                    android:textSize="20sp"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="今日训练"
                    android:textSize="14sp"
                    android:textColor="@color/md_theme_light_onSurfaceVariant" />

                <TextView
                    android:id="@+id/tv_today_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0次"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_primary" />
            </LinearLayout>

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/md_theme_light_outline"
                android:layout_marginHorizontal="16dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🔥"
                    android:textSize="20sp"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="连续天数"
                    android:textSize="14sp"
                    android:textColor="@color/md_theme_light_onSurfaceVariant" />

                <TextView
                    android:id="@+id/tv_streak_days"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0天"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_primary" />
            </LinearLayout>

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/md_theme_light_outline"
                android:layout_marginHorizontal="16dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🏆"
                    android:textSize="20sp"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="总训练"
                    android:textSize="14sp"
                    android:textColor="@color/md_theme_light_onSurfaceVariant" />

                <TextView
                    android:id="@+id/tv_total_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0次"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_primary" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</LinearLayout>
</ScrollView>
