package com.example.concentration;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import com.google.android.material.button.MaterialButton;

public class MultiplicationConceptActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private CardView cardVisualDemo, cardInteractiveCount, cardAnimationDemo;
    private TextView tvProgress;
    private MaterialButton btnStartLearning;
    
    private SharedPreferences prefs;
    private int completedLessons = 0;
    private final int totalLessons = 3;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_multiplication_concept);
        
        initializeManagers();
        initViews();
        setupClickListeners();
        loadProgress();
    }
    
    private void initializeManagers() {
        prefs = getSharedPreferences("multiplication_table_prefs", MODE_PRIVATE);
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        
        // 三个学习模块卡片
        cardVisualDemo = findViewById(R.id.card_visual_demo);
        cardInteractiveCount = findViewById(R.id.card_interactive_count);
        cardAnimationDemo = findViewById(R.id.card_animation_demo);
        
        // 进度显示
        tvProgress = findViewById(R.id.tv_progress);
        
        // 开始学习按钮
        btnStartLearning = findViewById(R.id.btn_start_learning);
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        
        cardVisualDemo.setOnClickListener(v -> startVisualDemo());
        cardInteractiveCount.setOnClickListener(v -> startInteractiveCount());
        cardAnimationDemo.setOnClickListener(v -> startAnimationDemo());
        
        btnStartLearning.setOnClickListener(v -> startConceptGame());
    }
    
    private void loadProgress() {
        completedLessons = prefs.getInt("concept_completed_lessons", 0);
        updateProgress();
    }
    
    private void updateProgress() {
        int progressPercent = (completedLessons * 100) / totalLessons;
        tvProgress.setText("学习进度: " + completedLessons + "/" + totalLessons + " (" + progressPercent + "%)");
        
        // 更新卡片状态
        updateCardStatus(cardVisualDemo, 0);
        updateCardStatus(cardInteractiveCount, 1);
        updateCardStatus(cardAnimationDemo, 2);
        
        // 更新开始学习按钮
        if (completedLessons >= totalLessons) {
            btnStartLearning.setText("🎮 开始概念测试");
            btnStartLearning.setEnabled(true);
        } else {
            btnStartLearning.setText("完成所有课程后解锁");
            btnStartLearning.setEnabled(false);
        }
    }
    
    private void updateCardStatus(CardView card, int lessonIndex) {
        // 检查测试模式
        boolean testMode = SettingsActivity.isTestModeEnabled(this);

        if (lessonIndex < completedLessons || testMode) {
            // 已完成或测试模式
            card.setAlpha(1.0f);
            card.setCardBackgroundColor(getColor(R.color.md_theme_light_primaryContainer));
        } else if (lessonIndex == completedLessons) {
            // 当前可学习
            card.setAlpha(1.0f);
            card.setCardBackgroundColor(getColor(R.color.md_theme_light_surface));
        } else {
            // 未解锁
            card.setAlpha(0.5f);
            card.setCardBackgroundColor(getColor(R.color.md_theme_light_surfaceVariant));
        }
    }
    
    private void startVisualDemo() {
        // 第一课总是可以开始
        Intent intent = new Intent(this, ConceptVisualDemoActivity.class);
        startActivityForResult(intent, 1001);
    }

    private void startInteractiveCount() {
        // 需要完成第一课才能开始第二课，或测试模式开启
        boolean testMode = SettingsActivity.isTestModeEnabled(this);
        if (completedLessons >= 1 || testMode) {
            Intent intent = new Intent(this, ConceptInteractiveCountActivity.class);
            startActivityForResult(intent, 1002);
        } else {
            // 提示需要先完成前面的课程
            android.widget.Toast.makeText(this, "请先完成第一课：视觉演示", android.widget.Toast.LENGTH_SHORT).show();
        }
    }

    private void startAnimationDemo() {
        // 需要完成前两课才能开始第三课，或测试模式开启
        boolean testMode = SettingsActivity.isTestModeEnabled(this);
        if (completedLessons >= 2 || testMode) {
            Intent intent = new Intent(this, ConceptAnimationDemoActivity.class);
            startActivityForResult(intent, 1003);
        } else {
            // 提示需要先完成前面的课程
            android.widget.Toast.makeText(this, "请先完成前面的课程", android.widget.Toast.LENGTH_SHORT).show();
        }
    }

    private void startConceptGame() {
        if (completedLessons >= totalLessons) {
            Intent intent = new Intent(this, ConceptTestGameActivity.class);
            startActivity(intent);
        }
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case 1001: // 视觉演示完成
                    if (completedLessons <= 0) {
                        completedLessons = 1;
                        saveProgress();
                    }
                    break;
                case 1002: // 互动计数完成
                    if (completedLessons <= 1) {
                        completedLessons = 2;
                        saveProgress();
                    }
                    break;
                case 1003: // 动画演示完成
                    if (completedLessons <= 2) {
                        completedLessons = 3;
                        saveProgress();
                    }
                    break;
            }
            updateProgress();
        }
    }
    
    private void saveProgress() {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt("concept_completed_lessons", completedLessons);

        // 更新阶段1总进度
        int stage1Progress = (completedLessons * 100) / totalLessons;
        editor.putInt("stage1_progress", stage1Progress);

        // 更新总学习会话数
        int totalSessions = prefs.getInt("total_sessions", 0);
        editor.putInt("total_sessions", totalSessions + 1);

        // 奖励水晶碎片
        CrystalManager crystalManager = CrystalManager.getInstance(this);
        crystalManager.addCrystalFragments(CrystalManager.CrystalType.MULTIPLICATION, 10);

        editor.apply();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        loadProgress();
    }
}
