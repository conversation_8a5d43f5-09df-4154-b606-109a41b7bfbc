package com.example.concentration;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import com.google.android.material.button.MaterialButton;

public class MultiplicationConceptActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private CardView cardVisualDemo, cardInteractiveCount;
    private TextView tvProgress;
    private MaterialButton btnStartLearning;

    private SharedPreferences prefs;
    private int completedLessons = 0;
    private final int totalLessons = 2; // 合并后只有2课
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_multiplication_concept);
        
        initializeManagers();
        initViews();
        setupClickListeners();
        loadProgress();
    }
    
    private void initializeManagers() {
        prefs = getSharedPreferences("multiplication_table_prefs", MODE_PRIVATE);
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);

        // 两个学习模块卡片（合并后）
        cardVisualDemo = findViewById(R.id.card_visual_demo);
        cardInteractiveCount = findViewById(R.id.card_interactive_count);

        // 进度显示
        tvProgress = findViewById(R.id.tv_progress);

        // 开始学习按钮
        btnStartLearning = findViewById(R.id.btn_start_learning);
    }

    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());

        cardVisualDemo.setOnClickListener(v -> startVisualDemo());
        cardInteractiveCount.setOnClickListener(v -> startInteractiveCount());

        btnStartLearning.setOnClickListener(v -> startConceptGame());
    }
    
    private void loadProgress() {
        // 检查各个课程的完成状态（合并后只有2课）
        boolean visualDemoCompleted = prefs.getBoolean("visual_demo_completed", false);
        boolean interactiveCountCompleted = prefs.getBoolean("interactive_count_completed", false);

        // 根据完成状态计算已完成课程数
        completedLessons = 0;
        if (visualDemoCompleted) completedLessons++;
        if (interactiveCountCompleted) completedLessons++;

        // 保存计算出的进度
        prefs.edit().putInt("concept_completed_lessons", completedLessons).apply();

        updateProgress();
    }
    
    private void updateProgress() {
        int progressPercent = (completedLessons * 100) / totalLessons;
        tvProgress.setText("学习进度: " + completedLessons + "/" + totalLessons + " (" + progressPercent + "%)");
        
        // 更新卡片状态（合并后只有2个卡片）
        updateCardStatus(cardVisualDemo, 0);
        updateCardStatus(cardInteractiveCount, 1);
        
        // 更新开始学习按钮
        if (completedLessons >= totalLessons) {
            btnStartLearning.setText("🎮 开始概念测试");
            btnStartLearning.setEnabled(true);
        } else {
            btnStartLearning.setText("完成所有课程后解锁");
            btnStartLearning.setEnabled(false);
        }
    }
    
    private void updateCardStatus(CardView card, int lessonIndex) {
        // 检查测试模式
        boolean testMode = SettingsActivity.isTestModeEnabled(this);

        if (lessonIndex < completedLessons || testMode) {
            // 已完成或测试模式
            card.setAlpha(1.0f);
            card.setCardBackgroundColor(getColor(R.color.md_theme_light_primaryContainer));
        } else if (lessonIndex == completedLessons) {
            // 当前可学习
            card.setAlpha(1.0f);
            card.setCardBackgroundColor(getColor(R.color.md_theme_light_surface));
        } else {
            // 未解锁
            card.setAlpha(0.5f);
            card.setCardBackgroundColor(getColor(R.color.md_theme_light_surfaceVariant));
        }
    }
    
    private void startVisualDemo() {
        // 第一课总是可以开始
        Intent intent = new Intent(this, ConceptVisualDemoActivity.class);
        startActivityForResult(intent, 1001);
    }

    private void startInteractiveCount() {
        // 需要完成第一课才能开始第二课，或测试模式开启
        boolean testMode = SettingsActivity.isTestModeEnabled(this);
        if (completedLessons >= 1 || testMode) {
            Intent intent = new Intent(this, ConceptInteractiveCountActivity.class);
            startActivityForResult(intent, 1002);
        } else {
            // 提示需要先完成前面的课程
            android.widget.Toast.makeText(this, "请先完成第一课：视觉演示", android.widget.Toast.LENGTH_SHORT).show();
        }
    }

    // startAnimationDemo方法已移除，因为课程3已合并到课程1

    private void startConceptGame() {
        if (completedLessons >= totalLessons) {
            Intent intent = new Intent(this, ConceptTestGameActivity.class);
            startActivity(intent);
        }
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case 1001: // 视觉演示完成
                    if (completedLessons <= 0) {
                        completedLessons = 1;
                        saveProgress();
                    }
                    break;
                case 1002: // 互动计数完成
                    if (completedLessons <= 1) {
                        completedLessons = 2;
                        saveProgress();
                    }
                    break;
                // 课程3已合并到课程1，不再需要单独处理
            }
            updateProgress();
        }
    }
    
    private void saveProgress() {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt("concept_completed_lessons", completedLessons);

        // 更新阶段1总进度
        int stage1Progress = (completedLessons * 100) / totalLessons;
        editor.putInt("stage1_progress", stage1Progress);

        // 更新总学习会话数
        int totalSessions = prefs.getInt("total_sessions", 0);
        editor.putInt("total_sessions", totalSessions + 1);

        // 奖励水晶碎片
        CrystalManager crystalManager = CrystalManager.getInstance(this);
        crystalManager.addCrystalFragments(CrystalManager.CrystalType.MULTIPLICATION, 10);

        editor.apply();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        loadProgress();
    }
}
