{"logs": [{"outputFile": "com.example.concentration.app-mergeDebugResources-48:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\38fdf4e56534b3c4c6a8297bf7eeddee\\transformed\\appcompat-1.6.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,415,513,623,709,811,932,1010,1087,1178,1271,1366,1460,1560,1653,1748,1842,1933,2024,2105,2210,2312,2410,2520,2623,2732,2890,8523", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "410,508,618,704,806,927,1005,1082,1173,1266,1361,1455,1555,1648,1743,1837,1928,2019,2100,2205,2307,2405,2515,2618,2727,2885,2986,8600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cf89ab1ea7cc19352d4f47103c74320d\\transformed\\core-1.9.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "8605", "endColumns": "100", "endOffsets": "8701"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ecca92267eba45ce3e43e495f0e1965d\\transformed\\material-1.10.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,413,494,601,697,804,936,1019,1084,1178,1247,1306,1391,1454,1517,1575,1640,1701,1762,1868,1926,1986,2045,2115,2231,2310,2390,2524,2599,2675,2812,2909,3007,3064,3119,3185,3255,3332,3418,3503,3571,3647,3728,3806,3907,3993,4080,4177,4276,4350,4420,4524,4578,4665,4732,4822,4914,4976,5040,5103,5169,5274,5384,5485,5592,5653,5712", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,80,106,95,106,131,82,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,79,133,74,75,136,96,97,56,54,65,69,76,85,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78", "endOffsets": "254,332,408,489,596,692,799,931,1014,1079,1173,1242,1301,1386,1449,1512,1570,1635,1696,1757,1863,1921,1981,2040,2110,2226,2305,2385,2519,2594,2670,2807,2904,3002,3059,3114,3180,3250,3327,3413,3498,3566,3642,3723,3801,3902,3988,4075,4172,4271,4345,4415,4519,4573,4660,4727,4817,4909,4971,5035,5098,5164,5269,5379,5480,5587,5648,5707,5786"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2991,3069,3145,3226,3333,3429,3536,3668,3751,3816,3910,3979,4038,4123,4186,4249,4307,4372,4433,4494,4600,4658,4718,4777,4847,4963,5042,5122,5256,5331,5407,5544,5641,5739,5796,5851,5917,5987,6064,6150,6235,6303,6379,6460,6538,6639,6725,6812,6909,7008,7082,7152,7256,7310,7397,7464,7554,7646,7708,7772,7835,7901,8006,8116,8217,8324,8385,8444", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "endColumns": "12,77,75,80,106,95,106,131,82,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,79,133,74,75,136,96,97,56,54,65,69,76,85,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78", "endOffsets": "304,3064,3140,3221,3328,3424,3531,3663,3746,3811,3905,3974,4033,4118,4181,4244,4302,4367,4428,4489,4595,4653,4713,4772,4842,4958,5037,5117,5251,5326,5402,5539,5636,5734,5791,5846,5912,5982,6059,6145,6230,6298,6374,6455,6533,6634,6720,6807,6904,7003,7077,7147,7251,7305,7392,7459,7549,7641,7703,7767,7830,7896,8001,8111,8212,8319,8380,8439,8518"}}]}]}