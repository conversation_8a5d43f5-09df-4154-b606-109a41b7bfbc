R_DEF: Internal format may change without notice
local
color accent_color
color accent_light
color background_card
color background_primary
color background_secondary
color black_overlay
color celebration_primary
color celebration_secondary
color divider_color
color error_color
color error_light
color gradient_end
color gradient_end_secondary
color gradient_end_tertiary
color gradient_start
color gradient_start_secondary
color gradient_start_tertiary
color grid_border
color grid_cell_correct
color grid_cell_normal
color grid_cell_pressed
color grid_cell_wrong
color grid_correct
color grid_hover
color grid_selected
color grid_wrong
color md_theme_light_background
color md_theme_light_onBackground
color md_theme_light_onPrimary
color md_theme_light_onPrimaryContainer
color md_theme_light_onSecondary
color md_theme_light_onSecondaryContainer
color md_theme_light_onSurface
color md_theme_light_onSurfaceVariant
color md_theme_light_onTertiary
color md_theme_light_onTertiaryContainer
color md_theme_light_outline
color md_theme_light_primary
color md_theme_light_primaryContainer
color md_theme_light_secondary
color md_theme_light_secondaryContainer
color md_theme_light_surface
color md_theme_light_surfaceVariant
color md_theme_light_tertiary
color md_theme_light_tertiaryContainer
color overlay_background
color particle_color
color primary_color
color primary_dark
color primary_light
color secondary_color
color star_color
color success_color
color success_light
color text_disabled
color text_hint
color text_primary
color text_secondary
color theme_animal_accent
color theme_animal_background
color theme_animal_primary
color theme_animal_secondary
color theme_cartoon_accent
color theme_cartoon_background
color theme_cartoon_primary
color theme_cartoon_secondary
color theme_space_accent
color theme_space_background
color theme_space_primary
color theme_space_secondary
color warning_color
color warning_light
drawable animal_background
drawable animal_game_background
drawable cartoon_background
drawable cartoon_game_background
drawable clean_game_background
drawable clickable_background
drawable countdown_background
drawable countdown_circle_background
drawable countdown_gradient_background
drawable default_background
drawable default_game_background
drawable dialog_background
drawable difficulty_background
drawable game_cell_background
drawable gradient_background
drawable gradient_background_secondary
drawable gradient_gold
drawable gradient_light_bg
drawable gradient_multiplication_bg
drawable gradient_number_bg
drawable gradient_stage1_bg
drawable gradient_stage2_bg
drawable gradient_stage3_bg
drawable grid_cell_background
drawable grid_cell_cartoon
drawable grid_cell_clicked
drawable grid_cell_gradient
drawable grid_cell_modern
drawable grid_cell_space
drawable grid_cell_wrong
drawable grid_container_background
drawable ic_arrow_back
drawable ic_brain_launcher
drawable ic_launcher_foreground
drawable ic_pause
drawable ic_settings
drawable ic_skip_next
drawable ic_trophy
drawable light_gradient_background
drawable modern_button_background
drawable modern_card_background
drawable number_display_background
drawable number_highlight_background
drawable space_background
drawable space_game_background
drawable status_background
drawable target_background
drawable timer_background
drawable timer_background_light
id animal_status
id app_info_layout
id background_view
id bottom_space
id btn_answer
id btn_answer1
id btn_answer2
id btn_answer3
id btn_answer4
id btn_back
id btn_back_to_main
id btn_back_to_stages
id btn_check_answer
id btn_color_match
id btn_color_name
id btn_complete
id btn_confirm
id btn_easy
id btn_hard
id btn_interference
id btn_medium
id btn_memory_card
id btn_next
id btn_play
id btn_play_again
id btn_puzzle_challenge
id btn_replay
id btn_reset
id btn_restart
id btn_rewards
id btn_rhythm_learning
id btn_settings
id btn_skip
id btn_start
id btn_start_game
id btn_start_learning
id btn_start_training
id btn_theme_selection
id btn_tutorial
id card_about
id card_color_training
id card_content
id card_crystal_reward
id card_fill_blanks
id card_interactive_count
id card_memory
id card_multiplication_table
id card_number
id card_number_display
id card_schulte
id card_sequence_memory
id card_stage1
id card_stage2
id card_stage3
id card_stats
id card_step
id card_test_mode
id card_visual_demo
id card_voice
id cartoon_status
id celebration_container
id container_groups
id container_options
id container_total
id countdown_container
id et_cols
id et_rows
id et_username
id grid_animals
id grid_colors
id grid_items
id grid_layout
id grid_positions
id layout_input_area
id layout_number_buttons
id layout_recent_records
id particles_container
id progress_bar
id progress_color
id progress_memory
id progress_multiplication
id progress_overall
id progress_schulte
id records_container
id recycler_achievements
id recycler_answers
id recycler_cards
id recycler_numbers
id recycler_recent_records
id recycler_steps
id root_view
id space_status
id splash_container
id switch_test_mode
id switch_voice
id theme_animal
id theme_cartoon
id theme_default
id theme_space
id top_status
id tv_addition_formula
id tv_average_time
id tv_avg_time
id tv_best_record
id tv_best_score
id tv_best_time
id tv_color_stats
id tv_completion_time
id tv_content
id tv_countdown
id tv_crystal_description
id tv_crystal_emoji
id tv_crystal_progress
id tv_crystal_title
id tv_current_difficulty
id tv_current_equation
id tv_current_mode
id tv_current_number
id tv_description
id tv_dialog_message
id tv_dialog_title
id tv_difficulty
id tv_difficulty_label
id tv_explanation
id tv_formula
id tv_game_time
id tv_game_type
id tv_icon
id tv_input_prompt
id tv_instruction
id tv_level
id tv_memory_stats
id tv_message
id tv_multiplication_formula
id tv_multiplication_stats
id tv_narration
id tv_number
id tv_number_mode
id tv_play_count
id tv_progress
id tv_question
id tv_record_time
id tv_result
id tv_schulte_stats
id tv_score
id tv_stage1_progress
id tv_stage1_status
id tv_stage2_progress
id tv_stage2_status
id tv_stage3_progress
id tv_stage3_status
id tv_status
id tv_step
id tv_streak_days
id tv_sub_title
id tv_target_name
id tv_target_shape
id tv_target_time
id tv_time
id tv_time_result
id tv_timer
id tv_timestamp
id tv_title
id tv_today_count
id tv_today_games
id tv_total_count
id tv_total_crystals
id tv_total_fragments
id tv_total_games
id tv_total_score
id tv_username
id tv_welcome
id tv_welcome_username
id view_target_color
layout activity_celebration
layout activity_color_game
layout activity_color_training
layout activity_concept_animation_demo
layout activity_concept_interactive_count
layout activity_concept_test_game
layout activity_concept_visual_demo
layout activity_countdown
layout activity_game
layout activity_main
layout activity_memory_card_game
layout activity_memory_game
layout activity_memory_game_main
layout activity_multiplication_challenge
layout activity_multiplication_concept
layout activity_multiplication_number
layout activity_multiplication_table
layout activity_number_learning
layout activity_puzzle_challenge
layout activity_rewards
layout activity_rhythm_learning
layout activity_schulte_grid
layout activity_settings
layout activity_splash
layout activity_theme_selection
layout activity_tutorial
layout activity_username_setup
layout dialog_game_completion
layout dialog_sequence_completion
layout item_achievement
layout item_challenge_answer
layout item_game_record
layout item_memory_card
layout item_number_selection
layout item_tutorial_step
mipmap ic_launcher
string app_name
style AppTheme
style CardDescription
style CardTitle
style CustomDialogTheme
style DifficultyText
style GameTimer
style NextNumber
style SchulteGridCell
style StatLabel
style StatNumber
xml anythink_bk_gdt_file_path
xml anythink_bk_sigmob_file_path
xml anythink_bk_tt_file_path
