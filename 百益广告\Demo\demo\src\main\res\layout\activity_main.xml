<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <Button
                android:id="@+id/a_main_ChannelNumManage_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:text="渠道号管理" />

            <Button
                android:id="@+id/a_main_Splash_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:text="开屏(Splash)" />

            <Button
                android:id="@+id/a_main_Interaction_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:text="插屏(Interaction)" />

            <Button
                android:id="@+id/a_main_Banner_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:text="Banner(Banner)" />

            <Button
                android:id="@+id/a_main_Feed_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:text="信息流(Feed)" />

            <Button
                android:id="@+id/a_main_RewardVideo_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:text="激励视频(RewardVideo)" />

            <Button
                android:id="@+id/a_main_DrawFeed_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:text="Draw竖版视频信息流(DrawFeed)" />

            <Button
                android:id="@+id/a_main_Native_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:text="自渲染(Native)" />

            <Button
                android:id="@+id/a_main_Video_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:text="短视频(Video)" />

            <Button
                android:id="@+id/a_main_NewsFeed_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:text="新闻资讯(NewsFeed)" />

        </LinearLayout>
    </ScrollView>
</LinearLayout>