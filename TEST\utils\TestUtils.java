package TEST.utils;

import java.io.*;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 测试工具类
 * 
 * 提供测试过程中需要的各种工具方法：
 * 1. 模拟数据生成
 * 2. 文件操作工具
 * 3. 时间测量工具
 * 4. 断言辅助方法
 * 5. 测试环境管理
 */
public class TestUtils {
    
    private static final String TEST_DATA_PREFIX = "test_";
    private static final Random random = new Random();
    
    /**
     * 生成测试用的SharedPreferences键名
     */
    public static String getTestPrefsKey(String originalKey) {
        return TEST_DATA_PREFIX + originalKey;
    }
    
    /**
     * 清理测试数据
     */
    public static void cleanupTestData() {
        System.out.println("🧹 清理测试数据...");
        // 这里可以实现清理测试用的SharedPreferences数据
        // 在实际Android环境中，需要通过Context来操作
        System.out.println("  ✓ 测试数据清理完成");
    }
    
    /**
     * 生成随机游戏时间（毫秒）
     */
    public static long generateRandomGameTime(int minSeconds, int maxSeconds) {
        int seconds = random.nextInt(maxSeconds - minSeconds + 1) + minSeconds;
        return seconds * 1000L + random.nextInt(1000); // 添加毫秒部分
    }
    
    /**
     * 生成随机积分
     */
    public static int generateRandomScore(int min, int max) {
        return random.nextInt(max - min + 1) + min;
    }
    
    /**
     * 生成随机进度百分比
     */
    public static int generateRandomProgress() {
        return random.nextInt(101); // 0-100
    }
    
    /**
     * 模拟舒尔特方格游戏数据
     */
    public static Map<String, Object> generateSchulteGridData(int gridSize) {
        Map<String, Object> data = new HashMap<>();
        data.put("gridSize", gridSize);
        data.put("bestTime", generateRandomGameTime(10, 120));
        data.put("averageTime", generateRandomGameTime(15, 150));
        data.put("totalGames", random.nextInt(50) + 1);
        data.put("todayGames", random.nextInt(10));
        return data;
    }
    
    /**
     * 模拟记忆游戏数据
     */
    public static Map<String, Object> generateMemoryGameData(String mode, int difficulty) {
        Map<String, Object> data = new HashMap<>();
        data.put("mode", mode);
        data.put("difficulty", difficulty);
        data.put("bestTime", generateRandomGameTime(5, 60));
        data.put("totalGames", random.nextInt(30) + 1);
        data.put("successRate", random.nextInt(101));
        return data;
    }
    
    /**
     * 模拟乘法表学习数据
     */
    public static Map<String, Object> generateMultiplicationData() {
        Map<String, Object> data = new HashMap<>();
        data.put("stage1Progress", generateRandomProgress());
        data.put("stage2Progress", generateRandomProgress());
        data.put("stage3Progress", generateRandomProgress());
        data.put("conceptCompleted", random.nextBoolean());
        data.put("visualDemoCompleted", random.nextBoolean());
        data.put("interactiveCountCompleted", random.nextBoolean());
        data.put("animationDemoCompleted", random.nextBoolean());
        
        // 生成数字1-9的学习进度
        for (int i = 1; i <= 9; i++) {
            data.put("number_" + i + "_progress", generateRandomProgress());
            data.put("number_" + i + "_total_games", random.nextInt(20));
        }
        
        return data;
    }
    
    /**
     * 模拟水晶系统数据
     */
    public static Map<String, Object> generateCrystalData() {
        Map<String, Object> data = new HashMap<>();
        
        // 各种水晶类型的数据
        String[] crystalTypes = {"schulte_crystal", "memory_crystal", "color_crystal", "math_crystal"};
        
        for (String type : crystalTypes) {
            data.put(type + "_fragments", random.nextInt(30));
            data.put(type + "_complete", random.nextInt(10));
        }
        
        return data;
    }
    
    /**
     * 验证时间格式
     */
    public static boolean isValidTimeFormat(String timeStr) {
        if (timeStr == null || timeStr.isEmpty()) {
            return false;
        }
        
        // 检查格式：MM:SS 或 MM:SS.ms
        return timeStr.matches("\\d{2}:\\d{2}(\\.\\d{2})?");
    }
    
    /**
     * 验证积分范围
     */
    public static boolean isValidScore(int score, int min, int max) {
        return score >= min && score <= max;
    }
    
    /**
     * 验证进度百分比
     */
    public static boolean isValidProgress(int progress) {
        return progress >= 0 && progress <= 100;
    }
    
    /**
     * 测量方法执行时间
     */
    public static long measureExecutionTime(Runnable method) {
        long startTime = System.nanoTime();
        method.run();
        long endTime = System.nanoTime();
        return TimeUnit.NANOSECONDS.toMillis(endTime - startTime);
    }
    
    /**
     * 生成测试报告文件名
     */
    public static String generateReportFileName(String testType) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        return "test_report_" + testType + "_" + timestamp + ".html";
    }
    
    /**
     * 创建测试目录
     */
    public static boolean createTestDirectory(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists()) {
            return dir.mkdirs();
        }
        return true;
    }
    
    /**
     * 写入测试日志
     */
    public static void writeTestLog(String message) {
        try {
            createTestDirectory("TEST/logs");
            FileWriter writer = new FileWriter("TEST/logs/test.log", true);
            writer.write("[" + new Date() + "] " + message + "\n");
            writer.close();
        } catch (IOException e) {
            System.err.println("写入测试日志失败: " + e.getMessage());
        }
    }
    
    /**
     * 比较两个浮点数是否相等（考虑精度）
     */
    public static boolean floatEquals(double a, double b, double epsilon) {
        return Math.abs(a - b) < epsilon;
    }
    
    /**
     * 生成随机字符串
     */
    public static String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }
    
    /**
     * 模拟网络延迟
     */
    public static void simulateNetworkDelay(int minMs, int maxMs) {
        try {
            int delay = random.nextInt(maxMs - minMs + 1) + minMs;
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 验证数组是否已排序
     */
    public static boolean isSorted(int[] array) {
        for (int i = 1; i < array.length; i++) {
            if (array[i] < array[i - 1]) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 生成边界测试数据
     */
    public static List<Integer> generateBoundaryTestData() {
        List<Integer> data = new ArrayList<>();
        data.add(Integer.MIN_VALUE);
        data.add(-1);
        data.add(0);
        data.add(1);
        data.add(Integer.MAX_VALUE);
        return data;
    }
    
    /**
     * 生成无效输入测试数据
     */
    public static List<String> generateInvalidInputData() {
        List<String> data = new ArrayList<>();
        data.add(null);
        data.add("");
        data.add("   ");
        data.add("abc");
        data.add("123abc");
        data.add("!@#$%");
        data.add("很长很长很长很长很长很长很长很长很长很长的字符串");
        return data;
    }
    
    /**
     * 打印测试分隔线
     */
    public static void printSeparator(String title) {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("  " + title);
        System.out.println("=".repeat(60));
    }
    
    /**
     * 打印测试子标题
     */
    public static void printSubTitle(String subtitle) {
        System.out.println("\n" + "-".repeat(40));
        System.out.println("  " + subtitle);
        System.out.println("-".repeat(40));
    }
    
    /**
     * 格式化测试结果
     */
    public static String formatTestResult(String testName, boolean passed, long duration) {
        String status = passed ? "✅ PASS" : "❌ FAIL";
        return String.format("%-40s %s (%dms)", testName, status, duration);
    }
    
    /**
     * 计算成功率
     */
    public static double calculateSuccessRate(int passed, int total) {
        if (total == 0) return 0.0;
        return (double) passed / total * 100.0;
    }
    
    /**
     * 生成测试摘要
     */
    public static String generateTestSummary(int total, int passed, int failed, int errors) {
        StringBuilder sb = new StringBuilder();
        sb.append("测试摘要:\n");
        sb.append("总测试数: ").append(total).append("\n");
        sb.append("通过: ").append(passed).append("\n");
        sb.append("失败: ").append(failed).append("\n");
        sb.append("错误: ").append(errors).append("\n");
        sb.append("成功率: ").append(String.format("%.2f%%", calculateSuccessRate(passed, total)));
        return sb.toString();
    }
    
    /**
     * 检查测试环境
     */
    public static boolean checkTestEnvironment() {
        System.out.println("🔍 检查测试环境...");
        
        // 检查Java版本
        String javaVersion = System.getProperty("java.version");
        System.out.println("  Java版本: " + javaVersion);
        
        // 检查可用内存
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory() / 1024 / 1024; // MB
        System.out.println("  最大可用内存: " + maxMemory + "MB");
        
        // 检查测试目录
        boolean testDirExists = new File("TEST").exists();
        System.out.println("  测试目录存在: " + testDirExists);
        
        return testDirExists;
    }
}
