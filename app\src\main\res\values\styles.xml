<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 应用主题 -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="colorAccent">@color/accent_color</item>
        <item name="android:windowBackground">@color/background_secondary</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
    </style>

    <!-- 舒尔特方格单元格样式 -->
    <style name="SchulteGridCell">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_margin">2dp</item>
        <item name="android:background">@drawable/grid_cell_background</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style>

    <!-- 卡片标题样式 -->
    <style name="CardTitle">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style>

    <!-- 卡片描述样式 -->
    <style name="CardDescription">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:layout_marginBottom">12dp</item>
    </style>

    <!-- 统计数字样式 -->
    <style name="StatNumber">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <!-- 统计标签样式 -->
    <style name="StatLabel">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/text_hint</item>
    </style>

    <!-- 游戏计时器样式 -->
    <style name="GameTimer">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/primary_color</item>
    </style>

    <!-- 下一个数字样式 -->
    <style name="NextNumber">
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/accent_color</item>
    </style>

    <!-- 难度显示样式 -->
    <style name="DifficultyText">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/success_color</item>
    </style>

    <!-- 自定义对话框样式 -->
    <style name="CustomDialogTheme" parent="Theme.MaterialComponents.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
    </style>
</resources>
