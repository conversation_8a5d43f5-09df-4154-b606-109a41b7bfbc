package com.example.concentration;

import java.util.List;

public class ChallengeQuestion {
    public int num1;
    public int num2;
    public int correctAnswer;
    public List<Integer> options;
    
    public ChallengeQuestion() {
    }
    
    public ChallengeQuestion(int num1, int num2) {
        this.num1 = num1;
        this.num2 = num2;
        this.correctAnswer = num1 * num2;
    }
    
    @Override
    public String toString() {
        return num1 + " × " + num2 + " = " + correctAnswer;
    }
}
