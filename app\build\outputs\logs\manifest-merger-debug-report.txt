-- Merging decision tree log ---
provider#com.bytedance.sdk.openadsdk.multipro.TTMultiProvider
INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml:34:9-37:40
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:45:9-168
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:45:9-168
	android:authorities
		INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:36:13-67
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:37:13-37
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:35:13-80
provider#com.bytedance.sdk.openadsdk.TTFileProvider
INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml:38:9-46:20
	android:grantUriPermissions
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:42:13-47
	android:authorities
		INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:40:13-66
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:41:13-37
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:39:13-70
provider#com.qq.e.comm.GDTFileProvider
INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml:48:9-56:20
	android:grantUriPermissions
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:52:13-47
	android:authorities
		INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:50:13-68
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:51:13-37
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:49:13-57
provider#com.sigmob.sdk.SigmobFileV4Provider
INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml:58:9-67:20
	android:grantUriPermissions
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:63:13-47
	android:authorities
		INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:60:13-63
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:61:13-37
	android:initOrder
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:62:13-36
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:59:13-63
manifest
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:2:1-204:12
INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml:2:1-204:12
INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml:2:1-204:12
INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml:2:1-204:12
MERGED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:2:1-53:12
MERGED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:2:1-58:12
MERGED from [anythink_banner.aar] C:\Users\<USER>\.gradle\caches\transforms-3\1f2624d0c0c6e9f455af9dc863724ae0\transformed\jetified-anythink_banner\AndroidManifest.xml:2:1-13:12
MERGED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:2:1-51:12
MERGED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:2:1-74:12
MERGED from [anythink_interstitial.aar] C:\Users\<USER>\.gradle\caches\transforms-3\b5cc6b602694d5ab809d4e95bc91f53e\transformed\jetified-anythink_interstitial\AndroidManifest.xml:2:1-13:12
MERGED from [anythink_native.aar] C:\Users\<USER>\.gradle\caches\transforms-3\7af093fe7f17e0c3a647664cf25da2f8\transformed\jetified-anythink_native\AndroidManifest.xml:2:1-14:12
MERGED from [anythink_network_baidu.aar] C:\Users\<USER>\.gradle\caches\transforms-3\ce43e2aa2554c765f3cfa0634f5eda38\transformed\jetified-anythink_network_baidu\AndroidManifest.xml:2:1-16:12
MERGED from [anythink_network_csj_mix.aar] C:\Users\<USER>\.gradle\caches\transforms-3\f64897484faa12ada527d54eb745b431\transformed\jetified-anythink_network_csj_mix\AndroidManifest.xml:2:1-12:12
MERGED from [anythink_network_gdt.aar] C:\Users\<USER>\.gradle\caches\transforms-3\5f1f9bbf883cfa1220d07503d95cf1ac\transformed\jetified-anythink_network_gdt\AndroidManifest.xml:2:1-16:12
MERGED from [anythink_network_kuaishou.aar] C:\Users\<USER>\.gradle\caches\transforms-3\6c5f8582ca0f6df83ee8a57074d55fc0\transformed\jetified-anythink_network_kuaishou\AndroidManifest.xml:2:1-16:12
MERGED from [anythink_network_mintegral_china.aar] C:\Users\<USER>\.gradle\caches\transforms-3\664ee24a4f6fbf622776a8ce662af7dd\transformed\jetified-anythink_network_mintegral_china\AndroidManifest.xml:2:1-16:12
MERGED from [anythink_network_mobrain_mix_plus.aar] C:\Users\<USER>\.gradle\caches\transforms-3\197b2095d494c640280cf459638165d1\transformed\jetified-anythink_network_mobrain_mix_plus\AndroidManifest.xml:2:1-12:12
MERGED from [anythink_network_sigmob.aar] C:\Users\<USER>\.gradle\caches\transforms-3\9bf89581943efb627f2b427a2c80676d\transformed\jetified-anythink_network_sigmob\AndroidManifest.xml:2:1-16:12
MERGED from [anythink_rewardvideo.aar] C:\Users\<USER>\.gradle\caches\transforms-3\d370f4103a419bd03131f12381e4296b\transformed\jetified-anythink_rewardvideo\AndroidManifest.xml:2:1-13:12
MERGED from [anythink_splash.aar] C:\Users\<USER>\.gradle\caches\transforms-3\458f0753acc8e8706b494ac19de11b01\transformed\jetified-anythink_splash\AndroidManifest.xml:2:1-13:12
MERGED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:2:1-39:12
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:10:1-49:12
MERGED from [beizi_fusion_sdk_4.90.4.36.aar] C:\Users\<USER>\.gradle\caches\transforms-3\edb8951c7f25448943e5f159ccbca8a9\transformed\jetified-beizi_fusion_sdk_4.90.4.36\AndroidManifest.xml:2:1-18:12
MERGED from [by_5.05.10.aar] C:\Users\<USER>\.gradle\caches\transforms-3\9b2c95f474c8e1c18611376b46d3f6ce\transformed\jetified-by_5.05.10\AndroidManifest.xml:2:1-11:12
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:2:1-86:12
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:2:1-194:12
MERGED from [mediation_baidu_adapter_9.3905.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\f93b0c9554f067ea285686fdb3458d41\transformed\jetified-mediation_baidu_adapter_9.3905.1\AndroidManifest.xml:2:1-11:12
MERGED from [mediation_gdt_adapter_4.640.1510.0.aar] C:\Users\<USER>\.gradle\caches\transforms-3\86709dcc5f68b50a22bb06f51f8d0da4\transformed\jetified-mediation_gdt_adapter_4.640.1510.0\AndroidManifest.xml:2:1-11:12
MERGED from [mediation_ks_adapter_3.3.76.5.0.aar] C:\Users\<USER>\.gradle\caches\transforms-3\f5969883c37f11fce5d76b1f98a8e00e\transformed\jetified-mediation_ks_adapter_3.3.76.5.0\AndroidManifest.xml:2:1-11:12
MERGED from [mediation_mintegral_adapter_16.6.57.9.aar] C:\Users\<USER>\.gradle\caches\transforms-3\18770e105eb019778236642398edc4d4\transformed\jetified-mediation_mintegral_adapter_16.6.57.9\AndroidManifest.xml:2:1-11:12
MERGED from [mediation_sigmob_adapter_4.22.2.0.aar] C:\Users\<USER>\.gradle\caches\transforms-3\874c3f359609fd3ecb5bd9b8f8eaf2a8\transformed\jetified-mediation_sigmob_adapter_4.22.2.0\AndroidManifest.xml:2:1-11:12
MERGED from [oaid_sdk_1.0.25.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01bc7fca446bdde30d72de23c77cdbfa\transformed\jetified-oaid_sdk_1.0.25\AndroidManifest.xml:2:1-17:12
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:2:1-121:12
MERGED from [wind-common-1.7.7.aar] C:\Users\<USER>\.gradle\caches\transforms-3\9bea298525678d2d8b769291a94b3fad\transformed\jetified-wind-common-1.7.7\AndroidManifest.xml:2:1-9:12
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:2:1-91:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\ecca92267eba45ce3e43e495f0e1965d\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\395d71d697a2819f71d941e83c2d5fe8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a6e5f6212cc01c97226962f50ee2288b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\38fdf4e56534b3c4c6a8297bf7eeddee\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8bdece1432d6bd447ee6e396aae1f67\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-3\24d8c27b9d89ea399a191c23392817f3\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f4c5009e19be168bbbd44bc6887a962\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9007ca337390510c83423ff4c37ff3cd\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\580ce83cbd06ed986a537ce85c92ad08\transformed\jetified-glide-4.9.0\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e056b10f26770d4e3dd34feeb1354a3c\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\c3dc071c2c0ff1053390120e17d4afaf\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\a99a550775fc239fa35ebbba720a5cdf\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fcc853f08bc42045cd980d63acc7c5a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b6ed83c756fa81836783bb8a55d86113\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2c0baf04ac6533147f9384ef4e31969\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1abb1035bb8a9c4eb057e4213de35400\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1743650eff319645d518a855bfe1fe5a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\90714f149f25b0cfa5dbdeb0b88fec36\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ee8791f02be5799e6df9830af15f939\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dedf2eb2b169583c131497feb19f69d4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b473ba004de6f7fc4ee8164a18a79bd2\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16d49e6813d7c6aef7e67bde9a0987a7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\90dbda2796d0082100feb9bfc0838eaa\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\85bca364794f8d628710ba163dcadda2\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\90d8c277878f3e93aea5f5378d390fb3\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\59be872dd741da2aefad29199a5c688e\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1b340f1aca83c1e882ac68b79d8d0df7\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d1da6a6161390c1e21c57807d5a91b4\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8eec3305fa08ab6c364850fac4f2f3da\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\c6b758a0250d1d72a62de869337013f9\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\a419870f824a5ed52312905adad0dfd9\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\48226b2b3c5a88035ae96e2d0ebf97e5\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb5fac538ce26b1dc83d6f236aa3293c\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e691780b60fbd82fe9bef3ba34a4f1e1\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e8d651961ab3a64fe5e9a922dcb7cc07\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3733f843a5ee849fe02ab2bf462dfa2\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\738310a87d9925967294fafc069fcc67\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b865b04191c5f21eb914045e17fc0f4\transformed\jetified-gifdecoder-4.9.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b21884de3c1fed91191658c9c59e73bb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d338db99dbee4b2179b8774b799ae6a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\85cd030943f9498f5b64cfe78341395f\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b7916a1392bbb1cace393bfa310f2e1c\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5271149ab19ed6165189410cef969bfe\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae6d865693f1ebb006f8a0018c2b43a8\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3361067d52275e58bda1185bd0e0722\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c59bd9210ecc79c0b9d6f2393b2a4a\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d240c2876715f64c8f86f348098b6d9\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:12:5-67
MERGED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:12:5-67
MERGED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:12:5-67
MERGED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:12:5-67
MERGED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:11:5-67
MERGED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:11:5-67
MERGED from [anythink_network_baidu.aar] C:\Users\<USER>\.gradle\caches\transforms-3\ce43e2aa2554c765f3cfa0634f5eda38\transformed\jetified-anythink_network_baidu\AndroidManifest.xml:11:5-67
MERGED from [anythink_network_baidu.aar] C:\Users\<USER>\.gradle\caches\transforms-3\ce43e2aa2554c765f3cfa0634f5eda38\transformed\jetified-anythink_network_baidu\AndroidManifest.xml:11:5-67
MERGED from [anythink_network_gdt.aar] C:\Users\<USER>\.gradle\caches\transforms-3\5f1f9bbf883cfa1220d07503d95cf1ac\transformed\jetified-anythink_network_gdt\AndroidManifest.xml:11:5-67
MERGED from [anythink_network_gdt.aar] C:\Users\<USER>\.gradle\caches\transforms-3\5f1f9bbf883cfa1220d07503d95cf1ac\transformed\jetified-anythink_network_gdt\AndroidManifest.xml:11:5-67
MERGED from [anythink_network_kuaishou.aar] C:\Users\<USER>\.gradle\caches\transforms-3\6c5f8582ca0f6df83ee8a57074d55fc0\transformed\jetified-anythink_network_kuaishou\AndroidManifest.xml:11:5-67
MERGED from [anythink_network_kuaishou.aar] C:\Users\<USER>\.gradle\caches\transforms-3\6c5f8582ca0f6df83ee8a57074d55fc0\transformed\jetified-anythink_network_kuaishou\AndroidManifest.xml:11:5-67
MERGED from [anythink_network_mintegral_china.aar] C:\Users\<USER>\.gradle\caches\transforms-3\664ee24a4f6fbf622776a8ce662af7dd\transformed\jetified-anythink_network_mintegral_china\AndroidManifest.xml:11:5-67
MERGED from [anythink_network_mintegral_china.aar] C:\Users\<USER>\.gradle\caches\transforms-3\664ee24a4f6fbf622776a8ce662af7dd\transformed\jetified-anythink_network_mintegral_china\AndroidManifest.xml:11:5-67
MERGED from [anythink_network_sigmob.aar] C:\Users\<USER>\.gradle\caches\transforms-3\9bf89581943efb627f2b427a2c80676d\transformed\jetified-anythink_network_sigmob\AndroidManifest.xml:11:5-67
MERGED from [anythink_network_sigmob.aar] C:\Users\<USER>\.gradle\caches\transforms-3\9bf89581943efb627f2b427a2c80676d\transformed\jetified-anythink_network_sigmob\AndroidManifest.xml:11:5-67
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:19:5-67
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:19:5-67
MERGED from [beizi_fusion_sdk_4.90.4.36.aar] C:\Users\<USER>\.gradle\caches\transforms-3\edb8951c7f25448943e5f159ccbca8a9\transformed\jetified-beizi_fusion_sdk_4.90.4.36\AndroidManifest.xml:11:5-67
MERGED from [beizi_fusion_sdk_4.90.4.36.aar] C:\Users\<USER>\.gradle\caches\transforms-3\edb8951c7f25448943e5f159ccbca8a9\transformed\jetified-beizi_fusion_sdk_4.90.4.36\AndroidManifest.xml:11:5-67
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:21:5-67
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:21:5-67
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:16:5-67
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:16:5-67
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:23:5-66
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:23:5-66
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:12:5-67
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:12:5-67
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:10:5-79
MERGED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:10:5-79
MERGED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:19:5-79
MERGED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:19:5-79
MERGED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:12:5-79
MERGED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:12:5-79
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:22:5-79
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:22:5-79
MERGED from [beizi_fusion_sdk_4.90.4.36.aar] C:\Users\<USER>\.gradle\caches\transforms-3\edb8951c7f25448943e5f159ccbca8a9\transformed\jetified-beizi_fusion_sdk_4.90.4.36\AndroidManifest.xml:14:5-79
MERGED from [beizi_fusion_sdk_4.90.4.36.aar] C:\Users\<USER>\.gradle\caches\transforms-3\edb8951c7f25448943e5f159ccbca8a9\transformed\jetified-beizi_fusion_sdk_4.90.4.36\AndroidManifest.xml:14:5-79
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:22:5-79
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:22:5-79
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:15:5-79
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:15:5-79
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:22:5-78
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:22:5-78
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:13:5-79
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:13:5-79
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:8:5-76
MERGED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:11:5-76
MERGED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:11:5-76
MERGED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:21:5-76
MERGED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:21:5-76
MERGED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:14:5-76
MERGED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:14:5-76
MERGED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:13:5-76
MERGED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:13:5-76
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:23:5-76
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:23:5-76
MERGED from [beizi_fusion_sdk_4.90.4.36.aar] C:\Users\<USER>\.gradle\caches\transforms-3\edb8951c7f25448943e5f159ccbca8a9\transformed\jetified-beizi_fusion_sdk_4.90.4.36\AndroidManifest.xml:15:5-76
MERGED from [beizi_fusion_sdk_4.90.4.36.aar] C:\Users\<USER>\.gradle\caches\transforms-3\edb8951c7f25448943e5f159ccbca8a9\transformed\jetified-beizi_fusion_sdk_4.90.4.36\AndroidManifest.xml:15:5-76
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:23:5-76
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:23:5-76
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:18:5-76
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:18:5-76
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:27:5-75
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:27:5-75
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:14:5-76
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:14:5-76
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:12:5-75
MERGED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:11:5-75
MERGED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:11:5-75
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:17:5-75
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:17:5-75
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:15:5-75
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:15:5-75
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:12:22-72
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:14:5-80
MERGED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:13:5-80
MERGED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:13:5-80
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:20:5-80
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:20:5-80
MERGED from [beizi_fusion_sdk_4.90.4.36.aar] C:\Users\<USER>\.gradle\caches\transforms-3\edb8951c7f25448943e5f159ccbca8a9\transformed\jetified-beizi_fusion_sdk_4.90.4.36\AndroidManifest.xml:12:5-80
MERGED from [beizi_fusion_sdk_4.90.4.36.aar] C:\Users\<USER>\.gradle\caches\transforms-3\edb8951c7f25448943e5f159ccbca8a9\transformed\jetified-beizi_fusion_sdk_4.90.4.36\AndroidManifest.xml:12:5-80
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:23:5-80
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:23:5-80
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:25:5-79
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:25:5-79
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:14:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:15:5-81
MERGED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:14:5-81
MERGED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:14:5-81
MERGED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:12:5-81
MERGED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:12:5-81
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:21:5-81
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:21:5-81
MERGED from [beizi_fusion_sdk_4.90.4.36.aar] C:\Users\<USER>\.gradle\caches\transforms-3\edb8951c7f25448943e5f159ccbca8a9\transformed\jetified-beizi_fusion_sdk_4.90.4.36\AndroidManifest.xml:13:5-81
MERGED from [beizi_fusion_sdk_4.90.4.36.aar] C:\Users\<USER>\.gradle\caches\transforms-3\edb8951c7f25448943e5f159ccbca8a9\transformed\jetified-beizi_fusion_sdk_4.90.4.36\AndroidManifest.xml:13:5-81
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:22:5-81
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:22:5-81
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:24:5-80
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:24:5-80
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:15:22-78
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:17:5-81
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:9:5-81
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:9:5-81
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:21:5-81
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:21:5-81
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:17:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:18:5-79
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:18:22-76
application
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:20:5-202:19
INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml:20:5-202:19
MERGED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:16:5-51:19
MERGED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:16:5-51:19
MERGED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:30:5-56:19
MERGED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:30:5-56:19
MERGED from [anythink_banner.aar] C:\Users\<USER>\.gradle\caches\transforms-3\1f2624d0c0c6e9f455af9dc863724ae0\transformed\jetified-anythink_banner\AndroidManifest.xml:11:5-20
MERGED from [anythink_banner.aar] C:\Users\<USER>\.gradle\caches\transforms-3\1f2624d0c0c6e9f455af9dc863724ae0\transformed\jetified-anythink_banner\AndroidManifest.xml:11:5-20
MERGED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:17:5-49:19
MERGED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:17:5-49:19
MERGED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:24:5-72:19
MERGED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:24:5-72:19
MERGED from [anythink_interstitial.aar] C:\Users\<USER>\.gradle\caches\transforms-3\b5cc6b602694d5ab809d4e95bc91f53e\transformed\jetified-anythink_interstitial\AndroidManifest.xml:11:5-20
MERGED from [anythink_interstitial.aar] C:\Users\<USER>\.gradle\caches\transforms-3\b5cc6b602694d5ab809d4e95bc91f53e\transformed\jetified-anythink_interstitial\AndroidManifest.xml:11:5-20
MERGED from [anythink_native.aar] C:\Users\<USER>\.gradle\caches\transforms-3\7af093fe7f17e0c3a647664cf25da2f8\transformed\jetified-anythink_native\AndroidManifest.xml:11:5-12:19
MERGED from [anythink_native.aar] C:\Users\<USER>\.gradle\caches\transforms-3\7af093fe7f17e0c3a647664cf25da2f8\transformed\jetified-anythink_native\AndroidManifest.xml:11:5-12:19
MERGED from [anythink_network_baidu.aar] C:\Users\<USER>\.gradle\caches\transforms-3\ce43e2aa2554c765f3cfa0634f5eda38\transformed\jetified-anythink_network_baidu\AndroidManifest.xml:13:5-14:19
MERGED from [anythink_network_baidu.aar] C:\Users\<USER>\.gradle\caches\transforms-3\ce43e2aa2554c765f3cfa0634f5eda38\transformed\jetified-anythink_network_baidu\AndroidManifest.xml:13:5-14:19
MERGED from [anythink_network_csj_mix.aar] C:\Users\<USER>\.gradle\caches\transforms-3\f64897484faa12ada527d54eb745b431\transformed\jetified-anythink_network_csj_mix\AndroidManifest.xml:10:5-20
MERGED from [anythink_network_csj_mix.aar] C:\Users\<USER>\.gradle\caches\transforms-3\f64897484faa12ada527d54eb745b431\transformed\jetified-anythink_network_csj_mix\AndroidManifest.xml:10:5-20
MERGED from [anythink_network_gdt.aar] C:\Users\<USER>\.gradle\caches\transforms-3\5f1f9bbf883cfa1220d07503d95cf1ac\transformed\jetified-anythink_network_gdt\AndroidManifest.xml:13:5-14:19
MERGED from [anythink_network_gdt.aar] C:\Users\<USER>\.gradle\caches\transforms-3\5f1f9bbf883cfa1220d07503d95cf1ac\transformed\jetified-anythink_network_gdt\AndroidManifest.xml:13:5-14:19
MERGED from [anythink_network_kuaishou.aar] C:\Users\<USER>\.gradle\caches\transforms-3\6c5f8582ca0f6df83ee8a57074d55fc0\transformed\jetified-anythink_network_kuaishou\AndroidManifest.xml:13:5-14:19
MERGED from [anythink_network_kuaishou.aar] C:\Users\<USER>\.gradle\caches\transforms-3\6c5f8582ca0f6df83ee8a57074d55fc0\transformed\jetified-anythink_network_kuaishou\AndroidManifest.xml:13:5-14:19
MERGED from [anythink_network_mintegral_china.aar] C:\Users\<USER>\.gradle\caches\transforms-3\664ee24a4f6fbf622776a8ce662af7dd\transformed\jetified-anythink_network_mintegral_china\AndroidManifest.xml:13:5-14:19
MERGED from [anythink_network_mintegral_china.aar] C:\Users\<USER>\.gradle\caches\transforms-3\664ee24a4f6fbf622776a8ce662af7dd\transformed\jetified-anythink_network_mintegral_china\AndroidManifest.xml:13:5-14:19
MERGED from [anythink_network_mobrain_mix_plus.aar] C:\Users\<USER>\.gradle\caches\transforms-3\197b2095d494c640280cf459638165d1\transformed\jetified-anythink_network_mobrain_mix_plus\AndroidManifest.xml:10:5-20
MERGED from [anythink_network_mobrain_mix_plus.aar] C:\Users\<USER>\.gradle\caches\transforms-3\197b2095d494c640280cf459638165d1\transformed\jetified-anythink_network_mobrain_mix_plus\AndroidManifest.xml:10:5-20
MERGED from [anythink_network_sigmob.aar] C:\Users\<USER>\.gradle\caches\transforms-3\9bf89581943efb627f2b427a2c80676d\transformed\jetified-anythink_network_sigmob\AndroidManifest.xml:13:5-14:19
MERGED from [anythink_network_sigmob.aar] C:\Users\<USER>\.gradle\caches\transforms-3\9bf89581943efb627f2b427a2c80676d\transformed\jetified-anythink_network_sigmob\AndroidManifest.xml:13:5-14:19
MERGED from [anythink_rewardvideo.aar] C:\Users\<USER>\.gradle\caches\transforms-3\d370f4103a419bd03131f12381e4296b\transformed\jetified-anythink_rewardvideo\AndroidManifest.xml:11:5-20
MERGED from [anythink_rewardvideo.aar] C:\Users\<USER>\.gradle\caches\transforms-3\d370f4103a419bd03131f12381e4296b\transformed\jetified-anythink_rewardvideo\AndroidManifest.xml:11:5-20
MERGED from [anythink_splash.aar] C:\Users\<USER>\.gradle\caches\transforms-3\458f0753acc8e8706b494ac19de11b01\transformed\jetified-anythink_splash\AndroidManifest.xml:11:5-20
MERGED from [anythink_splash.aar] C:\Users\<USER>\.gradle\caches\transforms-3\458f0753acc8e8706b494ac19de11b01\transformed\jetified-anythink_splash\AndroidManifest.xml:11:5-20
MERGED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:9:5-37:19
MERGED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:9:5-37:19
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:26:5-47:19
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:26:5-47:19
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:27:5-84:19
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:27:5-84:19
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:27:5-192:19
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:27:5-192:19
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:31:5-119:19
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:31:5-119:19
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:33:5-89:19
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:33:5-89:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\ecca92267eba45ce3e43e495f0e1965d\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\ecca92267eba45ce3e43e495f0e1965d\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\395d71d697a2819f71d941e83c2d5fe8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\395d71d697a2819f71d941e83c2d5fe8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\580ce83cbd06ed986a537ce85c92ad08\transformed\jetified-glide-4.9.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\580ce83cbd06ed986a537ce85c92ad08\transformed\jetified-glide-4.9.0\AndroidManifest.xml:10:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [com.github.bumptech.glide:gifdecoder:4.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b865b04191c5f21eb914045e17fc0f4\transformed\jetified-gifdecoder-4.9.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b865b04191c5f21eb914045e17fc0f4\transformed\jetified-gifdecoder-4.9.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b21884de3c1fed91191658c9c59e73bb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b21884de3c1fed91191658c9c59e73bb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d338db99dbee4b2179b8774b799ae6a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d338db99dbee4b2179b8774b799ae6a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml
		REJECTED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:30:18-50
	android:requestLegacyExternalStorage
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:28:9-53
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:26:9-35
	android:label
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:24:9-41
	android:roundIcon
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:25:9-48
	android:icon
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:23:9-43
	android:allowBackup
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:22:9-36
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:27:9-40
	android:usesCleartextTraffic
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:16:18-53
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:21:9-38
uses-library#org.apache.http.legacy
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:30:9-32:40
	android:required
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:32:13-37
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:31:13-50
activity#com.example.concentration.SplashActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:70:9-79:20
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:72:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:74:13-36
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:73:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:71:13-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:75:13-78:29
action#android.intent.action.MAIN
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:76:17-69
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:76:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:77:17-77
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:77:27-74
activity#com.example.concentration.MainActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:81:9-84:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:82:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:84:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:83:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:81:19-47
activity#com.example.concentration.SchulteGridActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:86:9-90:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:88:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:90:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:89:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:87:13-48
activity#com.example.concentration.ThemeSelectionActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:91:9-95:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:93:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:95:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:94:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:92:13-51
activity#com.example.concentration.CountdownActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:96:9-100:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:98:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:100:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:99:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:97:13-46
activity#com.example.concentration.GameActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:101:9-105:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:103:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:105:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:104:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:102:13-41
activity#com.example.concentration.ColorTrainingActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:107:9-111:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:109:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:111:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:110:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:108:13-50
activity#com.example.concentration.ColorGameActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:112:9-116:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:114:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:116:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:115:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:113:13-46
activity#com.example.concentration.MemoryGameMainActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:117:9-121:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:119:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:121:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:120:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:118:13-51
activity#com.example.concentration.MemoryGameActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:122:9-126:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:124:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:126:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:125:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:123:13-47
activity#com.example.concentration.CelebrationActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:127:9-131:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:129:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:131:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:130:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:128:13-48
activity#com.example.concentration.MultiplicationTableActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:132:9-136:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:134:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:136:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:135:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:133:13-56
activity#com.example.concentration.MultiplicationConceptActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:137:9-141:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:139:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:141:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:140:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:138:13-58
activity#com.example.concentration.ConceptVisualDemoActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:142:9-146:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:144:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:146:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:145:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:143:13-54
activity#com.example.concentration.ConceptInteractiveCountActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:147:9-151:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:149:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:151:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:150:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:148:13-60
activity#com.example.concentration.ConceptAnimationDemoActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:152:9-156:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:154:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:156:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:155:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:153:13-57
activity#com.example.concentration.ConceptTestGameActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:157:9-161:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:159:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:161:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:160:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:158:13-52
activity#com.example.concentration.MultiplicationNumberActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:162:9-166:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:164:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:166:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:165:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:163:13-57
activity#com.example.concentration.NumberLearningActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:167:9-171:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:169:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:171:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:170:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:168:13-51
activity#com.example.concentration.MemoryCardGameActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:172:9-176:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:174:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:176:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:175:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:173:13-51
activity#com.example.concentration.RhythmLearningActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:177:9-181:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:179:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:181:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:180:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:178:13-51
activity#com.example.concentration.PuzzleChallengeActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:182:9-186:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:184:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:186:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:185:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:183:13-52
activity#com.example.concentration.MultiplicationChallengeActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:187:9-191:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:189:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:191:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:190:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:188:13-60
activity#com.example.concentration.SettingsActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:192:9-196:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:194:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:196:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:195:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:193:13-45
activity#com.example.concentration.TutorialActivity
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:197:9-201:40
	android:screenOrientation
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:199:13-49
	android:exported
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:201:13-37
	android:theme
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:200:13-44
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:198:13-45
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
	android:resource
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
	android:name
		ADDED from C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
uses-sdk
INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml
INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml
MERGED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:6:5-8:41
MERGED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:6:5-8:41
MERGED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:7:5-9:41
MERGED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:7:5-9:41
MERGED from [anythink_banner.aar] C:\Users\<USER>\.gradle\caches\transforms-3\1f2624d0c0c6e9f455af9dc863724ae0\transformed\jetified-anythink_banner\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_banner.aar] C:\Users\<USER>\.gradle\caches\transforms-3\1f2624d0c0c6e9f455af9dc863724ae0\transformed\jetified-anythink_banner\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_interstitial.aar] C:\Users\<USER>\.gradle\caches\transforms-3\b5cc6b602694d5ab809d4e95bc91f53e\transformed\jetified-anythink_interstitial\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_interstitial.aar] C:\Users\<USER>\.gradle\caches\transforms-3\b5cc6b602694d5ab809d4e95bc91f53e\transformed\jetified-anythink_interstitial\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_native.aar] C:\Users\<USER>\.gradle\caches\transforms-3\7af093fe7f17e0c3a647664cf25da2f8\transformed\jetified-anythink_native\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_native.aar] C:\Users\<USER>\.gradle\caches\transforms-3\7af093fe7f17e0c3a647664cf25da2f8\transformed\jetified-anythink_native\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_network_baidu.aar] C:\Users\<USER>\.gradle\caches\transforms-3\ce43e2aa2554c765f3cfa0634f5eda38\transformed\jetified-anythink_network_baidu\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_network_baidu.aar] C:\Users\<USER>\.gradle\caches\transforms-3\ce43e2aa2554c765f3cfa0634f5eda38\transformed\jetified-anythink_network_baidu\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_network_csj_mix.aar] C:\Users\<USER>\.gradle\caches\transforms-3\f64897484faa12ada527d54eb745b431\transformed\jetified-anythink_network_csj_mix\AndroidManifest.xml:6:5-8:41
MERGED from [anythink_network_csj_mix.aar] C:\Users\<USER>\.gradle\caches\transforms-3\f64897484faa12ada527d54eb745b431\transformed\jetified-anythink_network_csj_mix\AndroidManifest.xml:6:5-8:41
MERGED from [anythink_network_gdt.aar] C:\Users\<USER>\.gradle\caches\transforms-3\5f1f9bbf883cfa1220d07503d95cf1ac\transformed\jetified-anythink_network_gdt\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_network_gdt.aar] C:\Users\<USER>\.gradle\caches\transforms-3\5f1f9bbf883cfa1220d07503d95cf1ac\transformed\jetified-anythink_network_gdt\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_network_kuaishou.aar] C:\Users\<USER>\.gradle\caches\transforms-3\6c5f8582ca0f6df83ee8a57074d55fc0\transformed\jetified-anythink_network_kuaishou\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_network_kuaishou.aar] C:\Users\<USER>\.gradle\caches\transforms-3\6c5f8582ca0f6df83ee8a57074d55fc0\transformed\jetified-anythink_network_kuaishou\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_network_mintegral_china.aar] C:\Users\<USER>\.gradle\caches\transforms-3\664ee24a4f6fbf622776a8ce662af7dd\transformed\jetified-anythink_network_mintegral_china\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_network_mintegral_china.aar] C:\Users\<USER>\.gradle\caches\transforms-3\664ee24a4f6fbf622776a8ce662af7dd\transformed\jetified-anythink_network_mintegral_china\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_network_mobrain_mix_plus.aar] C:\Users\<USER>\.gradle\caches\transforms-3\197b2095d494c640280cf459638165d1\transformed\jetified-anythink_network_mobrain_mix_plus\AndroidManifest.xml:6:5-8:41
MERGED from [anythink_network_mobrain_mix_plus.aar] C:\Users\<USER>\.gradle\caches\transforms-3\197b2095d494c640280cf459638165d1\transformed\jetified-anythink_network_mobrain_mix_plus\AndroidManifest.xml:6:5-8:41
MERGED from [anythink_network_sigmob.aar] C:\Users\<USER>\.gradle\caches\transforms-3\9bf89581943efb627f2b427a2c80676d\transformed\jetified-anythink_network_sigmob\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_network_sigmob.aar] C:\Users\<USER>\.gradle\caches\transforms-3\9bf89581943efb627f2b427a2c80676d\transformed\jetified-anythink_network_sigmob\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_rewardvideo.aar] C:\Users\<USER>\.gradle\caches\transforms-3\d370f4103a419bd03131f12381e4296b\transformed\jetified-anythink_rewardvideo\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_rewardvideo.aar] C:\Users\<USER>\.gradle\caches\transforms-3\d370f4103a419bd03131f12381e4296b\transformed\jetified-anythink_rewardvideo\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_splash.aar] C:\Users\<USER>\.gradle\caches\transforms-3\458f0753acc8e8706b494ac19de11b01\transformed\jetified-anythink_splash\AndroidManifest.xml:6:5-9:36
MERGED from [anythink_splash.aar] C:\Users\<USER>\.gradle\caches\transforms-3\458f0753acc8e8706b494ac19de11b01\transformed\jetified-anythink_splash\AndroidManifest.xml:6:5-9:36
MERGED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:7:5-44
MERGED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:7:5-44
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:15:5-17:41
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:15:5-17:41
MERGED from [beizi_fusion_sdk_4.90.4.36.aar] C:\Users\<USER>\.gradle\caches\transforms-3\edb8951c7f25448943e5f159ccbca8a9\transformed\jetified-beizi_fusion_sdk_4.90.4.36\AndroidManifest.xml:7:5-9:41
MERGED from [beizi_fusion_sdk_4.90.4.36.aar] C:\Users\<USER>\.gradle\caches\transforms-3\edb8951c7f25448943e5f159ccbca8a9\transformed\jetified-beizi_fusion_sdk_4.90.4.36\AndroidManifest.xml:7:5-9:41
MERGED from [by_5.05.10.aar] C:\Users\<USER>\.gradle\caches\transforms-3\9b2c95f474c8e1c18611376b46d3f6ce\transformed\jetified-by_5.05.10\AndroidManifest.xml:7:5-9:41
MERGED from [by_5.05.10.aar] C:\Users\<USER>\.gradle\caches\transforms-3\9b2c95f474c8e1c18611376b46d3f6ce\transformed\jetified-by_5.05.10\AndroidManifest.xml:7:5-9:41
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:6:5-44
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:6:5-44
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:8:5-11:50
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:8:5-11:50
MERGED from [mediation_baidu_adapter_9.3905.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\f93b0c9554f067ea285686fdb3458d41\transformed\jetified-mediation_baidu_adapter_9.3905.1\AndroidManifest.xml:7:5-9:41
MERGED from [mediation_baidu_adapter_9.3905.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\f93b0c9554f067ea285686fdb3458d41\transformed\jetified-mediation_baidu_adapter_9.3905.1\AndroidManifest.xml:7:5-9:41
MERGED from [mediation_gdt_adapter_4.640.1510.0.aar] C:\Users\<USER>\.gradle\caches\transforms-3\86709dcc5f68b50a22bb06f51f8d0da4\transformed\jetified-mediation_gdt_adapter_4.640.1510.0\AndroidManifest.xml:7:5-9:41
MERGED from [mediation_gdt_adapter_4.640.1510.0.aar] C:\Users\<USER>\.gradle\caches\transforms-3\86709dcc5f68b50a22bb06f51f8d0da4\transformed\jetified-mediation_gdt_adapter_4.640.1510.0\AndroidManifest.xml:7:5-9:41
MERGED from [mediation_ks_adapter_3.3.76.5.0.aar] C:\Users\<USER>\.gradle\caches\transforms-3\f5969883c37f11fce5d76b1f98a8e00e\transformed\jetified-mediation_ks_adapter_3.3.76.5.0\AndroidManifest.xml:7:5-9:41
MERGED from [mediation_ks_adapter_3.3.76.5.0.aar] C:\Users\<USER>\.gradle\caches\transforms-3\f5969883c37f11fce5d76b1f98a8e00e\transformed\jetified-mediation_ks_adapter_3.3.76.5.0\AndroidManifest.xml:7:5-9:41
MERGED from [mediation_mintegral_adapter_16.6.57.9.aar] C:\Users\<USER>\.gradle\caches\transforms-3\18770e105eb019778236642398edc4d4\transformed\jetified-mediation_mintegral_adapter_16.6.57.9\AndroidManifest.xml:7:5-9:41
MERGED from [mediation_mintegral_adapter_16.6.57.9.aar] C:\Users\<USER>\.gradle\caches\transforms-3\18770e105eb019778236642398edc4d4\transformed\jetified-mediation_mintegral_adapter_16.6.57.9\AndroidManifest.xml:7:5-9:41
MERGED from [mediation_sigmob_adapter_4.22.2.0.aar] C:\Users\<USER>\.gradle\caches\transforms-3\874c3f359609fd3ecb5bd9b8f8eaf2a8\transformed\jetified-mediation_sigmob_adapter_4.22.2.0\AndroidManifest.xml:7:5-9:41
MERGED from [mediation_sigmob_adapter_4.22.2.0.aar] C:\Users\<USER>\.gradle\caches\transforms-3\874c3f359609fd3ecb5bd9b8f8eaf2a8\transformed\jetified-mediation_sigmob_adapter_4.22.2.0\AndroidManifest.xml:7:5-9:41
MERGED from [oaid_sdk_1.0.25.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01bc7fca446bdde30d72de23c77cdbfa\transformed\jetified-oaid_sdk_1.0.25\AndroidManifest.xml:8:5-11:63
MERGED from [oaid_sdk_1.0.25.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01bc7fca446bdde30d72de23c77cdbfa\transformed\jetified-oaid_sdk_1.0.25\AndroidManifest.xml:8:5-11:63
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:4:5-118
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:4:5-118
MERGED from [wind-common-1.7.7.aar] C:\Users\<USER>\.gradle\caches\transforms-3\9bea298525678d2d8b769291a94b3fad\transformed\jetified-wind-common-1.7.7\AndroidManifest.xml:5:5-7:41
MERGED from [wind-common-1.7.7.aar] C:\Users\<USER>\.gradle\caches\transforms-3\9bea298525678d2d8b769291a94b3fad\transformed\jetified-wind-common-1.7.7\AndroidManifest.xml:5:5-7:41
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:8:5-10:41
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:8:5-10:41
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\ecca92267eba45ce3e43e495f0e1965d\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\ecca92267eba45ce3e43e495f0e1965d\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\395d71d697a2819f71d941e83c2d5fe8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\395d71d697a2819f71d941e83c2d5fe8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a6e5f6212cc01c97226962f50ee2288b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a6e5f6212cc01c97226962f50ee2288b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\38fdf4e56534b3c4c6a8297bf7eeddee\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\38fdf4e56534b3c4c6a8297bf7eeddee\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8bdece1432d6bd447ee6e396aae1f67\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8bdece1432d6bd447ee6e396aae1f67\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-3\24d8c27b9d89ea399a191c23392817f3\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-3\24d8c27b9d89ea399a191c23392817f3\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f4c5009e19be168bbbd44bc6887a962\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\2f4c5009e19be168bbbd44bc6887a962\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9007ca337390510c83423ff4c37ff3cd\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9007ca337390510c83423ff4c37ff3cd\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [com.github.bumptech.glide:glide:4.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\580ce83cbd06ed986a537ce85c92ad08\transformed\jetified-glide-4.9.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\580ce83cbd06ed986a537ce85c92ad08\transformed\jetified-glide-4.9.0\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e056b10f26770d4e3dd34feeb1354a3c\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e056b10f26770d4e3dd34feeb1354a3c\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\c3dc071c2c0ff1053390120e17d4afaf\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\c3dc071c2c0ff1053390120e17d4afaf\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\a99a550775fc239fa35ebbba720a5cdf\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\a99a550775fc239fa35ebbba720a5cdf\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fcc853f08bc42045cd980d63acc7c5a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fcc853f08bc42045cd980d63acc7c5a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b6ed83c756fa81836783bb8a55d86113\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b6ed83c756fa81836783bb8a55d86113\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2c0baf04ac6533147f9384ef4e31969\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2c0baf04ac6533147f9384ef4e31969\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1abb1035bb8a9c4eb057e4213de35400\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1abb1035bb8a9c4eb057e4213de35400\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1743650eff319645d518a855bfe1fe5a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1743650eff319645d518a855bfe1fe5a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\90714f149f25b0cfa5dbdeb0b88fec36\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\90714f149f25b0cfa5dbdeb0b88fec36\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ee8791f02be5799e6df9830af15f939\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ee8791f02be5799e6df9830af15f939\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dedf2eb2b169583c131497feb19f69d4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\dedf2eb2b169583c131497feb19f69d4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b473ba004de6f7fc4ee8164a18a79bd2\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b473ba004de6f7fc4ee8164a18a79bd2\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16d49e6813d7c6aef7e67bde9a0987a7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16d49e6813d7c6aef7e67bde9a0987a7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\90dbda2796d0082100feb9bfc0838eaa\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\90dbda2796d0082100feb9bfc0838eaa\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\85bca364794f8d628710ba163dcadda2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\85bca364794f8d628710ba163dcadda2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\90d8c277878f3e93aea5f5378d390fb3\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\90d8c277878f3e93aea5f5378d390fb3\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\59be872dd741da2aefad29199a5c688e\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\59be872dd741da2aefad29199a5c688e\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1b340f1aca83c1e882ac68b79d8d0df7\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1b340f1aca83c1e882ac68b79d8d0df7\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d1da6a6161390c1e21c57807d5a91b4\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d1da6a6161390c1e21c57807d5a91b4\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8eec3305fa08ab6c364850fac4f2f3da\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8eec3305fa08ab6c364850fac4f2f3da\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\c6b758a0250d1d72a62de869337013f9\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\c6b758a0250d1d72a62de869337013f9\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\a419870f824a5ed52312905adad0dfd9\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\a419870f824a5ed52312905adad0dfd9\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\48226b2b3c5a88035ae96e2d0ebf97e5\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\48226b2b3c5a88035ae96e2d0ebf97e5\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb5fac538ce26b1dc83d6f236aa3293c\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb5fac538ce26b1dc83d6f236aa3293c\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e691780b60fbd82fe9bef3ba34a4f1e1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e691780b60fbd82fe9bef3ba34a4f1e1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e8d651961ab3a64fe5e9a922dcb7cc07\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e8d651961ab3a64fe5e9a922dcb7cc07\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3733f843a5ee849fe02ab2bf462dfa2\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3733f843a5ee849fe02ab2bf462dfa2\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\738310a87d9925967294fafc069fcc67\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\738310a87d9925967294fafc069fcc67\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b865b04191c5f21eb914045e17fc0f4\transformed\jetified-gifdecoder-4.9.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b865b04191c5f21eb914045e17fc0f4\transformed\jetified-gifdecoder-4.9.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b21884de3c1fed91191658c9c59e73bb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b21884de3c1fed91191658c9c59e73bb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d338db99dbee4b2179b8774b799ae6a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d338db99dbee4b2179b8774b799ae6a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\85cd030943f9498f5b64cfe78341395f\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\85cd030943f9498f5b64cfe78341395f\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b7916a1392bbb1cace393bfa310f2e1c\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b7916a1392bbb1cace393bfa310f2e1c\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5271149ab19ed6165189410cef969bfe\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5271149ab19ed6165189410cef969bfe\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae6d865693f1ebb006f8a0018c2b43a8\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae6d865693f1ebb006f8a0018c2b43a8\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3361067d52275e58bda1185bd0e0722\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3361067d52275e58bda1185bd0e0722\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c59bd9210ecc79c0b9d6f2393b2a4a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c59bd9210ecc79c0b9d6f2393b2a4a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d240c2876715f64c8f86f348098b6d9\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d240c2876715f64c8f86f348098b6d9\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	tools:overrideLibrary
		ADDED from [anythink_banner.aar] C:\Users\<USER>\.gradle\caches\transforms-3\1f2624d0c0c6e9f455af9dc863724ae0\transformed\jetified-anythink_banner\AndroidManifest.xml:9:9-33
	android:targetSdkVersion
		INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\专注力训练\app\src\main\AndroidManifest.xml
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:13:5-83
MERGED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:23:5-83
MERGED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:23:5-83
MERGED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:15:5-83
MERGED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:15:5-83
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:24:5-83
MERGED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:24:5-83
MERGED from [beizi_fusion_sdk_4.90.4.36.aar] C:\Users\<USER>\.gradle\caches\transforms-3\edb8951c7f25448943e5f159ccbca8a9\transformed\jetified-beizi_fusion_sdk_4.90.4.36\AndroidManifest.xml:16:5-83
MERGED from [beizi_fusion_sdk_4.90.4.36.aar] C:\Users\<USER>\.gradle\caches\transforms-3\edb8951c7f25448943e5f159ccbca8a9\transformed\jetified-beizi_fusion_sdk_4.90.4.36\AndroidManifest.xml:16:5-83
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:10:5-83
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:10:5-83
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:19:5-83
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:19:5-83
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:16:5-83
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:16:5-83
	android:name
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:13:22-80
activity#com.alliance.ssp.ad.activity.SAAllianceWebViewActivity
ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:17:9-20:47
	android:launchMode
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:20:13-44
	android:configChanges
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:19:13-74
	android:name
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:18:13-82
activity#com.alliance.ssp.ad.activity.NMRewardVideoActivity
ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:21:9-25:52
	android:screenOrientation
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:25:13-49
	android:launchMode
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:24:13-44
	android:configChanges
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:23:13-74
	android:name
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:22:13-78
activity#com.alliance.ssp.ad.activity.AppInfoViewActivity
ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:26:9-30:64
	android:launchMode
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:29:13-44
	android:configChanges
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:28:13-74
	android:theme
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:30:13-61
	android:name
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:27:13-76
service#com.alliance.ssp.ad.service.YTSDKDownloadService
ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:33:13-76
activity#com.alliance.ssp.ad.activity.DeepLinkProxyActivity
ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:36:9-40:64
	android:launchMode
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:39:13-44
	android:configChanges
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:38:13-74
	android:theme
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:40:13-61
	android:name
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:37:13-78
provider#com.alliance.ssp.ad.utils.NMSSPFileProvider
ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:42:9-50:20
	android:grantUriPermissions
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:46:13-47
	android:authorities
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:44:13-69
	android:exported
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:45:13-37
	android:name
		ADDED from [adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:43:13-71
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:15:5-76
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:28:5-75
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:28:5-75
	android:name
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:15:22-73
uses-permission#android.permission.GET_TASKS
ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:17:5-68
	android:name
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:17:22-65
uses-permission#android.permission.WAKE_LOCK
ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:25:5-68
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:29:5-67
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:29:5-67
	android:name
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:25:22-65
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:28:5-77
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:11:5-77
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:11:5-77
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:19:5-21:53
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:19:5-21:53
	tools:ignore
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:21:9-50
	android:name
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:28:22-74
activity#com.ads.admob.saas.SaasH5Activity
ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:31:9-35:52
	android:screenOrientation
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:35:13-49
	android:exported
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:34:13-37
	android:configChanges
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:33:13-68
	android:name
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:32:13-61
activity#com.ads.admob.saas.H5Activity
ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:36:9-40:52
	android:screenOrientation
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:40:13-49
	android:exported
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:39:13-37
	android:configChanges
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:38:13-68
	android:name
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:37:13-57
activity#com.ads.admob.saas.GameH5Activity
ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:41:9-45:52
	android:screenOrientation
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:45:13-49
	android:exported
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:44:13-37
	android:configChanges
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:43:13-68
	android:name
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:42:13-61
activity#com.ads.admob.saas.YmActivity
ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:46:9-50:52
	android:screenOrientation
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:50:13-49
	android:exported
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:49:13-37
	android:configChanges
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:48:13-68
	android:name
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:47:13-57
activity#com.ads.admob.saas.VideoFragmentActivity
ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:51:9-55:52
	android:screenOrientation
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:55:13-49
	android:exported
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:54:13-37
	android:configChanges
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:53:13-68
	android:name
		ADDED from [admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:52:13-68
activity#com.anythink.china.activity.TransparentActivity
ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:18:9-21:86
	android:exported
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:20:13-37
	android:theme
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:21:13-83
	android:name
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:19:13-75
activity#com.anythink.dlopt.activity.ApkConfirmDialogActivity
ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:22:9-25:86
	android:exported
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:24:13-37
	android:theme
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:25:13-83
	android:name
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:23:13-80
provider#com.anythink.dlopt.common.ApkFileProvider
ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:27:9-35:20
	android:grantUriPermissions
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:31:13-47
	android:authorities
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:29:13-73
	android:exported
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:28:13-69
receiver#com.anythink.dlopt.common.NotificationBroadcastReceiver
ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:37:9-44:20
	android:exported
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:39:13-37
	android:name
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:38:13-83
intent-filter#action:name:anythink_action_notification_cancel+action:name:anythink_action_notification_click
ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:40:13-43:29
action#anythink_action_notification_click
ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:41:17-77
	android:name
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:41:25-74
action#anythink_action_notification_cancel
ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:42:17-78
	android:name
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:42:25-75
service#com.anythink.dlopt.common.service.ApkDownloadService
ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:46:9-48:40
	android:exported
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:48:13-37
	android:name
		ADDED from [anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:47:13-80
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:14:5-79
	android:name
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:14:22-76
queries
ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:16:5-22:15
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:13:5-19:15
MERGED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:13:5-19:15
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:12:5-20:15
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:12:5-20:15
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:23:5-31:15
MERGED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:23:5-31:15
intent#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:17:9-21:18
activity#com.anythink.core.activity.AnyThinkGdprAuthActivity
ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:25:9-30:74
	android:launchMode
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:29:13-44
	android:exported
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:28:13-37
	android:configChanges
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:27:13-87
	android:theme
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:30:13-71
	android:name
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:26:13-79
activity#com.anythink.basead.ui.ATLandscapeActivity
ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:31:9-36:74
	android:launchMode
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:35:13-42
	android:exported
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:34:13-37
	android:configChanges
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:33:13-87
	android:theme
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:36:13-71
	android:name
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:32:13-70
activity#com.anythink.basead.ui.ATPortraitActivity
ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:37:9-42:74
	android:launchMode
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:41:13-42
	android:exported
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:40:13-37
	android:configChanges
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:39:13-87
	android:theme
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:42:13-71
	android:name
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:38:13-69
activity#com.anythink.basead.ui.ATLandscapeTranslucentActivity
ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:43:9-48:67
	android:launchMode
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:47:13-42
	android:exported
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:46:13-37
	android:configChanges
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:45:13-87
	android:theme
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:48:13-64
	android:name
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:44:13-81
activity#com.anythink.basead.ui.ATPortraitTranslucentActivity
ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:49:9-54:67
	android:launchMode
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:53:13-42
	android:exported
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:52:13-37
	android:configChanges
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:51:13-87
	android:theme
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:54:13-64
	android:name
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:50:13-80
activity#com.anythink.core.basead.ui.web.WebLandPageActivity
ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:55:9-61:58
	android:launchMode
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:59:13-43
	android:windowSoftInputMode
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:61:13-55
	android:exported
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:58:13-37
	android:configChanges
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:57:13-87
	android:theme
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:60:13-66
	android:name
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:56:13-79
activity#com.anythink.basead.ui.RewardExitConfirmDialogActivity
ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:62:9-65:86
	android:exported
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:64:13-37
	android:theme
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:65:13-83
	android:name
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:63:13-82
activity#com.anythink.basead.ui.activity.ATMixSplashActivity
ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:66:9-71:74
	android:launchMode
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:70:13-42
	android:exported
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:69:13-37
	android:configChanges
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:68:13-83
	android:theme
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:71:13-71
	android:name
		ADDED from [anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:67:13-79
provider#com.baidu.mobads.sdk.api.BdFileProvider
ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:14:9-22:20
	android:grantUriPermissions
		ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:18:13-47
	android:authorities
		ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:16:13-63
	android:exported
		ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:17:13-37
	android:name
		ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:15:13-67
activity#com.baidu.mobads.sdk.api.AppActivity
ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:24:9-27:63
	android:configChanges
		ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:26:13-83
	android:theme
		ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:27:13-60
	android:name
		ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:25:13-64
activity#com.baidu.mobads.sdk.api.MobRewardVideoActivity
ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:29:9-32:75
	android:configChanges
		ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:31:13-81
	android:theme
		ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:32:13-72
	android:name
		ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:30:13-75
activity#com.baidu.mobads.sdk.api.BdShellActivity
ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:33:9-36:75
	android:configChanges
		ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:35:13-115
	android:theme
		ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:36:13-72
	android:name
		ADDED from [Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:34:13-68
activity#com.beizi.ad.AdActivity
ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:27:9-31:50
	android:hardwareAccelerated
		ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:31:13-47
	android:exported
		ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:30:13-37
	android:configChanges
		ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:29:13-122
	android:name
		ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:28:13-51
activity#com.beizi.ad.internal.activity.BeiZiInterstitialActivity
ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:32:9-35:61
	android:exported
		ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:35:13-58
	android:name
		ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:33:13-84
activity#com.beizi.ad.internal.activity.DownloadAppInfoActivity
ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:37:13-82
activity#com.beizi.ad.internal.activity.BeiZiDownloadDialogActivity
ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:39:9-42:61
	android:exported
		ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:41:13-37
	android:theme
		ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:42:13-58
	android:name
		ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:40:13-86
service#com.beizi.ad.DownloadService
ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:44:9-46:40
	android:exported
		ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:46:13-37
	android:name
		ADDED from [beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:45:13-56
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:8:5-79
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:6:5-78
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:6:5-78
	android:name
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:8:22-76
uses-permission#android.permission.REORDER_TASKS
ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:24:22-69
uses-permission#android.permission.VIBRATE
ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:25:5-66
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:25:5-66
MERGED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:25:5-66
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:26:5-65
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:26:5-65
	android:name
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:25:22-63
service#com.qq.e.comm.DownloadService
ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:32:9-35:43
	android:multiprocess
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:35:13-40
	android:exported
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:33:13-57
activity#com.qq.e.ads.ADActivity
ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:37:9-41:43
	android:multiprocess
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:41:13-40
	android:exported
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:40:13-37
	android:configChanges
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:39:13-83
	android:name
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:38:13-51
activity#com.qq.e.ads.PortraitADActivity
ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:42:9-48:69
	android:screenOrientation
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:47:13-49
	android:multiprocess
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:46:13-40
	android:exported
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:45:13-37
	android:configChanges
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:44:13-83
	android:theme
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:48:13-66
	android:name
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:43:13-59
activity#com.qq.e.ads.LandscapeADActivity
ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:49:9-55:69
	android:screenOrientation
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:54:13-56
	android:multiprocess
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:53:13-40
	android:exported
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:52:13-37
	android:configChanges
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:51:13-83
	android:theme
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:55:13-66
	android:name
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:50:13-60
activity#com.qq.e.ads.RewardvideoPortraitADActivity
ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:57:9-66:20
	android:multiprocess
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:61:13-40
	android:exported
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:60:13-37
	android:configChanges
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:59:13-83
	android:theme
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:62:13-66
	android:name
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:58:13-70
meta-data#android.notch_support
ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:63:13-65:40
	android:value
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:65:17-37
	android:name
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:64:17-53
activity#com.qq.e.ads.RewardvideoLandscapeADActivity
ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:68:9-78:20
	android:screenOrientation
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:73:13-50
	android:multiprocess
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:72:13-40
	android:exported
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:71:13-37
	android:configChanges
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:70:13-83
	android:theme
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:74:13-66
	android:name
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:69:13-71
activity#com.qq.e.ads.DialogActivity
ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:79:9-83:86
	android:multiprocess
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:82:13-40
	android:exported
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:81:13-37
	android:theme
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:83:13-83
	android:name
		ADDED from [GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:80:13-55
uses-permission#android.permission.SET_WALLPAPER
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:20:5-72
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:20:22-69
uses-permission#android.permission.BLUETOOTH
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:24:5-68
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:24:22-65
activity#com.kwad.sdk.api.proxy.app.AdWebViewActivity
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:28:9-32:69
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:31:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:30:13-74
	android:theme
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:32:13-66
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:29:13-72
activity#com.kwad.sdk.api.proxy.app.KsFullScreenVideoActivity
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:33:9-37:80
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:36:13-47
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:35:13-74
	android:theme
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:37:13-77
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:34:13-80
activity#com.kwad.sdk.api.proxy.app.KsFullScreenLandScapeVideoActivity
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:38:9-42:80
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:41:13-50
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:40:13-74
	android:theme
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:42:13-77
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:39:13-89
activity#com.kwad.sdk.api.proxy.app.KsRewardVideoActivity
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:43:9-47:80
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:46:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:45:13-74
	android:theme
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:47:13-77
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:44:13-76
activity#com.kwad.sdk.api.proxy.app.KSRewardLandScapeVideoActivity
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:48:9-52:80
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:51:13-50
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:50:13-74
	android:theme
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:52:13-77
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:49:13-85
activity#com.kwad.sdk.api.proxy.app.FeedDownloadActivity
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:53:9-57:80
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:56:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:55:13-74
	android:theme
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:57:13-77
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:54:13-75
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$KsTrendsActivity
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:58:9-62:69
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:61:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:60:13-74
	android:theme
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:62:13-66
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:59:13-92
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$ProfileHomeActivity
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:63:9-66:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:66:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:65:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:64:13-95
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$GoodsPlayBackActivity
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:67:9-70:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:70:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:69:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:68:13-97
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$ProfileVideoDetailActivity
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:71:9-74:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:74:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:73:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:72:13-102
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$TubeProfileActivity
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:75:9-78:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:78:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:77:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:76:13-95
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$ChannelDetailActivity
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:79:9-82:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:82:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:81:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:80:13-97
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$TubeDetailActivity
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:83:9-86:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:86:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:85:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:84:13-94
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$EpisodeDetailActivity
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:87:9-90:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:90:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:89:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:88:13-97
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity1
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:91:9-95:80
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:94:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:93:13-74
	android:theme
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:95:13-77
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:92:13-93
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity2
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:96:9-100:80
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:99:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:98:13-74
	android:theme
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:100:13-77
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:97:13-93
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity3
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:101:9-105:80
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:104:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:103:13-74
	android:theme
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:105:13-77
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:102:13-93
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity4
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:106:9-109:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:109:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:108:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:107:13-93
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity5
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:110:9-113:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:113:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:112:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:111:13-93
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity6
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:114:9-117:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:117:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:116:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:115:13-93
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity7
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:118:9-121:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:121:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:120:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:119:13-93
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity8
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:122:9-125:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:125:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:124:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:123:13-93
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity9
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:126:9-129:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:129:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:128:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:127:13-93
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity10
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:130:9-133:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:133:13-49
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:132:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:131:13-94
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleTop1
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:134:9-138:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:138:13-49
	android:launchMode
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:137:13-43
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:136:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:135:13-102
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleTop2
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:139:9-143:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:143:13-49
	android:launchMode
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:142:13-43
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:141:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:140:13-102
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleInstance1
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:144:9-148:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:148:13-49
	android:launchMode
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:147:13-48
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:146:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:145:13-107
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleInstance2
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:149:9-153:52
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:153:13-49
	android:launchMode
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:152:13-48
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:151:13-74
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:150:13-107
activity#com.kwad.sdk.api.proxy.app.BaseFragmentActivity$DeveloperConfigActivity
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:154:9-159:80
	android:screenOrientation
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:158:13-49
	android:launchMode
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:157:13-48
	android:configChanges
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:156:13-74
	android:theme
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:159:13-77
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:155:13-99
service#com.kwad.sdk.api.proxy.app.FileDownloadService$SharedMainProcessService
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:161:9-107
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:161:18-104
service#com.kwad.sdk.api.proxy.app.FileDownloadService$SeparateProcessService
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:162:9-105
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:162:18-102
service#com.kwad.sdk.api.proxy.app.DownloadService
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:163:9-165:40
	android:exported
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:165:13-37
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:164:13-70
service#com.kwad.sdk.api.proxy.app.ServiceProxyRemote
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:166:9-169:47
	android:process
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:169:13-44
	android:exported
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:168:13-37
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:167:13-73
service#com.kwad.sdk.api.proxy.VideoWallpaperService
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:170:9-181:19
	android:exported
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:172:13-36
	android:permission
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:173:13-67
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:171:13-72
intent-filter#action:name:android.service.wallpaper.WallpaperService
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:174:13-176:29
	android:priority
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:174:28-51
action#android.service.wallpaper.WallpaperService
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:175:17-85
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:175:25-82
meta-data#android.service.wallpaper
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:178:13-180:58
	android:resource
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:180:17-55
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:179:17-57
provider#com.kwad.sdk.api.proxy.app.AdSdkFileProvider
ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:183:9-191:20
	android:grantUriPermissions
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:187:13-47
	android:authorities
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:185:13-66
	android:exported
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:186:13-37
	android:name
		ADDED from [kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:184:13-72
uses-permission#com.asus.msa.SupplementaryDID.ACCESS
ADDED from [oaid_sdk_1.0.25.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01bc7fca446bdde30d72de23c77cdbfa\transformed\jetified-oaid_sdk_1.0.25\AndroidManifest.xml:13:5-76
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:8:5-75
MERGED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:8:5-75
	android:name
		ADDED from [oaid_sdk_1.0.25.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01bc7fca446bdde30d72de23c77cdbfa\transformed\jetified-oaid_sdk_1.0.25\AndroidManifest.xml:13:22-73
uses-permission#freemme.permission.msa
ADDED from [oaid_sdk_1.0.25.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01bc7fca446bdde30d72de23c77cdbfa\transformed\jetified-oaid_sdk_1.0.25\AndroidManifest.xml:15:5-62
	android:name
		ADDED from [oaid_sdk_1.0.25.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01bc7fca446bdde30d72de23c77cdbfa\transformed\jetified-oaid_sdk_1.0.25\AndroidManifest.xml:15:22-59
uses-permission#${applicationId}.openadsdk.permission.TT_PANGOLIN
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:7:5-88
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:7:22-86
uses-permission#com.example.concentration.openadsdk.permission.TT_PANGOLIN
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:7:5-88
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:7:22-86
permission#${applicationId}.openadsdk.permission.TT_PANGOLIN
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:10:5-119
	android:protectionLevel
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:10:82-117
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:10:17-81
permission#com.example.concentration.openadsdk.permission.TT_PANGOLIN
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:10:5-119
	android:protectionLevel
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:10:82-117
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:10:17-81
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:13:9-19:18
action#android.intent.action.VIEW
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:14:13-64
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:14:21-62
category#android.intent.category.BROWSABLE
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:16:13-73
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:16:23-71
data
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:18:13-43
	android:scheme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:18:19-41
meta-data#ZEUS_PLUGIN_PANGLE
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:32:9-291
	android:value
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:32:54-289
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:32:20-53
meta-data#ZEUS_PLUGIN_com.byted.csj.ext
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:33:9-248
	android:value
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:33:65-246
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:33:20-64
activity#com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Activity
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:9-331
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:197-226
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:275-329
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:172-196
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:99-171
	android:theme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:227-274
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:19-98
activity#com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Portrait_Activity
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:9-384
	android:screenOrientation
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:243-279
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:213-242
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:328-382
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:188-212
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:108-187
	android:theme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:280-327
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:19-107
activity#com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Activity_T
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:9-334
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:199-228
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:278-332
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:174-198
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:101-173
	android:theme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:229-277
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:19-100
activity#com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Landscape_Activity
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:9-386
	android:screenOrientation
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:244-281
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:214-243
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:330-384
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:189-213
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:109-188
	android:theme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:282-329
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:19-108
activity#com.bytedance.sdk.openadsdk.stub.activity.Stub_Activity
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:9-292
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:236-290
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:163-187
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:90-162
	android:theme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:188-235
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:19-89
activity#com.bytedance.sdk.openadsdk.stub.activity.Stub_SingleTask_Activity_T
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:9-338
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:201-232
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:282-336
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:176-200
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:103-175
	android:theme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:233-281
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:19-102
activity#com.bytedance.sdk.openadsdk.stub.activity.Stub_SingleTask_Activity
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:9-287
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:199-230
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:231-285
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:174-198
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:101-173
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:19-100
provider#com.bytedance.sdk.openadsdk.stub.server.DownloaderServerManager
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:43:9-260
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:43:229-258
	android:authorities
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:43:98-203
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:43:204-228
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:43:19-97
provider#com.bytedance.sdk.openadsdk.stub.server.MainServerManager
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:44:9-183
	android:authorities
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:44:92-156
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:44:157-181
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:44:19-91
provider#com.bytedance.pangle.provider.MainProcessProviderProxy
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:47:9-181
	android:authorities
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:47:89-154
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:47:155-179
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:47:19-88
provider#com.bytedance.pangle.FileProvider
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:48:9-188
	android:grantUriPermissions
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:48:127-161
	android:authorities
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:48:68-126
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:48:162-186
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:48:19-67
activity#com.ss.android.downloadlib.addownload.compliance.AppPrivacyPolicyActivity
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:50:9-134
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:50:108-132
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:50:19-107
activity#com.ss.android.downloadlib.addownload.compliance.AppDetailInfoActivity
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:51:9-131
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:51:105-129
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:51:19-104
activity#com.ss.android.downloadlib.activity.TTDelegateActivity
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:52:9-207
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:52:89-120
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:52:181-205
	android:theme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:52:121-180
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:52:19-88
activity#com.ss.android.downloadlib.activity.JumpKllkActivity
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:53:9-205
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:53:87-118
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:53:179-203
	android:theme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:53:119-178
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:53:19-86
receiver#com.ss.android.downloadlib.core.download.DownloadReceiver
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:55:9-118
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:55:92-116
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:55:19-91
service#com.ss.android.socialbase.appdownloader.DownloadHandlerService
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:56:9-150
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:56:124-148
	android:stopWithTask
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:56:96-123
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:56:18-95
activity#com.ss.android.socialbase.appdownloader.view.DownloadTaskDeleteActivity
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:58:9-208
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:58:106-137
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:58:182-206
	android:theme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:58:138-181
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:58:19-105
activity#com.ss.android.socialbase.appdownloader.view.JumpUnknownSourceActivity
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:59:9-207
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:59:105-136
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:59:181-205
	android:theme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:59:137-180
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:59:19-104
service#com.ss.android.socialbase.appdownloader.RetryJobSchedulerService
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:9-232
	android:enabled
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:98-120
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:121-145
	android:permission
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:146-202
	android:stopWithTask
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:203-230
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:18-97
service#com.ss.android.socialbase.downloader.downloader.IndependentProcessDownloadService
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:62:9-66:19
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:62:140-169
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:62:115-139
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:62:18-114
intent-filter#action:name:com.ss.android.socialbase.downloader.remote
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:63:13-65:29
action#com.ss.android.socialbase.downloader.remote
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:64:17-85
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:64:25-83
service#com.ss.android.socialbase.downloader.notification.DownloadNotificationService
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:67:9-165
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:67:139-163
	android:stopWithTask
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:67:111-138
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:67:18-110
service#com.ss.android.socialbase.downloader.downloader.DownloadService
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:68:9-151
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:68:125-149
	android:stopWithTask
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:68:97-124
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:68:18-96
service#com.ss.android.socialbase.downloader.impls.DownloadHandleService
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:69:9-152
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:69:126-150
	android:stopWithTask
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:69:98-125
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:69:18-97
service#com.ss.android.socialbase.downloader.downloader.SqlDownloadCacheService
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:75:9-159
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:75:133-157
	android:stopWithTask
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:75:105-132
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:75:18-104
meta-data#ZEUS_PLUGIN_LIVE
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:77:9-294
	android:value
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:77:52-292
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:77:20-51
meta-data#LIVE_API_VERSION_CODE
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:78:9-79
	android:value
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:78:57-77
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:78:20-56
activity#com.bytedance.android.openliveplugin.stub.activity.DouyinAuthorizeActivityProxy
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:80:9-139
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:80:114-137
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:80:19-113
activity#com.bytedance.android.openliveplugin.stub.activity.DouyinAuthorizeActivityLiveProcessProxy
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:81:9-178
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:81:149-176
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:81:125-148
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:81:19-124
provider#com.bytedance.dataplatform.ABExtraProvider
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:83:9-192
	tools:node
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:83:161-180
	android:authorities
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:83:77-135
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:83:136-160
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:83:19-76
provider#com.bytedance.android.openliveplugin.process.server.LiveServerManager
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:84:9-263
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:84:234-261
	android:authorities
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:84:104-208
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:84:209-233
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:84:19-103
activity#com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:9-301
	android:screenOrientation
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:184-220
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:275-299
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:116-183
	android:theme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:221-274
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:19-115
activity#com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait2
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:9-302
	android:screenOrientation
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:185-221
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:276-300
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:117-184
	android:theme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:222-275
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:19-116
activity#com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait3
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:9-302
	android:screenOrientation
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:185-221
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:276-300
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:117-184
	android:theme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:222-275
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:19-116
activity#com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait4
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:9-302
	android:screenOrientation
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:185-221
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:276-300
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:117-184
	android:theme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:222-275
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:19-116
activity#com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait5
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:9-302
	android:screenOrientation
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:185-221
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:276-300
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:117-184
	android:theme
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:222-275
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:19-116
activity#com.byted.live.lite.Activity_bytelive_singleTask4
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:9-298
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:214-241
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:182-213
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:242-296
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:157-181
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:84-156
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:19-83
activity#com.byted.live.lite.Activity_bytelive_singleTop3
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:9-296
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:212-239
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:181-211
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:240-294
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:156-180
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:83-155
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:19-82
activity#com.byted.live.lite.Activity_bytelive_singleTop2
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:9-296
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:212-239
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:181-211
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:240-294
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:156-180
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:83-155
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:19-82
activity#com.byted.live.lite.Activity_bytelive_singleTop5
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:9-296
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:212-239
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:181-211
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:240-294
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:156-180
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:83-155
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:19-82
activity#com.byted.live.lite.Activity_bytelive_singleTop4
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:9-296
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:212-239
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:181-211
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:240-294
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:156-180
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:83-155
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:19-82
activity#com.byted.live.lite.Activity_bytelive_standard
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:9-293
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:209-236
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:179-208
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:237-291
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:154-178
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:81-153
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:19-80
activity#com.byted.live.lite.Activity_bytelive_singleTop1
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:9-296
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:212-239
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:181-211
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:240-294
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:156-180
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:83-155
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:19-82
activity#com.byted.live.lite.Activity_bytelive_singleTask1
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:9-298
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:214-241
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:182-213
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:242-296
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:157-181
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:84-156
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:19-83
activity#com.byted.live.lite.Activity_bytelive_singleTop6
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:9-296
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:212-239
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:181-211
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:240-294
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:156-180
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:83-155
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:19-82
activity#com.byted.live.lite.Activity_bytelive_singleTask2
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:9-298
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:214-241
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:182-213
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:242-296
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:157-181
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:84-156
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:19-83
activity#com.byted.live.lite.Activity_bytelive_singleTask3
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:9-298
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:214-241
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:182-213
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:242-296
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:157-181
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:84-156
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:19-83
activity#com.byted.live.lite.Activity_main_singleTask1
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:9-266
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:178-209
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:210-264
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:153-177
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:80-152
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:19-79
activity#com.byted.live.lite.Activity_main_singleTask2
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:9-266
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:178-209
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:210-264
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:153-177
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:80-152
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:19-79
activity#com.byted.live.lite.Activity_main_singleTask3
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:9-266
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:178-209
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:210-264
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:153-177
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:80-152
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:19-79
activity#com.byted.live.lite.Activity_main_singleTask4
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:9-266
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:178-209
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:210-264
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:153-177
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:80-152
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:19-79
activity#com.byted.live.lite.Activity_main_standard
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:9-261
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:175-204
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:205-259
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:150-174
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:77-149
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:19-76
activity#com.byted.live.lite.Activity_main_singleTop1
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:9-264
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:177-207
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:208-262
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:152-176
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:79-151
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:19-78
activity#com.byted.live.lite.Activity_main_singleTop2
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:9-264
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:177-207
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:208-262
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:152-176
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:79-151
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:19-78
activity#com.byted.live.lite.Activity_main_singleInstance1
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:9-274
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:182-217
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:218-272
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:157-181
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:84-156
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:19-83
activity#com.byted.live.lite.Activity_main_singleTop5
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:9-264
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:177-207
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:208-262
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:152-176
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:79-151
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:19-78
activity#com.byted.live.lite.Activity_main_singleTop6
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:9-264
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:177-207
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:208-262
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:152-176
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:79-151
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:19-78
activity#com.byted.live.lite.Activity_main_singleTop3
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:9-264
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:177-207
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:208-262
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:152-176
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:79-151
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:19-78
activity#com.byted.live.lite.Activity_main_singleTop4
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:9-264
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:177-207
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:208-262
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:152-176
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:79-151
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:19-78
activity#com.byted.live.lite.Activity_bytelive_singleInstance1
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:9-306
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:222-249
	android:launchMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:186-221
	android:windowSoftInputMode
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:250-304
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:161-185
	android:configChanges
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:88-160
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:19-87
provider#com.byted.live.lite.ServerManager_bytelive
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:116:9-220
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:116:191-218
	android:authorities
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:116:77-165
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:116:166-190
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:116:19-76
provider#com.byted.live.lite.ServerManager_push
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:117:9-208
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:117:183-206
	android:authorities
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:117:73-157
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:117:158-182
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:117:19-72
provider#com.byted.live.lite.ServerManager_downloader
ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:118:9-226
	android:process
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:118:195-224
	android:authorities
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:118:79-169
	android:exported
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:118:170-194
	android:name
		ADDED from [open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:118:19-78
provider#com.sigmob.sdk.SigmobFileProvider
ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:36:9-45:20
	android:grantUriPermissions
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:40:13-47
	android:authorities
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:38:13-63
	android:exported
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:39:13-37
	android:initOrder
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:41:13-36
	android:name
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:37:13-61
activity#com.sigmob.sdk.base.common.TransparentAdActivity
ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:47:9-53:60
	android:screenOrientation
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:52:13-47
	android:multiprocess
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:51:13-40
	android:hardwareAccelerated
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:50:13-47
	android:configChanges
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:49:13-83
	android:theme
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:53:13-57
	android:name
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:48:13-76
activity#com.sigmob.sdk.base.common.AdActivity
ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:54:9-60:69
	android:screenOrientation
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:59:13-47
	android:multiprocess
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:58:13-40
	android:hardwareAccelerated
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:57:13-47
	android:configChanges
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:56:13-83
	android:theme
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:60:13-66
	android:name
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:55:13-65
activity#com.sigmob.sdk.base.common.PortraitAdActivity
ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:61:9-67:69
	android:screenOrientation
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:66:13-55
	android:multiprocess
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:65:13-40
	android:hardwareAccelerated
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:64:13-47
	android:configChanges
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:63:13-83
	android:theme
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:67:13-66
	android:name
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:62:13-73
activity#com.sigmob.sdk.base.common.LandscapeAdActivity
ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:68:9-74:69
	android:screenOrientation
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:73:13-56
	android:multiprocess
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:72:13-40
	android:hardwareAccelerated
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:71:13-47
	android:configChanges
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:70:13-83
	android:theme
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:74:13-66
	android:name
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:69:13-74
activity#com.sigmob.sdk.base.common.PortraitTransparentAdActivity
ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:75:9-81:60
	android:screenOrientation
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:80:13-55
	android:multiprocess
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:79:13-40
	android:hardwareAccelerated
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:78:13-47
	android:configChanges
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:77:13-83
	android:theme
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:81:13-57
	android:name
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:76:13-84
activity#com.sigmob.sdk.base.common.LandscapeTransparentAdActivity
ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:82:9-88:60
	android:screenOrientation
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:87:13-56
	android:multiprocess
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:86:13-40
	android:hardwareAccelerated
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:85:13-47
	android:configChanges
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:84:13-83
	android:theme
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:88:13-57
	android:name
		ADDED from [wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:83:13-85
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d338db99dbee4b2179b8774b799ae6a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d338db99dbee4b2179b8774b799ae6a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.example.concentration.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.concentration.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
