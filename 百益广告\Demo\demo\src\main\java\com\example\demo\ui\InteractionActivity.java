package com.example.demo.ui;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v7.app.AppCompatActivity;
import android.view.View;
import android.widget.Toast;

import com.example.demo.R;
import com.example.demo.TbConfig;

//插屏
public class InteractionActivity extends AppCompatActivity implements View.OnClickListener {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_interaction);
        findViewById(R.id.btn_interaction).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_interaction:
                loadInteraction();
                break;
        }
    }

    //加载插屏
    private void loadInteraction() {
        com.by.mob.config.ByInteractionConfig config = new com.by.mob.config.ByInteractionConfig.Builder()
                .codeId(TbConfig.interactionCodeId)//平台申请的代码位id
                .viewWidth(600)//期望模板view的width（height自适应），默认值450（单位dp）
                .build();
        com.by.mob.ByManager.loadInteraction(config, this, new com.by.mob.ByManager.InteractionLoadListener() {
            @Override
            public void onFail(String s) {
                Toast.makeText(InteractionActivity.this, s, Toast.LENGTH_SHORT).show();
            }

            @Override
            public void getSDKID(Integer integer, String s) {

            }

            @Override
            public void onClicked() {

            }

            @Override
            public void onDismiss() {

            }

            @Override
            public void onExposure(com.by.mob.bean.Position position) {

            }

            @Override
            public void onVideoReady() {

            }

            @Override
            public void onVideoComplete() {

            }
        });
    }
}
