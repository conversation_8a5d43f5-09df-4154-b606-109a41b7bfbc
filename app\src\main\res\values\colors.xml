<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 主色调 -->
    <color name="primary_color">#2196F3</color>
    <color name="primary_dark">#1976D2</color>
    <color name="primary_light">#BBDEFB</color>
    
    <!-- 次要色 -->
    <color name="secondary_color">#FF9800</color>

    <!-- 强调色 -->
    <color name="accent_color">#FF5722</color>
    <color name="accent_light">#FFCCBC</color>
    
    <!-- 成功色 -->
    <color name="success_color">#4CAF50</color>
    <color name="success_light">#C8E6C9</color>
    
    <!-- 警告色 -->
    <color name="warning_color">#FF9800</color>
    <color name="warning_light">#FFE0B2</color>
    
    <!-- 错误色 -->
    <color name="error_color">#F44336</color>
    <color name="error_light">#FFCDD2</color>
    
    <!-- 文字颜色 -->
    <color name="text_primary">#333333</color>
    <color name="text_secondary">#666666</color>
    <color name="text_hint">#888888</color>
    <color name="text_disabled">#CCCCCC</color>
    
    <!-- 背景颜色 -->
    <color name="background_primary">#FFFFFF</color>
    <color name="background_secondary">#F5F5F5</color>
    <color name="background_card">#FFFFFF</color>
    
    <!-- 分割线颜色 -->
    <color name="divider_color">#E0E0E0</color>
    
    <!-- 舒尔特方格专用颜色 -->
    <color name="grid_cell_normal">#FFFFFF</color>
    <color name="grid_cell_pressed">#E3F2FD</color>
    <color name="grid_cell_correct">#C8E6C9</color>
    <color name="grid_cell_wrong">#FFCDD2</color>
    <color name="grid_border">#DDDDDD</color>
    
    <!-- 透明度 -->
    <color name="overlay_background">#80000000</color>

    <!-- Material Design 3 现代化颜色 -->
    <color name="md_theme_light_primary">#6750A4</color>
    <color name="md_theme_light_onPrimary">#FFFFFF</color>
    <color name="md_theme_light_primaryContainer">#EADDFF</color>
    <color name="md_theme_light_onPrimaryContainer">#21005D</color>
    <color name="md_theme_light_secondary">#625B71</color>
    <color name="md_theme_light_onSecondary">#FFFFFF</color>
    <color name="md_theme_light_secondaryContainer">#E8DEF8</color>
    <color name="md_theme_light_onSecondaryContainer">#1D192B</color>
    <color name="md_theme_light_surface">#FFFBFE</color>
    <color name="md_theme_light_onSurface">#1C1B1F</color>
    <color name="md_theme_light_surfaceVariant">#E7E0EC</color>
    <color name="md_theme_light_onSurfaceVariant">#49454F</color>
    <color name="md_theme_light_background">#FFFBFE</color>
    <color name="md_theme_light_onBackground">#1C1B1F</color>
    <color name="md_theme_light_outline">#79747E</color>
    <color name="md_theme_light_tertiary">#7D5260</color>
    <color name="md_theme_light_onTertiary">#FFFFFF</color>
    <color name="md_theme_light_tertiaryContainer">#FFD8E4</color>
    <color name="md_theme_light_onTertiaryContainer">#31111D</color>

    <!-- 渐变色 -->
    <color name="gradient_start">#667eea</color>
    <color name="gradient_end">#764ba2</color>
    <color name="gradient_start_secondary">#f093fb</color>
    <color name="gradient_end_secondary">#f5576c</color>
    <color name="gradient_start_tertiary">#a8edea</color>
    <color name="gradient_end_tertiary">#fed6e3</color>

    <!-- 主题特定颜色 -->
    <!-- 动物主题 -->
    <color name="theme_animal_primary">#4CAF50</color>
    <color name="theme_animal_secondary">#8BC34A</color>
    <color name="theme_animal_background">#E8F5E8</color>
    <color name="theme_animal_accent">#FF9800</color>

    <!-- 太空主题 -->
    <color name="theme_space_primary">#3F51B5</color>
    <color name="theme_space_secondary">#9C27B0</color>
    <color name="theme_space_background">#1A1A2E</color>
    <color name="theme_space_accent">#00BCD4</color>

    <!-- 卡通主题 -->
    <color name="theme_cartoon_primary">#FF9800</color>
    <color name="theme_cartoon_secondary">#FF5722</color>
    <color name="theme_cartoon_background">#FFF3E0</color>
    <color name="theme_cartoon_accent">#E91E63</color>

    <!-- 游戏状态颜色 -->
    <color name="grid_correct">#4CAF50</color>
    <color name="grid_wrong">#F44336</color>
    <color name="grid_selected">#2196F3</color>
    <color name="grid_hover">#E3F2FD</color>

    <!-- 特效颜色 -->
    <color name="star_color">#FFD700</color>
    <color name="particle_color">#FF6B6B</color>
    <color name="celebration_primary">#FF6B35</color>
    <color name="celebration_secondary">#F7931E</color>
    <color name="black_overlay">#80000000</color>
</resources>
