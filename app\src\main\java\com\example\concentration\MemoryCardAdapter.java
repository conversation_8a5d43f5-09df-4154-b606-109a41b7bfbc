package com.example.concentration;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;

public class MemoryCardAdapter extends RecyclerView.Adapter<MemoryCardAdapter.CardViewHolder> {
    
    private List<MemoryCard> cards;
    private OnCardClickListener listener;

    public interface OnCardClickListener {
        void onCardClick(MemoryCard card);
    }

    public MemoryCardAdapter(List<MemoryCard> cards, OnCardClickListener listener) {
        this.cards = cards;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public CardViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_memory_card, parent, false);
        return new CardViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull CardViewHolder holder, int position) {
        MemoryCard card = cards.get(position);
        
        if (card.isFlipped || card.isMatched) {
            holder.tvContent.setText(card.content);
            holder.tvContent.setTextSize(32); // 统一字体大小
            holder.tvContent.setVisibility(View.VISIBLE);

            if (card.isQuestion) {
                holder.cardView.setCardBackgroundColor(Color.parseColor("#E3F2FD"));
            } else {
                holder.cardView.setCardBackgroundColor(Color.parseColor("#F3E5F5"));
            }

            if (card.isMatched) {
                holder.cardView.setCardBackgroundColor(Color.parseColor("#C8E6C9"));
            }
        } else {
            // 显示动物图片而不是"?"
            if (card.animalEmoji != null && !card.animalEmoji.isEmpty()) {
                holder.tvContent.setText(card.animalEmoji);
                holder.tvContent.setTextSize(32); // 统一字体大小
                holder.cardView.setCardBackgroundColor(card.backgroundColor);
            } else {
                holder.tvContent.setText("?");
                holder.tvContent.setTextSize(32); // 统一字体大小
                holder.cardView.setCardBackgroundColor(Color.parseColor("#EEEEEE"));
            }
            holder.tvContent.setVisibility(View.VISIBLE);
        }
        
        holder.cardView.setOnClickListener(v -> {
            if (!card.isMatched && listener != null) {
                listener.onCardClick(card);
            }
        });
    }
    
    @Override
    public int getItemCount() {
        return cards.size();
    }
    
    static class CardViewHolder extends RecyclerView.ViewHolder {
        CardView cardView;
        TextView tvContent;
        
        CardViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_memory);
            tvContent = itemView.findViewById(R.id.tv_content);
        }
    }
}
