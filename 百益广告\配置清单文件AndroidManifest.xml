<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.example.demo">

    <!-- 可选权限 动态权限-->
    <!--获取设备标识IMEI。⽤于标识⽤户-->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <!--读写存储权限-->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!--定位权限-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <application>
        
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <provider
            android:name="com.bytedance.sdk.openadsdk.multipro.TTMultiProvider"
            android:authorities="${applicationId}.TTMultiProvider"
            android:exported="false" />
        <provider
            android:name="com.bytedance.sdk.openadsdk.TTFileProvider"
            android:authorities="${applicationId}.TTFileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/anythink_bk_tt_file_path" />
        </provider>

        <provider
            android:name="com.qq.e.comm.GDTFileProvider"
            android:authorities="${applicationId}.gdt.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/anythink_bk_gdt_file_path" />
        </provider>

        <provider
            android:name="com.sigmob.sdk.SigmobFileV4Provider"
            android:authorities="${applicationId}.sigprovider"
            android:exported="false"
            android:initOrder="200"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/anythink_bk_sigmob_file_path"/>
        </provider>

    </application>
</manifest>
