package com.example.concentration;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.material.button.MaterialButton;

public class SchulteGridActivity extends AppCompatActivity {
    
    // 游戏配置
    private int currentGridSize = 3;
    private final int[] availableSizes = {3, 4, 5, 6};
    
    // UI组件
    private MaterialButton btnBack;
    private MaterialButton btnStartGame;
    private MaterialButton btnThemeSelection;
    private MaterialButton btnTutorial;
    private TextView tvDifficulty;
    private TextView tvDifficultyLabel;
    private TextView tvNumberMode;
    private TextView tvPlayCount;
    private TextView tvBestTime;
    private TextView tvAverageTime;
    private TextView tvTodayGames;
    private TextView tvCrystalProgress;
    private LinearLayout recordsContainer;
    
    // 当前主题
    private String currentThemeId = "default";

    // 游戏设置
    private int currentSizeIndex = 0;
    private final String[] sizeLabels = {"3×3", "4×4", "5×5", "6×6"};
    private final int[] gridSizes = {3, 4, 5, 6};
    private boolean numbersDisappear = true; // 数字是否消失，默认消失（简单模式）

    // 管理器
    private ThemeManager themeManager;
    private GameDataManager gameDataManager;
    private TutorialManager tutorialManager;
    private CrystalManager crystalManager;
    private CelebrationManager celebrationManager;
    private SoundManager soundManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_schulte_grid);

        initializeManagers();
        initializeViews();
        setupClickListeners();
        updateUI();

        // 检查是否需要显示教程
        checkAndShowTutorial();
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 每次返回时刷新统计数据
        loadStatistics();
        loadRecentRecords();
    }
    
    private void initializeManagers() {
        themeManager = ThemeManager.getInstance(this);
        gameDataManager = new GameDataManager(this);
        celebrationManager = CelebrationManager.getInstance(this);
        soundManager = SoundManager.getInstance(this);
        tutorialManager = TutorialManager.getInstance(this);
        crystalManager = CrystalManager.getInstance(this);

        // 获取当前主题
        currentThemeId = themeManager.getCurrentTheme().getId();
    }
    
    private void initializeViews() {
        btnBack = findViewById(R.id.btn_back);
        btnStartGame = findViewById(R.id.btn_start_game);
        btnThemeSelection = findViewById(R.id.btn_theme_selection);
        btnTutorial = findViewById(R.id.btn_tutorial);
        tvDifficulty = findViewById(R.id.tv_difficulty);
        tvDifficultyLabel = findViewById(R.id.tv_difficulty_label);
        tvNumberMode = findViewById(R.id.tv_number_mode);
        tvPlayCount = findViewById(R.id.tv_play_count);
        tvBestTime = findViewById(R.id.tv_best_time);
        tvAverageTime = findViewById(R.id.tv_average_time);
        tvTodayGames = findViewById(R.id.tv_today_games);
        tvCrystalProgress = findViewById(R.id.tv_crystal_progress);
        recordsContainer = findViewById(R.id.records_container);
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());

        btnStartGame.setOnClickListener(v -> startGame());

        tvDifficulty.setOnClickListener(v -> showDifficultySelector());

        tvNumberMode.setOnClickListener(v -> toggleNumberMode());

        btnThemeSelection.setOnClickListener(v -> {
            Intent intent = new Intent(this, ThemeSelectionActivity.class);
            startActivity(intent);
        });

        btnTutorial.setOnClickListener(v -> showTutorial());
    }
    
    private void updateUI() {
        // 更新难度显示
        tvDifficulty.setText(sizeLabels[getCurrentSizeIndex()]);
        tvDifficultyLabel.setText(sizeLabels[getCurrentSizeIndex()] + " 方格平均时间");

        // 更新数字模式显示
        tvNumberMode.setText(numbersDisappear ? "简单" : "标准");

        // 加载统计数据
        loadStatistics();

        // 加载最近记录
        loadRecentRecords();

        // 应用主题
        applyTheme();
    }
    
    private void loadStatistics() {
        // 加载统计数据
        int playCount = gameDataManager.getPlayCount(currentGridSize);
        long bestTime = gameDataManager.getBestTime(currentGridSize);
        long averageTime = gameDataManager.getAverageTime(currentGridSize);
        int todayGames = gameDataManager.getTodayGameCount(currentGridSize);

        // 更新UI显示
        tvPlayCount.setText(String.valueOf(playCount));
        tvBestTime.setText(bestTime > 0 ? formatTime(bestTime) : "00:00.00");
        tvAverageTime.setText(averageTime > 0 ? formatTime(averageTime) : "00:00.00");
        tvTodayGames.setText("本周训练次数: " + todayGames);

        // 更新水晶进度
        String crystalProgress = crystalManager.getCrystalProgressText(CrystalManager.CrystalType.SCHULTE_GRID);
        tvCrystalProgress.setText(crystalProgress);
    }

    private void loadRecentRecords() {
        // 清空现有记录
        recordsContainer.removeAllViews();

        // 从GameDataManager获取真实的游戏记录
        int totalGames = gameDataManager.getTotalGames();

        if (totalGames == 0) {
            // 没有记录时显示占位文本
            TextView placeholder = new TextView(this);
            placeholder.setText("暂无训练记录");
            placeholder.setTextSize(14);
            placeholder.setTextColor(getColor(R.color.md_theme_light_onSurfaceVariant));
            placeholder.setPadding(0, 16, 0, 16);
            placeholder.setGravity(android.view.Gravity.CENTER);
            recordsContainer.addView(placeholder);
        } else {
            // 显示最近的游戏记录（最多显示5条）
            long bestTime = gameDataManager.getBestTime(currentGridSize);
            long averageTime = gameDataManager.getAverageTime(currentGridSize);
            int todayGames = gameDataManager.getTodayGames();

            // 显示统计信息作为记录
            if (bestTime > 0) {
                addRecordRow("最佳记录", currentGridSize + "×" + currentGridSize, formatTime(bestTime), "完成");
            }
            if (averageTime > 0) {
                addRecordRow("平均时间", currentGridSize + "×" + currentGridSize, formatTime(averageTime), "统计");
            }
            if (todayGames > 0) {
                addRecordRow("今日训练", "共" + todayGames + "次", "--", "进行中");
            }

            // 如果记录太少，添加一些示例记录
            if (totalGames < 3) {
                addRecordRow("继续训练", "解锁更多", "记录", "加油!");
            }
        }
    }

    private void addRecordRow(String date, String gridSize, String time, String status) {
        LinearLayout row = new LinearLayout(this);
        row.setOrientation(LinearLayout.HORIZONTAL);
        row.setPadding(0, 8, 0, 8);

        // 日期
        TextView tvDate = new TextView(this);
        tvDate.setText(date);
        tvDate.setTextSize(12);
        tvDate.setTextColor(getColor(R.color.md_theme_light_onSurface));
        tvDate.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));

        // 方格大小
        TextView tvGrid = new TextView(this);
        tvGrid.setText(gridSize);
        tvGrid.setTextSize(12);
        tvGrid.setTextColor(getColor(R.color.md_theme_light_onSurface));
        tvGrid.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));
        tvGrid.setGravity(android.view.Gravity.CENTER);

        // 用时
        TextView tvTime = new TextView(this);
        tvTime.setText(time);
        tvTime.setTextSize(12);
        tvTime.setTextColor(getColor(R.color.md_theme_light_primary));
        tvTime.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));
        tvTime.setGravity(android.view.Gravity.CENTER);

        // 状态
        TextView tvStatus = new TextView(this);
        tvStatus.setText(status);
        tvStatus.setTextSize(12);
        tvStatus.setTextColor(getColor(R.color.md_theme_light_onSurfaceVariant));
        tvStatus.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));
        tvStatus.setGravity(android.view.Gravity.CENTER);

        row.addView(tvDate);
        row.addView(tvGrid);
        row.addView(tvTime);
        row.addView(tvStatus);

        recordsContainer.addView(row);
    }
    
    private void applyTheme() {
        ThemeManager.ThemeType currentTheme = themeManager.getCurrentTheme();
        
        // 应用主题颜色到UI组件
        // 这里可以根据主题调整颜色和样式
    }
    
    private int getCurrentSizeIndex() {
        for (int i = 0; i < availableSizes.length; i++) {
            if (availableSizes[i] == currentGridSize) {
                return i;
            }
        }
        return 0;
    }
    
    private void showDifficultySelector() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("选择难度");
        builder.setItems(sizeLabels, (dialog, which) -> {
            currentGridSize = availableSizes[which];
            updateUI();
        });
        builder.show();
    }
    
    private void startGame() {
        // 跳转到倒计时页面
        Intent intent = new Intent(this, CountdownActivity.class);
        intent.putExtra("gridSize", currentGridSize);
        intent.putExtra("themeId", currentThemeId);
        intent.putExtra("numbersDisappear", numbersDisappear);
        startActivity(intent);

        // 添加页面切换动画
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
    }
    
    // 新纪录庆祝方法
    public void checkAndCelebrateNewRecord(long completionTime) {
        long previousBest = gameDataManager.getBestTime(currentGridSize);
        gameDataManager.saveGameResult(currentGridSize, completionTime);
        
        if (previousBest == 0 || completionTime < previousBest) {
            showNewRecordCelebration(completionTime);
        }
        
        loadStatistics();
    }
    
    private void showNewRecordCelebration(long newRecordTime) {
        // 播放烟花特效
        celebrationManager.showFireworks(recordsContainer, 3000); // 3秒烟花

        // 播放音效
        soundManager.playCorrectSound();

        // 显示新纪录提示
        new AlertDialog.Builder(this)
            .setTitle("🎉 新纪录！")
            .setMessage("恭喜！您创造了新的最佳时间：" + formatTime(newRecordTime))
            .setPositiveButton("太棒了！", null)
            .show();
    }
    
    // 格式化时间显示
    private String formatTime(long timeInMillis) {
        int minutes = (int) (timeInMillis / 60000);
        int seconds = (int) ((timeInMillis % 60000) / 1000);
        int milliseconds = (int) (timeInMillis % 1000);
        return String.format("%02d:%02d.%03d", minutes, seconds, milliseconds);
    }

    private void toggleNumberMode() {
        numbersDisappear = !numbersDisappear;
        tvNumberMode.setText(numbersDisappear ? "简单" : "标准");

        // 可以添加一个提示
        String message = numbersDisappear ? "已切换到简单模式：点击的数字会消失" : "已切换到标准模式：点击的数字保持显示";
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show();
    }

    /**
     * 检查并显示教程
     */
    private void checkAndShowTutorial() {
        if (tutorialManager.isFirstTime(TutorialManager.GameType.SCHULTE_GRID)) {
            showTutorial();
        }
    }

    /**
     * 显示教程
     */
    private void showTutorial() {
        Intent intent = new Intent(this, TutorialActivity.class);
        intent.putExtra("game_type", TutorialManager.GameType.SCHULTE_GRID.getKey());
        startActivity(intent);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}
