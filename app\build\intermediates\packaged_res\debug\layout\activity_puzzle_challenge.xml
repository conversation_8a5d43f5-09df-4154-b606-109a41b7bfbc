<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_number_bg">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="答题挑战"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:gravity="center" />

        <TextView
            android:id="@+id/tv_score"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="得分: 0"
            android:textSize="14sp"
            android:textColor="@android:color/white" />

    </LinearLayout>

    <!-- 主要内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp">

        <TextView
            android:id="@+id/tv_question"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1 × 1 = ?"
            android:textSize="32sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:layout_marginBottom="48dp" />

        <!-- 答案选项 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginBottom="16dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_answer1"
                    android:layout_width="120dp"
                    android:layout_height="60dp"
                    android:text="1"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:cornerRadius="12dp"
                    android:layout_marginEnd="16dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_answer2"
                    android:layout_width="120dp"
                    android:layout_height="60dp"
                    android:text="2"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:cornerRadius="12dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_answer3"
                    android:layout_width="120dp"
                    android:layout_height="60dp"
                    android:text="3"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:cornerRadius="12dp"
                    android:layout_marginEnd="16dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_answer4"
                    android:layout_width="120dp"
                    android:layout_height="60dp"
                    android:text="4"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:cornerRadius="12dp" />

            </LinearLayout>

        </LinearLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_next"
            android:layout_width="200dp"
            android:layout_height="56dp"
            android:text="下一题"
            android:textSize="16sp"
            android:textStyle="bold"
            app:cornerRadius="28dp"
            android:layout_marginTop="32dp"
            android:enabled="false" />

    </LinearLayout>

</LinearLayout>
