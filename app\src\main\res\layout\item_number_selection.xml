<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_number"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:gravity="center">

        <!-- 数字显示 -->
        <TextView
            android:id="@+id/tv_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1"
            android:textSize="32sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_primary"
            android:layout_marginBottom="8dp" />

        <!-- 进度条 -->
        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:max="100"
            android:progress="0"
            android:progressTint="@color/md_theme_light_primary"
            android:layout_marginBottom="4dp" />

        <!-- 进度文字 -->
        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0%"
            android:textSize="12sp"
            android:textColor="@color/md_theme_light_onSurfaceVariant"
            android:layout_marginBottom="8dp" />

        <!-- 最佳时间 -->
        <TextView
            android:id="@+id/tv_best_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="最佳: 暂无记录"
            android:textSize="10sp"
            android:textColor="@color/md_theme_light_onSurfaceVariant"
            android:gravity="center"
            android:layout_marginBottom="2dp" />

        <!-- 游戏次数 -->
        <TextView
            android:id="@+id/tv_total_games"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="已玩: 0次"
            android:textSize="10sp"
            android:textColor="@color/md_theme_light_onSurfaceVariant"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <!-- 解锁状态 -->
        <TextView
            android:id="@+id/tv_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="✅ 已解锁"
            android:textSize="11sp"
            android:textStyle="bold"
            android:padding="4dp"
            android:background="@drawable/status_background"
            android:textColor="@color/md_theme_light_primary" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
