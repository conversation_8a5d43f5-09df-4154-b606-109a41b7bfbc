<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- 顶部标题区域 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp"
                android:background="@color/md_theme_light_surface"
                android:gravity="center_vertical">

                <ImageView
                    android:id="@+id/btn_back"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@android:drawable/ic_menu_revert"
                    android:background="@drawable/modern_button_background"
                    android:padding="8dp"
                    android:tint="@color/md_theme_light_onPrimary" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="🎨 选择主题"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSurface"
                    android:gravity="center" />

                <View
                    android:layout_width="40dp"
                    android:layout_height="40dp" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 主题说明 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@color/md_theme_light_surface"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="✨ 完成训练解锁新主题"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSurface"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="每个主题都有独特的故事背景和视觉效果，让训练更有趣！"
                    android:textSize="14sp"
                    android:textColor="@color/md_theme_light_onSurfaceVariant" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 默认主题 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/theme_default"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@color/md_theme_light_surface"
                android:padding="20dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🎯"
                    android:textSize="40sp"
                    android:layout_marginEnd="16dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="默认主题"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_onSurface" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="经典的专注力训练体验"
                        android:textSize="14sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant"
                        android:layout_marginTop="4dp" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="✓ 已选择"
                    android:textSize="14sp"
                    android:textColor="@color/md_theme_light_primary"
                    android:textStyle="bold"
                    android:background="@drawable/modern_card_background"
                    android:padding="8dp" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 动物主题 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/theme_animal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@color/theme_animal_background"
                android:padding="20dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🦁"
                    android:textSize="40sp"
                    android:layout_marginEnd="16dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="动物世界"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/theme_animal_primary" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="帮助小动物们找到回家的路"
                        android:textSize="14sp"
                        android:textColor="@color/theme_animal_secondary"
                        android:layout_marginTop="4dp" />
                </LinearLayout>

                <TextView
                    android:id="@+id/animal_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🔒 需要5次训练"
                    android:textSize="12sp"
                    android:textColor="@color/theme_animal_secondary"
                    android:background="@drawable/modern_card_background"
                    android:padding="8dp" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 太空主题 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/theme_space"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@color/theme_space_background"
                android:padding="20dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🚀"
                    android:textSize="40sp"
                    android:layout_marginEnd="16dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="太空探索"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/theme_space_primary" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="收集宇宙中的能量水晶"
                        android:textSize="14sp"
                        android:textColor="@color/theme_space_secondary"
                        android:layout_marginTop="4dp" />
                </LinearLayout>

                <TextView
                    android:id="@+id/space_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🔒 需要15次训练"
                    android:textSize="12sp"
                    android:textColor="@color/theme_space_secondary"
                    android:background="@drawable/modern_card_background"
                    android:padding="8dp" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 卡通主题 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/theme_cartoon"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@color/theme_cartoon_background"
                android:padding="20dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🎨"
                    android:textSize="40sp"
                    android:layout_marginEnd="16dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="卡通乐园"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/theme_cartoon_primary" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="帮助小精灵收集魔法宝石"
                        android:textSize="14sp"
                        android:textColor="@color/theme_cartoon_secondary"
                        android:layout_marginTop="4dp" />
                </LinearLayout>

                <TextView
                    android:id="@+id/cartoon_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🔒 需要30次训练"
                    android:textSize="12sp"
                    android:textColor="@color/theme_cartoon_secondary"
                    android:background="@drawable/modern_card_background"
                    android:padding="8dp" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>
</ScrollView>
