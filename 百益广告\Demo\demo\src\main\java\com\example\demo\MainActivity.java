package com.example.demo;

import android.content.ComponentName;
import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v7.app.AppCompatActivity;
import android.view.View;

import com.example.demo.ui.BannerActivity;
import com.example.demo.ui.DrawFeedActivity;
import com.example.demo.ui.FeedActivity;
import com.example.demo.ui.InteractionActivity;
import com.example.demo.ui.NativeActivity;
import com.example.demo.ui.NewsFeedFragmentActivity;
import com.example.demo.ui.RewardVideoActivity;
import com.example.demo.ui.SplashActivity;
import com.example.demo.ui.VideoFragmentActivity;
import com.example.demo.ui.channel_num.ChannelNumManageActivity;
import com.example.demo.ui.h5.WebActivity;

public class MainActivity extends AppCompatActivity implements View.OnClickListener {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        initView();

        requestPermissionIfNecessary();
    }

    private void requestPermissionIfNecessary() {
        //动态权限，在App的入口Activity中的onCreate调用
        com.by.mob.utils.RequestPermission.RequestPermissionIfNecessary(this);
        /*
        SDK不强制校验上述权限（即:无上述权限sdk也可正常工作），但建议您申请上述权限。
        针对单媒体的用户，允许获取权限的，投放定向内容；不允许获取权限的用户，投放通投内容。
        媒体可以选择不添加上述权限，并承担相应填充和eCPM单价下降损失的结果。
         */
    }

    private void initView() {
        findViewById(R.id.a_main_ChannelNumManage_btn).setOnClickListener(this);//渠道号管理
        findViewById(R.id.a_main_Splash_btn).setOnClickListener(this);//开屏
        findViewById(R.id.a_main_Interaction_btn).setOnClickListener(this);//插屏
        findViewById(R.id.a_main_Banner_btn).setOnClickListener(this);//Banner
        findViewById(R.id.a_main_Feed_btn).setOnClickListener(this);//信息流
        findViewById(R.id.a_main_RewardVideo_btn).setOnClickListener(this);//激励视频
        findViewById(R.id.a_main_DrawFeed_btn).setOnClickListener(this);//Draw竖版视频信息流
        findViewById(R.id.a_main_Native_btn).setOnClickListener(this);//自渲染
        findViewById(R.id.a_main_Video_btn).setOnClickListener(this);//短视频
        findViewById(R.id.a_main_NewsFeed_btn).setOnClickListener(this);//新闻资讯
    }

    @Override
    public void onClick(View v) {
        Intent intent;
        switch (v.getId()) {
            case R.id.a_main_ChannelNumManage_btn://渠道号管理
                intent = new Intent(this, ChannelNumManageActivity.class);
                startActivity(intent);
                break;
            case R.id.a_main_Splash_btn://开屏
                intent = new Intent(this, SplashActivity.class);
                startActivity(intent);
                break;
            case R.id.a_main_Interaction_btn://插屏
                intent = new Intent(this, InteractionActivity.class);
                startActivity(intent);
                break;
            case R.id.a_main_Banner_btn://Banner
                intent = new Intent(this, BannerActivity.class);
                startActivity(intent);
                break;
            case R.id.a_main_Feed_btn://信息流
                intent = new Intent(this, FeedActivity.class);
                startActivity(intent);
                break;
            case R.id.a_main_RewardVideo_btn://激励视频
                intent = new Intent(this, RewardVideoActivity.class);
                startActivity(intent);
                break;
            case R.id.a_main_DrawFeed_btn://Draw竖版视频信息流
                intent = new Intent(this, DrawFeedActivity.class);
                startActivity(intent);
                break;
            case R.id.a_main_Native_btn://自渲染
                intent = new Intent(this, NativeActivity.class);
                startActivity(intent);
                break;
            case R.id.a_main_Video_btn://短视频
                intent = new Intent(this, VideoFragmentActivity.class);
                startActivity(intent);
                break;
            case R.id.a_main_NewsFeed_btn://新闻资讯
                intent = new Intent(this, NewsFeedFragmentActivity.class);
                startActivity(intent);
                break;
        }
    }
}
