package com.example.demo;

import android.app.Application;
import android.support.multidex.MultiDex;
import android.util.Log;

import com.example.demo.utils.SharedPreferencesUtils;

public class App extends Application {

    private static final String TAG = "Demo___";

    public static com.by.mob.config.ByInitConfig config = new com.by.mob.config.ByInitConfig.Builder()
            .appId(TbConfig.appId)//初始化id（平台上申请：应用列表的应用id）
            //.isTest(1)
            .build();

    @Override
    public void onCreate() {
        super.onCreate();
        if (!SharedPreferencesUtils.getIsFirstInit(this.getApplicationContext())) {
            //String currentProcessName = AppUtils.getCurrentProcessName(this);//当前进程名
            initTb();
        }
    }

    //SDK初始化
    private void initTb() {
        Log.d(TAG, "initTb");
        com.by.mob.ByManager.init(this, config, new com.by.mob.ByManager.IsInitListener() {
            @Override
            public void onFail(String s) {
                Log.d(TAG, "onFail=" + s);
            }

            @Override
            public void onSuccess() {
                Log.d(TAG, "onSuccess");
            }

            @Override
            public void onDpSuccess() {
                Log.d(TAG, "onDpSuccess");
            }
        });
    }

    @Override
    protected void attachBaseContext(android.content.Context base) {
        super.attachBaseContext(base);
        MultiDex.install(this);
    }
}
