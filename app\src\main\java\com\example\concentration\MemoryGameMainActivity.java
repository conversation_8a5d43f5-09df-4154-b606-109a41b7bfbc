package com.example.concentration;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import com.google.android.material.button.MaterialButton;

/**
 * 记忆游戏主页面
 * 提供模式选择和难度设置
 */
public class MemoryGameMainActivity extends AppCompatActivity {

    private ImageButton btnBack;
    private MaterialButton tvCurrentMode;
    private MaterialButton tvCurrentDifficulty;
    private MaterialButton btnStart;
    private MaterialButton btnTutorial;
    private CardView cardStats;
    private TextView tvBestScore;
    private TextView tvTotalGames;
    private TextView tvCrystalProgress;
    private LinearLayout layoutRecentRecords;
    
    private MemoryGameMode currentMode = MemoryGameMode.NUMBER_CONTENT;
    private int currentDifficulty = 5; // 数字模式默认5个数字，位置模式为网格大小
    private TutorialManager tutorialManager;
    private CrystalManager crystalManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_memory_game_main);

        // 初始化管理器（必须在loadStatistics之前）
        tutorialManager = TutorialManager.getInstance(this);
        crystalManager = CrystalManager.getInstance(this);

        initViews();
        setupClickListeners();
        updateUI();
        loadStatistics();

        // 检查是否需要显示教程
        checkAndShowTutorial();
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        tvCurrentMode = findViewById(R.id.tv_current_mode);
        tvCurrentDifficulty = findViewById(R.id.tv_current_difficulty);
        btnStart = findViewById(R.id.btn_start);
        btnTutorial = findViewById(R.id.btn_tutorial);
        cardStats = findViewById(R.id.card_stats);
        tvBestScore = findViewById(R.id.tv_best_score);
        tvTotalGames = findViewById(R.id.tv_total_games);
        tvCrystalProgress = findViewById(R.id.tv_crystal_progress);
        layoutRecentRecords = findViewById(R.id.layout_recent_records);
    }
    
    private void setupClickListeners() {
        // 返回按钮
        btnBack.setOnClickListener(v -> finish());

        // 模式切换
        tvCurrentMode.setOnClickListener(v -> toggleMode());

        // 难度选择
        tvCurrentDifficulty.setOnClickListener(v -> showDifficultyDialog());

        // 开始游戏
        btnStart.setOnClickListener(v -> startGame());

        // 教程按钮
        btnTutorial.setOnClickListener(v -> showTutorial());
    }
    
    private void toggleMode() {
        if (currentMode == MemoryGameMode.NUMBER_CONTENT) {
            currentMode = MemoryGameMode.POSITION_MEMORY;
            currentDifficulty = 3; // 位置模式默认3x3网格
        } else {
            currentMode = MemoryGameMode.NUMBER_CONTENT;
            currentDifficulty = 5; // 数字模式默认5个数字
        }
        updateUI();
    }
    
    private void showDifficultyDialog() {
        if (currentMode == MemoryGameMode.NUMBER_CONTENT) {
            // 数字模式：5-10个数字
            showNumberDifficultyDialog();
        } else {
            // 位置模式：3x3到5x5网格
            showPositionDifficultyDialog();
        }
    }
    
    private void showNumberDifficultyDialog() {
        String[] options = {"5个数字", "6个数字", "7个数字", "8个数字", "9个数字", "10个数字"};
        int[] values = {5, 6, 7, 8, 9, 10};
        
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("选择数字个数");
        builder.setItems(options, (dialog, which) -> {
            currentDifficulty = values[which];
            updateUI();
        });
        builder.show();
    }
    
    private void showPositionDifficultyDialog() {
        String[] options = {"3×3网格", "4×4网格", "5×5网格"};
        int[] values = {3, 4, 5};
        
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("选择网格大小");
        builder.setItems(options, (dialog, which) -> {
            currentDifficulty = values[which];
            updateUI();
        });
        builder.show();
    }
    
    private void updateUI() {
        tvCurrentMode.setText(currentMode.getDisplayName());
        
        if (currentMode == MemoryGameMode.NUMBER_CONTENT) {
            tvCurrentDifficulty.setText(currentDifficulty + "个数字");
        } else {
            tvCurrentDifficulty.setText(currentDifficulty + "×" + currentDifficulty + "网格");
        }
    }
    
    private void loadStatistics() {
        GameDataManager gameDataManager = new GameDataManager(this);

        // 根据当前模式和难度获取统计数据
        String gameKey = currentMode.getKey() + "_" + currentDifficulty;

        // 获取统计数据
        int totalGames = gameDataManager.getMemoryGameCount(gameKey);
        long bestTime = gameDataManager.getMemoryGameBestTime(gameKey);
        int todayGames = gameDataManager.getMemoryGameTodayCount(gameKey);

        // 更新UI显示
        tvBestScore.setText(bestTime > 0 ? formatTime(bestTime) : "--");
        tvTotalGames.setText(String.valueOf(totalGames));

        // 更新水晶进度
        String crystalProgress = crystalManager.getCrystalProgressText(CrystalManager.CrystalType.SEQUENCE_MEMORY);
        tvCrystalProgress.setText(crystalProgress);

        // 加载最近训练记录
        loadRecentRecords(gameDataManager, gameKey);
    }

    private void loadRecentRecords(GameDataManager gameDataManager, String gameKey) {
        layoutRecentRecords.removeAllViews();

        // 显示统计信息作为记录
        int totalGames = gameDataManager.getMemoryGameCount(gameKey);
        long bestTime = gameDataManager.getMemoryGameBestTime(gameKey);
        long averageTime = gameDataManager.getMemoryGameAverageTime(gameKey);
        int todayGames = gameDataManager.getMemoryGameTodayCount(gameKey);

        if (totalGames == 0) {
            // 没有记录时显示提示
            TextView placeholder = new TextView(this);
            placeholder.setText("暂无训练记录，开始你的第一次训练吧！");
            placeholder.setTextSize(14);
            placeholder.setTextColor(getColor(R.color.md_theme_light_onSurfaceVariant));
            placeholder.setPadding(0, 16, 0, 16);
            placeholder.setGravity(android.view.Gravity.CENTER);
            layoutRecentRecords.addView(placeholder);
        } else {
            // 显示统计信息作为记录
            if (bestTime > 0) {
                addRecordRow("最佳记录", getModeDisplayText(), formatTime(bestTime), "完成");
            }
            if (averageTime > 0) {
                addRecordRow("平均时间", getModeDisplayText(), formatTime(averageTime), "统计");
            }
            if (todayGames > 0) {
                addRecordRow("今日训练", "共" + todayGames + "次", "--", "进行中");
            }

            // 添加鼓励信息
            if (totalGames < 10) {
                addRecordRow("继续训练", "解锁更多", "记录", "加油!");
            }
        }
    }

    private String getModeDisplayText() {
        if (currentMode == MemoryGameMode.NUMBER_CONTENT) {
            return currentDifficulty + "个数字";
        } else {
            return currentDifficulty + "×" + currentDifficulty + "网格";
        }
    }

    private void addRecordRow(String date, String mode, String time, String status) {
        LinearLayout row = new LinearLayout(this);
        row.setOrientation(LinearLayout.HORIZONTAL);
        row.setPadding(0, 8, 0, 8);

        // 日期
        TextView tvDate = new TextView(this);
        tvDate.setText(date);
        tvDate.setTextSize(12);
        tvDate.setTextColor(getColor(R.color.md_theme_light_onSurface));
        tvDate.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));

        // 模式
        TextView tvMode = new TextView(this);
        tvMode.setText(mode);
        tvMode.setTextSize(12);
        tvMode.setTextColor(getColor(R.color.md_theme_light_onSurface));
        tvMode.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));
        tvMode.setGravity(android.view.Gravity.CENTER);

        // 时间
        TextView tvTime = new TextView(this);
        tvTime.setText(time);
        tvTime.setTextSize(12);
        tvTime.setTextColor(getColor(R.color.md_theme_light_onSurface));
        tvTime.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));
        tvTime.setGravity(android.view.Gravity.CENTER);

        // 状态
        TextView tvStatus = new TextView(this);
        tvStatus.setText(status);
        tvStatus.setTextSize(12);
        tvStatus.setTextColor(getColor(R.color.md_theme_light_primary));
        tvStatus.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));
        tvStatus.setGravity(android.view.Gravity.END);

        row.addView(tvDate);
        row.addView(tvMode);
        row.addView(tvTime);
        row.addView(tvStatus);

        layoutRecentRecords.addView(row);
    }

    private String formatTime(long timeInMillis) {
        long seconds = timeInMillis / 1000;
        long milliseconds = timeInMillis % 1000;
        long minutes = seconds / 60;
        seconds = seconds % 60;

        return String.format("%02d:%02d.%02d", minutes, seconds, milliseconds / 10);
    }
    
    private void startGame() {
        Intent intent = new Intent(this, CountdownActivity.class);
        intent.putExtra("game_type", "memory_game");
        intent.putExtra("memory_mode", currentMode.getKey());
        intent.putExtra("difficulty", currentDifficulty);
        startActivity(intent);
    }

    /**
     * 检查并显示教程
     */
    private void checkAndShowTutorial() {
        if (tutorialManager.isFirstTime(TutorialManager.GameType.SEQUENCE_MEMORY)) {
            showTutorial();
        }
    }

    /**
     * 显示教程
     */
    private void showTutorial() {
        Intent intent = new Intent(this, TutorialActivity.class);
        intent.putExtra("game_type", TutorialManager.GameType.SEQUENCE_MEMORY.getKey());
        startActivity(intent);
    }
}
