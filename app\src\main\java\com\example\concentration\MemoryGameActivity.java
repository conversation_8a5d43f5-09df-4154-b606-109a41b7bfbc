package com.example.concentration;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.widget.GridLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import com.google.android.material.button.MaterialButton;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;

/**
 * 统一的记忆游戏Activity
 * 根据模式参数显示不同的游戏界面
 */
public class MemoryGameActivity extends AppCompatActivity {

    private static final String TAG = "MemoryGameActivity";
    
    private TextView tvTitle;
    private TextView tvInstruction;
    private TextView tvProgress;
    
    // 数字内容记忆模式组件
    private CardView cardNumberDisplay;
    private TextView tvCurrentNumber;
    
    // 位置记忆模式组件
    private GridLayout gridPositions;
    private MaterialButton[] gridButtons;
    
    // 输入区域
    private LinearLayout layoutInputArea;
    private TextView tvInputPrompt;
    private LinearLayout layoutNumberButtons;
    private MaterialButton btnConfirm;
    
    private MemoryGameMode gameMode;
    private int difficulty;
    private Handler handler = new Handler();
    private SoundManager soundManager;
    
    // 游戏数据
    private List<Integer> sequence = new ArrayList<>();
    private List<Integer> userInput = new ArrayList<>();
    private List<Integer> highlightedPositions = new ArrayList<>();
    private long startTime;
    private int currentDisplayIndex = 0;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            Log.d(TAG, "onCreate: 开始创建Activity");
            super.onCreate(savedInstanceState);
            Log.d(TAG, "onCreate: super.onCreate完成");

            setContentView(R.layout.activity_memory_game);
            Log.d(TAG, "onCreate: setContentView完成");

            // 获取参数
            String modeKey = getIntent().getStringExtra("memory_mode");
            Log.d(TAG, "onCreate: 获取到modeKey = " + modeKey);
            if (modeKey == null) {
                modeKey = "number_content"; // 默认模式
                Log.d(TAG, "onCreate: 使用默认模式");
            }
            gameMode = MemoryGameMode.fromKey(modeKey);
            difficulty = getIntent().getIntExtra("difficulty", 7);
            Log.d(TAG, "onCreate: gameMode = " + gameMode + ", difficulty = " + difficulty);

            // 确保gameMode不为null
            if (gameMode == null) {
                gameMode = MemoryGameMode.NUMBER_CONTENT;
                Log.d(TAG, "onCreate: gameMode为null，使用默认值");
            }

            Log.d(TAG, "onCreate: 开始初始化视图");
            initViews();
            Log.d(TAG, "onCreate: 初始化视图完成");

            Log.d(TAG, "onCreate: 开始初始化管理器");
            initManagers();
            Log.d(TAG, "onCreate: 初始化管理器完成");

            Log.d(TAG, "onCreate: 开始设置游戏");
            setupGame();
            Log.d(TAG, "onCreate: 设置游戏完成");

            Log.d(TAG, "onCreate: 开始游戏");
            startGame();
            Log.d(TAG, "onCreate: Activity创建完成");
        } catch (Exception e) {
            Log.e(TAG, "onCreate: 发生异常", e);
            finish();
        }
    }
    
    private void initViews() {
        try {
            Log.d(TAG, "initViews: 开始查找视图");

            tvTitle = findViewById(R.id.tv_title);
            Log.d(TAG, "initViews: tvTitle = " + (tvTitle != null ? "找到" : "null"));

            tvInstruction = findViewById(R.id.tv_instruction);
            Log.d(TAG, "initViews: tvInstruction = " + (tvInstruction != null ? "找到" : "null"));

            tvProgress = findViewById(R.id.tv_progress);
            Log.d(TAG, "initViews: tvProgress = " + (tvProgress != null ? "找到" : "null"));

            cardNumberDisplay = findViewById(R.id.card_number_display);
            Log.d(TAG, "initViews: cardNumberDisplay = " + (cardNumberDisplay != null ? "找到" : "null"));

            tvCurrentNumber = findViewById(R.id.tv_current_number);
            Log.d(TAG, "initViews: tvCurrentNumber = " + (tvCurrentNumber != null ? "找到" : "null"));

            gridPositions = findViewById(R.id.grid_positions);
            Log.d(TAG, "initViews: gridPositions = " + (gridPositions != null ? "找到" : "null"));

            layoutInputArea = findViewById(R.id.layout_input_area);
            Log.d(TAG, "initViews: layoutInputArea = " + (layoutInputArea != null ? "找到" : "null"));

            tvInputPrompt = findViewById(R.id.tv_input_prompt);
            Log.d(TAG, "initViews: tvInputPrompt = " + (tvInputPrompt != null ? "找到" : "null"));

            layoutNumberButtons = findViewById(R.id.layout_number_buttons);
            Log.d(TAG, "initViews: layoutNumberButtons = " + (layoutNumberButtons != null ? "找到" : "null"));

            btnConfirm = findViewById(R.id.btn_confirm);
            Log.d(TAG, "initViews: btnConfirm = " + (btnConfirm != null ? "找到" : "null"));

            // 检查关键视图是否为null
            if (tvTitle == null || cardNumberDisplay == null || gridPositions == null) {
                Log.e(TAG, "initViews: 关键视图初始化失败");
                throw new RuntimeException("关键视图初始化失败");
            }

            Log.d(TAG, "initViews: 所有视图初始化成功");
        } catch (Exception e) {
            Log.e(TAG, "initViews: 发生异常", e);
            e.printStackTrace();
            finish();
        }
    }
    
    private void initManagers() {
        soundManager = SoundManager.getInstance(this);
    }
    
    private void setupGame() {
        if (gameMode == MemoryGameMode.NUMBER_CONTENT) {
            setupNumberContentGame();
        } else {
            setupPositionMemoryGame();
        }
    }
    
    private void setupNumberContentGame() {
        tvTitle.setText("🧠 数字内容记忆");
        tvInstruction.setText("✨ 请记住数字序列的内容");
        tvProgress.setText("准备显示 " + difficulty + " 个数字");
        
        // 生成随机数字序列
        Random random = new Random();
        sequence.clear();
        for (int i = 0; i < difficulty; i++) {
            sequence.add(random.nextInt(10)); // 0-9的数字
        }
        
        // 显示相关组件
        cardNumberDisplay.setVisibility(View.VISIBLE);
        gridPositions.setVisibility(View.GONE);
        layoutInputArea.setVisibility(View.GONE);
    }
    
    private void setupPositionMemoryGame() {
        tvTitle.setText("📍 位置记忆");
        tvInstruction.setText("✨ 请记住数字在网格中的位置");
        tvProgress.setText("准备显示 " + difficulty + "×" + difficulty + " 网格");
        
        // 创建网格
        createGrid();
        
        // 生成随机位置序列
        generateRandomPositions();
        
        // 显示相关组件
        cardNumberDisplay.setVisibility(View.GONE);
        gridPositions.setVisibility(View.VISIBLE);
        layoutInputArea.setVisibility(View.GONE);
    }
    
    private void createGrid() {
        gridPositions.removeAllViews();
        gridPositions.setColumnCount(difficulty);
        gridPositions.setRowCount(difficulty);

        gridButtons = new MaterialButton[difficulty * difficulty];

        // 计算屏幕可用宽度
        int screenWidth = getResources().getDisplayMetrics().widthPixels;
        int cardPadding = (int) (32 * getResources().getDisplayMetrics().density); // 卡片内边距
        int screenMargin = (int) (32 * getResources().getDisplayMetrics().density); // 屏幕边距
        int availableWidth = screenWidth - cardPadding - screenMargin;

        // 计算每个按钮的大小（正方形）
        int buttonMargin = (int) (3 * getResources().getDisplayMetrics().density);
        int totalMargin = (difficulty + 1) * buttonMargin * 2;
        int buttonSize = (availableWidth - totalMargin) / difficulty;

        // 确保按钮不会太小
        int minButtonSize = (int) (40 * getResources().getDisplayMetrics().density);
        if (buttonSize < minButtonSize) {
            buttonSize = minButtonSize;
        }

        for (int i = 0; i < difficulty * difficulty; i++) {
            MaterialButton button = new MaterialButton(this);
            button.setText("");
            button.setTextSize(Math.max(12, 20 - difficulty)); // 根据难度调整字体大小

            GridLayout.LayoutParams params = new GridLayout.LayoutParams();
            params.width = buttonSize;
            params.height = buttonSize; // 确保正方形
            params.setMargins(buttonMargin, buttonMargin, buttonMargin, buttonMargin);
            button.setLayoutParams(params);

            button.setBackgroundColor(getResources().getColor(R.color.grid_cell_normal));
            gridButtons[i] = button;
            gridPositions.addView(button);
        }
    }
    
    private void generateRandomPositions() {
        highlightedPositions.clear();
        Random random = new Random();
        int totalPositions = difficulty * difficulty;

        // 根据网格大小设置合理的记忆位置数量
        int numHighlights;
        switch (difficulty) {
            case 3: // 3x3网格
                numHighlights = 5;
                break;
            case 4: // 4x4网格
                numHighlights = 7;
                break;
            case 5: // 5x5网格
                numHighlights = 10;
                break;
            default:
                numHighlights = Math.min(5, totalPositions / 2); // 默认值
                break;
        }

        Log.d(TAG, "generateRandomPositions: difficulty=" + difficulty + ", totalPositions=" + totalPositions + ", numHighlights=" + numHighlights);

        while (highlightedPositions.size() < numHighlights) {
            int position = random.nextInt(totalPositions);
            if (!highlightedPositions.contains(position)) {
                highlightedPositions.add(position);
            }
        }

        Log.d(TAG, "generateRandomPositions: 实际生成位置数=" + highlightedPositions.size() + ", 位置列表=" + highlightedPositions);
    }
    
    private void startGame() {
        startTime = System.currentTimeMillis();
        
        handler.postDelayed(() -> {
            if (gameMode == MemoryGameMode.NUMBER_CONTENT) {
                startNumberDisplay();
            } else {
                startPositionDisplay();
            }
        }, 1000);
    }
    
    private void startNumberDisplay() {
        currentDisplayIndex = 0;
        showNextNumber();
    }
    
    private void showNextNumber() {
        if (currentDisplayIndex < sequence.size()) {
            int currentNumber = sequence.get(currentDisplayIndex);
            tvCurrentNumber.setText(String.valueOf(currentNumber));
            tvProgress.setText("显示中: " + (currentDisplayIndex + 1) + "/" + difficulty);
            
            soundManager.playCorrectSound();
            
            handler.postDelayed(() -> {
                tvCurrentNumber.setText("");
                currentDisplayIndex++;
                
                handler.postDelayed(() -> {
                    showNextNumber();
                }, 300);
                
            }, 1200);
        } else {
            // 显示完毕，开始输入阶段
            handler.postDelayed(() -> {
                startNumberInput();
            }, 500);
        }
    }
    
    private void startPositionDisplay() {
        // 高亮显示位置
        for (int i = 0; i < highlightedPositions.size(); i++) {
            int position = highlightedPositions.get(i);
            final int displayNumber = i + 1;
            handler.postDelayed(() -> {
                gridButtons[position].setBackgroundColor(getResources().getColor(R.color.md_theme_light_primary));
                gridButtons[position].setText(String.valueOf(displayNumber));
                gridButtons[position].setTextColor(getResources().getColor(android.R.color.white));
                gridButtons[position].setTextSize(20);
                soundManager.playCorrectSound();
            }, i * 800);
        }
        
        // 记忆时间结束后开始输入
        handler.postDelayed(() -> {
            startPositionInput();
        }, highlightedPositions.size() * 800 + 2000);
    }
    
    private void startNumberInput() {
        tvProgress.setText("请按顺序输入刚才看到的数字");
        cardNumberDisplay.setVisibility(View.GONE);
        layoutInputArea.setVisibility(View.VISIBLE);
        
        createNumberInputButtons();
        
        btnConfirm.setOnClickListener(v -> {
            if (userInput.size() == sequence.size()) {
                checkNumberAnswer();
            }
        });
    }
    
    private void startPositionInput() {
        tvProgress.setText("请按顺序点击刚才高亮的位置");
        
        // 重置网格
        for (int i = 0; i < gridButtons.length; i++) {
            gridButtons[i].setBackgroundColor(getResources().getColor(R.color.grid_cell_normal));
            gridButtons[i].setText("");
            gridButtons[i].setTextColor(getResources().getColor(R.color.md_theme_light_onBackground));

            final int position = i;
            gridButtons[i].setOnClickListener(v -> {
                userInput.add(position);
                gridButtons[position].setBackgroundColor(getResources().getColor(R.color.grid_cell_pressed));
                gridButtons[position].setText(String.valueOf(userInput.size()));
                gridButtons[position].setTextColor(getResources().getColor(R.color.md_theme_light_primary));
                gridButtons[position].setTextSize(18);
                soundManager.playCorrectSound();

                if (userInput.size() == highlightedPositions.size()) {
                    handler.postDelayed(() -> {
                        checkPositionAnswer();
                    }, 500);
                }
            });
        }
    }
    
    private void createNumberInputButtons() {
        layoutNumberButtons.removeAllViews();

        // 创建网格布局来容纳所有按钮
        GridLayout gridLayout = new GridLayout(this);
        gridLayout.setColumnCount(5); // 每行5个按钮
        gridLayout.setRowCount(3); // 3行

        // 设置按钮大小
        int buttonSize = (int) (60 * getResources().getDisplayMetrics().density);
        int margin = (int) (4 * getResources().getDisplayMetrics().density);

        // 添加数字按钮 0-9
        for (int i = 0; i <= 9; i++) {
            MaterialButton button = new MaterialButton(this);
            button.setText(String.valueOf(i));
            button.setTextSize(16);
            button.setTextColor(getResources().getColor(android.R.color.white));

            // 设置按钮布局参数
            GridLayout.LayoutParams params = new GridLayout.LayoutParams();
            params.width = buttonSize;
            params.height = buttonSize;
            params.setMargins(margin, margin, margin, margin);
            button.setLayoutParams(params);

            final int number = i;
            button.setOnClickListener(v -> {
                if (userInput.size() < sequence.size()) {
                    userInput.add(number);
                    updateInputDisplay();
                    soundManager.playCorrectSound();
                }
            });

            gridLayout.addView(button);
        }

        // 删除按钮
        MaterialButton btnDelete = new MaterialButton(this);
        btnDelete.setText("删除");
        btnDelete.setTextSize(14);
        btnDelete.setTextColor(getResources().getColor(android.R.color.white));
        btnDelete.setBackgroundColor(getResources().getColor(R.color.error_color));

        GridLayout.LayoutParams deleteParams = new GridLayout.LayoutParams();
        deleteParams.width = buttonSize;
        deleteParams.height = buttonSize;
        deleteParams.setMargins(margin, margin, margin, margin);
        btnDelete.setLayoutParams(deleteParams);

        btnDelete.setOnClickListener(v -> {
            if (!userInput.isEmpty()) {
                userInput.remove(userInput.size() - 1);
                updateInputDisplay();
                soundManager.playCorrectSound();
            }
        });
        gridLayout.addView(btnDelete);

        layoutNumberButtons.addView(gridLayout);
    }
    
    private void updateInputDisplay() {
        StringBuilder display = new StringBuilder("已输入: ");
        for (int num : userInput) {
            display.append(num).append(" ");
        }
        tvInputPrompt.setText(display.toString());
    }
    
    private void checkNumberAnswer() {
        boolean correct = sequence.equals(userInput);
        finishGame(correct);
    }
    
    private void checkPositionAnswer() {
        boolean correct = highlightedPositions.equals(userInput);
        finishGame(correct);
    }
    
    private void finishGame(boolean success) {
        long gameTime = System.currentTimeMillis() - startTime;

        // 保存游戏结果到统计数据
        String gameKey = gameMode.getKey() + "_" + difficulty;
        GameDataManager gameDataManager = new GameDataManager(this);
        gameDataManager.saveMemoryGameResult(gameKey, gameTime, success);

        Intent intent = new Intent(this, CelebrationActivity.class);
        intent.putExtra("game_type", "memory_game");
        intent.putExtra("success", success);
        intent.putExtra("time", gameTime);
        intent.putExtra("memory_mode", gameMode.getKey());
        intent.putExtra("difficulty", difficulty);

        // 添加水晶奖励（仅成功时）
        if (success) {
            CrystalManager crystalManager = CrystalManager.getInstance(this);
            String difficultyStr = difficulty == 3 ? "easy" : difficulty == 4 ? "medium" : "hard";
            CrystalManager.CrystalReward reward = CrystalManager.getMemoryGameReward(gameMode.getKey(), difficultyStr);
            boolean newCrystalGenerated = crystalManager.addCrystalFragments(reward.type, reward.fragments);

            intent.putExtra("crystal_reward", reward.fragments);
            intent.putExtra("crystal_description", reward.description);
            intent.putExtra("new_crystal_generated", newCrystalGenerated);
        }

        startActivity(intent);
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
        if (soundManager != null) {
            soundManager.stopBackgroundMusic();
        }
    }
}
