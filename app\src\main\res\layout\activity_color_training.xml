<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/light_gradient_background"
    android:fillViewport="true">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="24dp">

        <!-- 返回按钮 -->
        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回" />

        <!-- 标题 -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="🎨 颜色识别训练"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_primary"
            android:gravity="center" />

        <!-- 教程按钮 - 灯泡图标 -->
        <Button
            android:id="@+id/btn_tutorial"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="💡"
            android:textSize="20sp"
            style="@style/Widget.Material3.Button.IconButton"
            android:background="@android:color/transparent" />

    </LinearLayout>

    <!-- 水晶积分显示 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:cardBackgroundColor="@android:color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="💎"
                android:textSize="24sp"
                android:layout_marginEnd="12dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="颜色水晶"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onBackground"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_crystal_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0个 + 0/30碎片"
                    android:textSize="12sp"
                    android:textColor="@color/md_theme_light_onSurfaceVariant" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="完成训练获得碎片"
                android:textSize="10sp"
                android:textColor="@color/md_theme_light_onSurfaceVariant"
                android:gravity="end" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 统计卡片 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="📊 训练统计"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_primary"
                android:layout_marginBottom="16dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_best_score"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="95%"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_primary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="最佳准确率"
                        android:textSize="12sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_avg_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="2.5s"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_primary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="平均反应时间"
                        android:textSize="12sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_total_games"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="42"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_primary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="总训练次数"
                        android:textSize="12sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 难度选择 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🎯 选择难度"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_primary"
                android:layout_marginBottom="16dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_easy"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:text="简单\n3x3"
                    android:textSize="14sp"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    app:strokeColor="@color/md_theme_light_primary"
                    android:layout_marginEnd="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_medium"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:text="中等\n4x4"
                    android:textSize="14sp"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    app:strokeColor="@color/md_theme_light_primary"
                    android:layout_marginHorizontal="4dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_hard"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:text="困难\n5x5"
                    android:textSize="14sp"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    app:strokeColor="@color/md_theme_light_primary"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 游戏模式选择 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🎮 游戏模式"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_primary"
                android:layout_marginBottom="16dp" />

            <!-- 游戏模式按钮组 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="3">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_color_match"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:text="🎨\n匹配"
                    android:textSize="12sp"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    app:strokeColor="@color/md_theme_light_primary"
                    android:layout_marginEnd="4dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_color_name"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:text="📝\n命名"
                    android:textSize="12sp"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    app:strokeColor="@color/md_theme_light_primary"
                    android:layout_marginHorizontal="4dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_interference"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:text="⚡\n干扰"
                    android:textSize="12sp"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    app:strokeColor="@color/md_theme_light_primary"
                    android:layout_marginStart="4dp" />

            </LinearLayout>

            <!-- 开始训练按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_start"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:layout_marginTop="20dp"
                android:text="🚀 开始训练"
                android:textSize="18sp"
                android:textStyle="bold"
                style="@style/Widget.MaterialComponents.Button"
                app:backgroundTint="@color/md_theme_light_primary"
                android:textColor="@android:color/white"
                app:cornerRadius="16dp"
                android:elevation="8dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 最近训练记录卡片 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:strokeWidth="0dp"
        app:cardBackgroundColor="@color/md_theme_light_surface"
        android:layout_marginBottom="24dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 标题行 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="最近训练记录"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSurface" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="查看全部"
                    android:textSize="14sp"
                    android:textColor="@color/md_theme_light_primary"
                    android:clickable="true"
                    android:focusable="true" />

            </LinearLayout>

            <!-- 表头 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingVertical="8dp"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:text="日期"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSurfaceVariant" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="游戏模式"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSurfaceVariant"
                    android:gravity="center" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.5"
                    android:text="用时"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSurfaceVariant"
                    android:gravity="center" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="状态"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSurfaceVariant"
                    android:gravity="center" />

            </LinearLayout>

            <!-- 分隔线 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/md_theme_light_outline"
                android:layout_marginBottom="12dp" />

            <!-- 记录列表容器 -->
            <LinearLayout
                android:id="@+id/layout_recent_records"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 这里将动态添加训练记录 -->

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</LinearLayout>

</ScrollView>
