package com.example.concentration;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.material.button.MaterialButton;

public class NumberLearningActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private TextView tvTitle, tvProgress, tvBestTime, tvTotalGames;
    private MaterialButton btnMemoryCard, btnRhythmLearning, btnPuzzleChallenge;
    
    private int currentNumber;
    private SharedPreferences prefs;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_number_learning);
        
        currentNumber = getIntent().getIntExtra("number", 1);
        prefs = getSharedPreferences("multiplication_prefs", MODE_PRIVATE);
        
        initViews();
        setupClickListeners();
        updateUI();
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        tvTitle = findViewById(R.id.tv_title);
        tvProgress = findViewById(R.id.tv_progress);
        tvBestTime = findViewById(R.id.tv_best_time);
        tvTotalGames = findViewById(R.id.tv_total_games);
        btnMemoryCard = findViewById(R.id.btn_memory_card);
        btnRhythmLearning = findViewById(R.id.btn_rhythm_learning);
        btnPuzzleChallenge = findViewById(R.id.btn_puzzle_challenge);
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        
        btnMemoryCard.setOnClickListener(v -> startMemoryCardGame());
        btnRhythmLearning.setOnClickListener(v -> startRhythmLearning());
        btnPuzzleChallenge.setOnClickListener(v -> startPuzzleChallenge());
    }
    
    private void updateUI() {
        tvTitle.setText(currentNumber + " 的乘法表");
        
        int progress = prefs.getInt("number_" + currentNumber + "_progress", 0);
        tvProgress.setText("学习进度：" + progress + "%");
        
        long bestTime = prefs.getLong("number_" + currentNumber + "_best_time", 0);
        if (bestTime > 0) {
            tvBestTime.setText("最佳时间：" + String.format("%.1f秒", bestTime / 1000.0));
        } else {
            tvBestTime.setText("最佳时间：暂无记录");
        }
        
        int totalGames = prefs.getInt("number_" + currentNumber + "_total_games", 0);
        tvTotalGames.setText("总游戏次数：" + totalGames + "次");
        
        // 根据进度解锁不同的学习模式
        updateButtonStates(progress);
    }
    
    private void updateButtonStates(int progress) {
        // 检查测试模式
        boolean testMode = SettingsActivity.isTestModeEnabled(this);

        // 节拍学习始终可用
        btnRhythmLearning.setEnabled(true);
        btnRhythmLearning.setAlpha(1.0f);

        // 记忆卡片需要20%进度或测试模式
        if (progress >= 20 || testMode) {
            btnMemoryCard.setEnabled(true);
            btnMemoryCard.setAlpha(1.0f);
        } else {
            btnMemoryCard.setEnabled(false);
            btnMemoryCard.setAlpha(0.5f);
        }

        // 答题挑战需要50%进度或测试模式
        if (progress >= 50 || testMode) {
            btnPuzzleChallenge.setEnabled(true);
            btnPuzzleChallenge.setAlpha(1.0f);
        } else {
            btnPuzzleChallenge.setEnabled(false);
            btnPuzzleChallenge.setAlpha(0.5f);
        }
    }
    
    private void startMemoryCardGame() {
        Intent intent = new Intent(this, MemoryCardGameActivity.class);
        intent.putExtra("number", currentNumber);
        startActivityForResult(intent, 1001);
    }
    
    private void startRhythmLearning() {
        Intent intent = new Intent(this, RhythmLearningActivity.class);
        intent.putExtra("number", currentNumber);
        startActivityForResult(intent, 1002);
    }
    
    private void startPuzzleChallenge() {
        Intent intent = new Intent(this, PuzzleChallengeActivity.class);
        intent.putExtra("number", currentNumber);
        startActivityForResult(intent, 1003);
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (resultCode == RESULT_OK && data != null) {
            // 更新进度
            int progressGain = data.getIntExtra("progress_gain", 0);
            if (progressGain > 0) {
                updateProgress(progressGain);
            }
            
            // 更新最佳时间
            long gameTime = data.getLongExtra("game_time", 0);
            if (gameTime > 0) {
                updateBestTime(gameTime);
            }
            
            // 更新游戏次数
            updateGameCount();
            
            // 刷新UI
            updateUI();
        }
    }
    
    private void updateProgress(int gain) {
        int currentProgress = prefs.getInt("number_" + currentNumber + "_progress", 0);
        int newProgress = Math.min(100, currentProgress + gain);
        prefs.edit().putInt("number_" + currentNumber + "_progress", newProgress).apply();

        // 奖励水晶碎片
        CrystalManager crystalManager = CrystalManager.getInstance(this);
        crystalManager.addCrystalFragments(CrystalManager.CrystalType.MULTIPLICATION, gain / 2);
    }
    
    private void updateBestTime(long gameTime) {
        long currentBest = prefs.getLong("number_" + currentNumber + "_best_time", Long.MAX_VALUE);
        if (gameTime < currentBest) {
            prefs.edit().putLong("number_" + currentNumber + "_best_time", gameTime).apply();
        }
    }
    
    private void updateGameCount() {
        int currentCount = prefs.getInt("number_" + currentNumber + "_total_games", 0);
        prefs.edit().putInt("number_" + currentNumber + "_total_games", currentCount + 1).apply();
    }
}
