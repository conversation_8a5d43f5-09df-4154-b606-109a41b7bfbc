//添加依赖库⽬录
repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {

    //广告添加相关依赖包
    implementation fileTree(dir: "libs", include: ["*.jar", "*.aar"])//导入广告相关aar和jar包
    implementation "com.android.support:design:28.0.0"
    implementation 'com.android.support:multidex:1.0.3'
    implementation 'com.github.bumptech.glide:glide:4.9.0'
    implementation 'com.google.code.gson:gson:2.8.5'
    //implementation "androidx.legacy:legacy-support-v4:1.0.0"//加入 AndroidX V4 依赖支持库

    //adalliance_adn_sdk需要引入的第三方库支持
    implementation 'com.squareup.okhttp3:okhttp:3.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:3.12.0'
}
