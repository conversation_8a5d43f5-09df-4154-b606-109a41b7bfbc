package com.example.concentration;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.GridLayout;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.material.button.MaterialButton;
import java.util.ArrayList;
import java.util.List;

public class ConceptVisualDemoActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private TextView tvTitle, tvFormula, tvExplanation;
    private GridLayout gridAnimals;
    private MaterialButton btnNext, btnComplete;
    
    private int currentLesson = 0;
    private final int totalLessons = 81;
    private Handler handler = new Handler();
    private VoiceManager voiceManager;
    private SharedPreferences prefs;

    // 动物emoji数组
    private final String[] animals = {"🐣", "🐰", "🐶", "🐱", "🐸", "🐷", "🐻", "🐼", "🐨", "🦁", "🐯", "🐮", "🐵", "🐺", "🐴", "🦄", "🐘", "🐭", "🐹", "🐰"};

    // 动物名字数组（与emoji对应）
    private final String[] animalNames = {"小鸡", "兔子", "小狗", "小猫", "青蛙", "小猪", "熊", "熊猫", "考拉", "狮子", "老虎", "奶牛", "猴子", "狼", "马", "独角兽", "大象", "老鼠", "仓鼠", "兔子"};

    // 生成九九乘法表的所有组合
    private List<int[]> multiplicationTable;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_concept_visual_demo);

        voiceManager = VoiceManager.getInstance(this);
        prefs = getSharedPreferences("multiplication_table_prefs", MODE_PRIVATE);

        generateMultiplicationTable();
        initViews();
        setupClickListeners();
        loadProgress();
        startLesson();
    }

    private void generateMultiplicationTable() {
        multiplicationTable = new ArrayList<>();
        // 按照难度递增的顺序：1×几，2×几，3×几...9×几
        for (int i = 1; i <= 9; i++) {
            for (int j = 1; j <= 9; j++) {
                multiplicationTable.add(new int[]{i, j});
            }
        }
        // 不打乱顺序，保持难度递增
    }

    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        tvTitle = findViewById(R.id.tv_title);
        tvFormula = findViewById(R.id.tv_formula);
        tvExplanation = findViewById(R.id.tv_explanation);
        gridAnimals = findViewById(R.id.grid_animals);
        btnNext = findViewById(R.id.btn_next);
        btnComplete = findViewById(R.id.btn_complete);
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        
        btnNext.setOnClickListener(v -> {
            if (currentLesson < totalLessons - 1) {
                currentLesson++;
                // 每次进入下一课都保存进度
                saveCurrentProgress();
                startLesson();
            } else {
                // 所有课程完成，保存进度并返回
                saveProgress();
                setResult(RESULT_OK);
                finish();
            }
        });

        btnComplete.setOnClickListener(v -> {
            // 保存完成进度
            saveProgress();
            setResult(RESULT_OK);
            finish();
        });
    }
    
    private void startLesson() {
        int[] multiplication = multiplicationTable.get(currentLesson);
        int rows = multiplication[0];
        int cols = multiplication[1];
        String animal = animals[currentLesson % animals.length];

        // 更新标题
        tvTitle.setText("第" + (currentLesson + 1) + "课：乘法概念学习");

        // 清空网格
        gridAnimals.removeAllViews();
        gridAnimals.setRowCount(rows * 2); // 每行需要额外空间放分隔线
        gridAnimals.setColumnCount(cols);

        // 隐藏公式和说明
        tvFormula.setVisibility(View.INVISIBLE);
        tvExplanation.setVisibility(View.INVISIBLE);

        // 创建动物网格（带行分隔线）
        createAnimalGridWithLines(rows, cols, animal);

        // 隐藏按钮，等语音完成后再显示
        btnNext.setVisibility(View.GONE);
        btnComplete.setVisibility(View.GONE);

        // 开始完整的语音引导序列
        startVoiceGuideSequence(rows, cols, animal);
    }
    
    private void createAnimalGridWithLines(int rows, int cols, String animal) {
        List<TextView> animalViews = new ArrayList<>();

        // 计算动态文字大小和内边距
        float textSize = calculateDynamicTextSize(cols);
        int padding = calculateDynamicPadding(cols);

        for (int row = 0; row < rows; row++) {
            // 为每一行创建容器
            for (int col = 0; col < cols; col++) {
                TextView animalView = new TextView(this);
                animalView.setText(animal);
                animalView.setTextSize(textSize);
                animalView.setPadding(padding, padding, padding, padding);
                animalView.setAlpha(0f); // 初始透明
                animalView.setGravity(android.view.Gravity.CENTER);

                // 设置网格参数
                GridLayout.LayoutParams params = new GridLayout.LayoutParams();
                params.rowSpec = GridLayout.spec(row * 2); // 每行占用2个位置（动物+分隔线）
                params.columnSpec = GridLayout.spec(col);
                animalView.setLayoutParams(params);

                gridAnimals.addView(animalView);
                animalViews.add(animalView);
            }

            // 在每行后添加分隔线（除了最后一行）
            if (row < rows - 1) {
                View separator = new View(this);
                separator.setBackgroundColor(0xFF4CAF50); // 绿色分隔线
                GridLayout.LayoutParams separatorParams = new GridLayout.LayoutParams();
                separatorParams.rowSpec = GridLayout.spec(row * 2 + 1);
                separatorParams.columnSpec = GridLayout.spec(0, cols);
                separatorParams.width = GridLayout.LayoutParams.MATCH_PARENT;
                separatorParams.height = 4; // 4dp高度
                separatorParams.setMargins(0, 8, 0, 8);
                separator.setLayoutParams(separatorParams);
                gridAnimals.addView(separator);
            }
        }
        
        // 逐个显示动物
        animateAnimalsAppear(animalViews, rows, cols);
    }

    /**
     * 根据列数计算动态文字大小
     * 确保最多9个动物能在一行内显示
     */
    private float calculateDynamicTextSize(int cols) {
        // 获取屏幕宽度
        android.util.DisplayMetrics displayMetrics = new android.util.DisplayMetrics();
        getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        int screenWidth = displayMetrics.widthPixels;
        float density = displayMetrics.density;

        // 减去边距和内边距的空间（更保守的计算）
        int availableWidth = screenWidth - (int)(120 * density); // 增加边距空间

        // 根据列数计算每个动物的可用宽度
        int itemWidth = availableWidth / cols;

        // 根据可用宽度和列数动态计算文字大小
        float textSize;

        // 特殊处理不同列数的情况
        switch (cols) {
            case 1:
                textSize = 64f; // 1列，超大字体
                break;
            case 2:
                textSize = 48f; // 2列，大字体
                break;
            case 3:
                textSize = 40f; // 3列，中大字体
                break;
            case 4:
                textSize = 32f; // 4列，中等字体
                break;
            case 5:
                textSize = 28f; // 5列，较小字体
                break;
            case 6:
                textSize = 24f; // 6列，小字体
                break;
            case 7:
                textSize = 22f; // 7列，更小字体
                break;
            case 8:
                textSize = 20f; // 8列，最小字体
                break;
            default:
                // 超过8列的情况，根据可用宽度计算
                if (itemWidth >= 60 * density) {
                    textSize = 20f;
                } else if (itemWidth >= 50 * density) {
                    textSize = 18f;
                } else {
                    textSize = 16f; // 最小可读字体
                }
                break;
        }

        // 确保字体不会太小
        textSize = Math.max(textSize, 16f);

        return textSize;
    }

    /**
     * 根据列数计算动态内边距
     */
    private int calculateDynamicPadding(int cols) {
        float density = getResources().getDisplayMetrics().density;

        // 根据列数直接计算内边距
        int padding;
        switch (cols) {
            case 1:
            case 2:
                padding = (int)(16 * density); // 1-2列，大内边距
                break;
            case 3:
            case 4:
                padding = (int)(12 * density); // 3-4列，中等内边距
                break;
            case 5:
            case 6:
                padding = (int)(8 * density);  // 5-6列，较小内边距
                break;
            case 7:
            case 8:
                padding = (int)(6 * density);  // 7-8列，小内边距
                break;
            default:
                padding = (int)(4 * density);  // 超过8列，最小内边距
                break;
        }

        return padding;
    }
    
    private void animateAnimalsAppear(List<TextView> animalViews, int rows, int cols) {
        for (int row = 0; row < rows; row++) {
            final int currentRow = row;
            handler.postDelayed(() -> {
                // 显示当前行的所有动物
                for (int col = 0; col < cols; col++) {
                    int index = currentRow * cols + col;
                    TextView animalView = animalViews.get(index);
                    
                    ObjectAnimator fadeIn = ObjectAnimator.ofFloat(animalView, "alpha", 0f, 1f);
                    ObjectAnimator scaleX = ObjectAnimator.ofFloat(animalView, "scaleX", 0.5f, 1f);
                    ObjectAnimator scaleY = ObjectAnimator.ofFloat(animalView, "scaleY", 0.5f, 1f);
                    
                    AnimatorSet animatorSet = new AnimatorSet();
                    animatorSet.playTogether(fadeIn, scaleX, scaleY);
                    animatorSet.setDuration(300);
                    animatorSet.start();
                }
            }, row * 500);
        }
    }
    
    private void showFormula(int rows, int cols) {
        int total = rows * cols;

        // 显示填空题形式的说明
        String explanation = "有" + rows + "行，每行有" + cols + "个，一共有____个";
        tvExplanation.setText(explanation);
        tvExplanation.setVisibility(View.VISIBLE);

        // 延迟显示答案
        handler.postDelayed(() -> {
            String formula = rows + " × " + cols + " = " + total;
            tvFormula.setText(formula);
            tvFormula.setVisibility(View.VISIBLE);

            // 显示解释
            String finalExplanation = "有" + rows + "行，每行" + cols + "个，总共" + total + "个";
            tvExplanation.setText(finalExplanation);
            tvExplanation.setVisibility(View.VISIBLE);

            // 添加动画效果
            ObjectAnimator fadeInFormula = ObjectAnimator.ofFloat(tvFormula, "alpha", 0f, 1f);
            fadeInFormula.setDuration(500);
            fadeInFormula.start();

            handler.postDelayed(() -> {
                ObjectAnimator fadeInExplanation = ObjectAnimator.ofFloat(tvExplanation, "alpha", 0f, 1f);
                fadeInExplanation.setDuration(500);
                fadeInExplanation.start();
            }, 500);
        }, 2000);
    }
    
    private void updateButtons() {
        // 始终显示"下一课"按钮，最后一课点击后自动完成
        btnNext.setText(currentLesson < totalLessons - 1 ? "下一课" : "完成学习");
        btnNext.setVisibility(View.VISIBLE);
        btnComplete.setVisibility(View.GONE);
    }
    
    private void loadProgress() {
        // 加载上次学习的课程进度
        currentLesson = prefs.getInt("visual_demo_current_lesson", 0);
        if (currentLesson >= totalLessons) {
            currentLesson = 0; // 如果已完成所有课程，从头开始
        }
    }

    private void saveCurrentProgress() {
        // 保存当前课程进度（不标记为完成）
        prefs.edit().putInt("visual_demo_current_lesson", currentLesson).apply();
    }

    private void saveProgress() {
        // 保存当前课程进度
        prefs.edit().putInt("visual_demo_current_lesson", currentLesson + 1).apply();

        // 标记课程1已完成
        prefs.edit().putBoolean("visual_demo_completed", true).apply();
    }

    private void startVoiceGuideSequence(int rows, int cols, String animal) {
        // 获取动物名字
        String animalName = getAnimalName(animal);

        // 第1步：介绍动物和观察引导
        String step1 = "这些是" + animalName + "，观察" + animalName + "的排列，有几行呀，每行有几个" + animalName + "呀";
        voiceManager.speakWithCallback(step1, () -> {
            // 第2步：逐行闪烁和介绍
            highlightRowsSequentially(rows, cols, animal, 0);
        });
    }

    /**
     * 根据动物emoji获取对应的中文名字
     */
    private String getAnimalName(String animalEmoji) {
        for (int i = 0; i < animals.length; i++) {
            if (animals[i].equals(animalEmoji)) {
                return animalNames[i];
            }
        }
        return "小动物"; // 默认名字
    }

    private void highlightRowsSequentially(int rows, int cols, String animal, int currentRow) {
        if (currentRow >= rows) {
            // 所有行介绍完毕，开始介绍每行的个数
            highlightItemsInFirstRow(rows, cols, animal, 0);
            return;
        }

        // 高亮当前行
        highlightRow(currentRow);

        String animalName = getAnimalName(animal);
        String rowText = "这是第" + (currentRow + 1) + "行" + animalName;
        voiceManager.speakWithCallback(rowText, () -> {
            // 延迟后继续下一行
            handler.postDelayed(() -> {
                highlightRowsSequentially(rows, cols, animal, currentRow + 1);
            }, 500);
        });
    }

    private void highlightItemsInFirstRow(int rows, int cols, String animal, int currentItem) {
        if (currentItem >= cols) {
            // 所有物品介绍完毕，开始总结
            concludeLesson(rows, cols, animal);
            return;
        }

        // 高亮当前物品
        highlightItem(0, currentItem);

        String itemText = String.valueOf(currentItem + 1);
        voiceManager.speakWithCallback(itemText, () -> {
            // 延迟后继续下一个物品
            handler.postDelayed(() -> {
                highlightItemsInFirstRow(rows, cols, animal, currentItem + 1);
            }, 300);
        });
    }

    private void concludeLesson(int rows, int cols, String animal) {
        String conclusion = "每行有" + cols + "个" + animal + "。这就是乘法的意思，有" + rows + "行，每行" + cols + "个，总共" + (rows * cols) + "个，就是" + rows + "乘以" + cols + "等于" + (rows * cols);
        voiceManager.speakWithCallback(conclusion, () -> {
            // 显示公式
            showFormula(rows, cols);
            // 添加动画效果让学习更生动
            startAnimationSequence(rows, cols, animal);
            // 延迟后显示下一课按钮
            handler.postDelayed(() -> {
                updateButtons();
            }, 2000); // 增加延迟时间以适应动画
        });
    }

    private void startAnimationSequence(int rows, int cols, String animal) {
        // 为前几课添加动画效果，让学习更生动
        if (currentLesson < 10) { // 只为前10课添加动画
            handler.postDelayed(() -> {
                animateGridItems(rows, cols);
            }, 500);
        }
    }

    private void animateGridItems(int rows, int cols) {
        // 逐行闪烁动画
        for (int row = 0; row < rows; row++) {
            final int currentRow = row;
            handler.postDelayed(() -> {
                animateRow(currentRow, cols);
            }, row * 300);
        }
    }

    private void animateRow(int row, int cols) {
        // 让指定行的所有元素闪烁
        for (int i = 0; i < gridAnimals.getChildCount(); i++) {
            View child = gridAnimals.getChildAt(i);

            // 只处理TextView（动物）
            if (child instanceof TextView) {
                // 计算当前TextView在第几行
                int textViewIndex = 0;
                for (int j = 0; j < i; j++) {
                    if (gridAnimals.getChildAt(j) instanceof TextView) {
                        textViewIndex++;
                    }
                }
                int currentRow = textViewIndex / cols;

                if (currentRow == row) {
                    ObjectAnimator scaleX = ObjectAnimator.ofFloat(child, "scaleX", 1.0f, 1.3f, 1.0f);
                    ObjectAnimator scaleY = ObjectAnimator.ofFloat(child, "scaleY", 1.0f, 1.3f, 1.0f);
                    scaleX.setDuration(600);
                    scaleY.setDuration(600);
                    scaleX.start();
                    scaleY.start();
                }
            }
        }
    }

    private void highlightRow(int rowIndex) {
        // 实现行高亮动画
        int cols = gridAnimals.getColumnCount();

        // 简化方法：直接根据索引计算行
        for (int i = 0; i < gridAnimals.getChildCount(); i++) {
            View child = gridAnimals.getChildAt(i);

            // 只处理TextView（动物），跳过分隔线
            if (child instanceof TextView) {
                // 计算当前TextView在第几行
                int textViewIndex = 0;
                for (int j = 0; j < i; j++) {
                    if (gridAnimals.getChildAt(j) instanceof TextView) {
                        textViewIndex++;
                    }
                }
                int currentRow = textViewIndex / cols;

                if (currentRow == rowIndex) {
                    // 放大闪烁效果
                    ObjectAnimator scaleX = ObjectAnimator.ofFloat(child, "scaleX", 1.0f, 1.2f, 1.0f);
                    ObjectAnimator scaleY = ObjectAnimator.ofFloat(child, "scaleY", 1.0f, 1.2f, 1.0f);
                    scaleX.setDuration(600);
                    scaleY.setDuration(600);
                    scaleX.start();
                    scaleY.start();
                }
            }
        }
    }

    private void highlightItem(int row, int col) {
        // 实现单个物品高亮动画
        int cols = gridAnimals.getColumnCount();

        for (int i = 0; i < gridAnimals.getChildCount(); i++) {
            View child = gridAnimals.getChildAt(i);

            // 只处理TextView（动物）
            if (child instanceof TextView) {
                // 计算当前TextView的行列位置
                int textViewIndex = 0;
                for (int j = 0; j < i; j++) {
                    if (gridAnimals.getChildAt(j) instanceof TextView) {
                        textViewIndex++;
                    }
                }
                int currentRow = textViewIndex / cols;
                int currentCol = textViewIndex % cols;

                if (currentRow == row && currentCol == col) {
                    ObjectAnimator scaleX = ObjectAnimator.ofFloat(child, "scaleX", 1.0f, 1.3f, 1.0f);
                    ObjectAnimator scaleY = ObjectAnimator.ofFloat(child, "scaleY", 1.0f, 1.3f, 1.0f);
                    scaleX.setDuration(400);
                    scaleY.setDuration(400);
                    scaleX.start();
                    scaleY.start();
                    break;
                }
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
        if (voiceManager != null) {
            voiceManager.stop();
        }
    }
}
