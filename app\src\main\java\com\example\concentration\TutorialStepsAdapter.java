package com.example.concentration;

import android.animation.ObjectAnimator;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.card.MaterialCardView;
import java.util.List;

/**
 * 教程步骤适配器
 * 显示教程步骤列表，支持高亮动画
 */
public class TutorialStepsAdapter extends RecyclerView.Adapter<TutorialStepsAdapter.StepViewHolder> {
    
    private List<String> steps;
    private int highlightedStep = -1;
    private boolean allHighlighted = false;
    
    public TutorialStepsAdapter(List<String> steps) {
        this.steps = steps;
    }
    
    @NonNull
    @Override
    public StepViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
            .inflate(R.layout.item_tutorial_step, parent, false);
        return new StepViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull StepViewHolder holder, int position) {
        String step = steps.get(position);
        holder.tvStep.setText(step);
        
        // 设置高亮状态
        if (allHighlighted || position == highlightedStep) {
            holder.setHighlighted(true);
        } else if (position < highlightedStep) {
            holder.setCompleted(true);
        } else {
            holder.setNormal();
        }
    }
    
    @Override
    public int getItemCount() {
        return steps.size();
    }
    
    /**
     * 高亮指定步骤
     */
    public void highlightStep(int step) {
        int previousStep = highlightedStep;
        highlightedStep = step;
        allHighlighted = false;
        
        // 更新之前的步骤为完成状态
        if (previousStep >= 0) {
            notifyItemChanged(previousStep);
        }
        
        // 高亮当前步骤
        if (step < steps.size()) {
            notifyItemChanged(step);
        }
    }
    
    /**
     * 高亮所有步骤
     */
    public void highlightAllSteps() {
        allHighlighted = true;
        notifyDataSetChanged();
    }
    
    /**
     * 重置所有步骤
     */
    public void resetSteps() {
        highlightedStep = -1;
        allHighlighted = false;
        notifyDataSetChanged();
    }
    
    static class StepViewHolder extends RecyclerView.ViewHolder {
        MaterialCardView cardStep;
        TextView tvStep;
        
        public StepViewHolder(@NonNull View itemView) {
            super(itemView);
            cardStep = itemView.findViewById(R.id.card_step);
            tvStep = itemView.findViewById(R.id.tv_step);
        }
        
        public void setHighlighted(boolean animated) {
            // 高亮状态 - 蓝色背景
            cardStep.setCardBackgroundColor(itemView.getContext().getResources().getColor(R.color.md_theme_light_primaryContainer));
            tvStep.setTextColor(itemView.getContext().getResources().getColor(R.color.md_theme_light_onPrimaryContainer));
            
            if (animated) {
                // 添加缩放动画
                ObjectAnimator scaleX = ObjectAnimator.ofFloat(cardStep, "scaleX", 1.0f, 1.05f, 1.0f);
                ObjectAnimator scaleY = ObjectAnimator.ofFloat(cardStep, "scaleY", 1.0f, 1.05f, 1.0f);
                scaleX.setDuration(600);
                scaleY.setDuration(600);
                scaleX.start();
                scaleY.start();
            }
        }
        
        public void setCompleted(boolean animated) {
            // 完成状态 - 绿色背景
            cardStep.setCardBackgroundColor(itemView.getContext().getResources().getColor(R.color.md_theme_light_tertiaryContainer));
            tvStep.setTextColor(itemView.getContext().getResources().getColor(R.color.md_theme_light_onTertiaryContainer));
        }
        
        public void setNormal() {
            // 普通状态 - 默认背景
            cardStep.setCardBackgroundColor(itemView.getContext().getResources().getColor(R.color.md_theme_light_surface));
            tvStep.setTextColor(itemView.getContext().getResources().getColor(R.color.md_theme_light_onSurface));
        }
    }
}
