package com.example.concentration;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;

public class CountdownActivity extends AppCompatActivity {
    
    private TextView tvCountdown;
    private TextView tvMessage;
    private View backgroundView;
    private Handler handler;
    private SoundManager soundManager;
    private VoiceManager voiceManager;
    private ThemeManager themeManager;
    
    private int gridSize;
    private String themeId;
    private boolean numbersDisappear;
    private String gameType;
    private int currentCount = 3;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_countdown);
        
        // 获取传递的参数
        gameType = getIntent().getStringExtra("game_type");
        // 舒尔特方格游戏参数
        gridSize = getIntent().getIntExtra("gridSize", 3);
        themeId = getIntent().getStringExtra("themeId");
        numbersDisappear = getIntent().getBooleanExtra("numbersDisappear", true);
        if (themeId == null) themeId = "default";
        
        initializeManagers();
        initializeViews();
        startCountdown();
    }
    
    private void initializeManagers() {
        soundManager = SoundManager.getInstance(this);
        voiceManager = VoiceManager.getInstance(this);
        themeManager = ThemeManager.getInstance(this);
    }
    
    private void initializeViews() {
        tvCountdown = findViewById(R.id.tv_countdown);
        tvMessage = findViewById(R.id.tv_message);
        backgroundView = findViewById(R.id.background_view);
        handler = new Handler();
        
        // 设置初始消息
        tvMessage.setText("准备开始 " + gridSize + "×" + gridSize + " 挑战！");
        tvCountdown.setText("3");
        
        // 应用主题样式
        applyThemeStyles();
    }
    
    private void applyThemeStyles() {
        ThemeManager.ThemeType currentTheme = themeManager.getCurrentTheme();
        
        switch (currentTheme) {
            case SPACE:
                backgroundView.setBackgroundResource(R.drawable.space_background);
                tvMessage.setText("星际救援任务即将开始！");
                break;
            case CARTOON:
                backgroundView.setBackgroundResource(R.drawable.cartoon_background);
                tvMessage.setText("魔法学院的挑战即将开始！");
                break;
            case ANIMAL:
                backgroundView.setBackgroundResource(R.drawable.animal_background);
                tvMessage.setText("森林守护者的任务即将开始！");
                break;
            default:
                backgroundView.setBackgroundResource(R.drawable.default_background);
                tvMessage.setText("数字探险即将开始！");
                break;
        }
    }
    
    private void startCountdown() {
        animateCountdownNumber();
    }
    
    private void animateCountdownNumber() {
        // 播放倒计时音效
        soundManager.playCorrectSound();

        // 语音播报倒计时
        voiceManager.speakCountdown(currentCount);

        // 缩放动画
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(tvCountdown, "scaleX", 0.5f, 1.2f, 1.0f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(tvCountdown, "scaleY", 0.5f, 1.2f, 1.0f);
        ObjectAnimator alpha = ObjectAnimator.ofFloat(tvCountdown, "alpha", 0.3f, 1.0f);
        
        scaleX.setDuration(1000);
        scaleY.setDuration(1000);
        alpha.setDuration(1000);
        
        scaleX.setInterpolator(new AccelerateDecelerateInterpolator());
        scaleY.setInterpolator(new AccelerateDecelerateInterpolator());
        alpha.setInterpolator(new AccelerateDecelerateInterpolator());
        
        scaleX.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                currentCount--;
                if (currentCount > 0) {
                    tvCountdown.setText(String.valueOf(currentCount));
                    handler.postDelayed(() -> animateCountdownNumber(), 0);
                } else {
                    // 直接开始游戏，不显示"开始"
                    startGame();
                }
            }
        });
        
        scaleX.start();
        scaleY.start();
        alpha.start();
    }
    

    
    private void startGame() {
        Intent intent;
        if ("color_training".equals(gameType)) {
            // 启动颜色识别游戏
            intent = new Intent(this, ColorGameActivity.class);
            intent.putExtra("difficulty", getIntent().getStringExtra("difficulty"));
            intent.putExtra("game_mode", getIntent().getStringExtra("game_mode"));
            intent.putExtra("grid_size", getIntent().getIntExtra("grid_size", 3));
            intent.putExtra("total_rounds", getIntent().getIntExtra("total_rounds", 10));
        } else if ("memory_game".equals(gameType)) {
            // 启动记忆游戏
            intent = new Intent(this, MemoryGameActivity.class);
            intent.putExtra("memory_mode", getIntent().getStringExtra("memory_mode"));
            intent.putExtra("difficulty", getIntent().getIntExtra("difficulty", 7));
        } else {
            // 启动舒尔特方格游戏
            intent = new Intent(this, GameActivity.class);
            intent.putExtra("gridSize", gridSize);
            intent.putExtra("themeId", themeId);
            intent.putExtra("numbersDisappear", numbersDisappear);
        }
        startActivity(intent);
        finish();

        // 添加页面切换动画
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
        if (voiceManager != null) {
            voiceManager.stop();
        }
        if (soundManager != null) {
            soundManager.stopBackgroundMusic();
        }
    }
}
