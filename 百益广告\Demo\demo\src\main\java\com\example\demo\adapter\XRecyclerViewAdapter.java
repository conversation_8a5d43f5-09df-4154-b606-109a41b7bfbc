package com.example.demo.adapter;

import android.content.Context;
import android.support.annotation.LayoutRes;
import android.support.annotation.NonNull;
import android.support.v7.widget.RecyclerView;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.example.demo.bean.AdapterTypeBean;

import java.util.List;

/**
 * https://gitee.com/chm006/projects
 * <AUTHOR> 2018-11-19
 */

public abstract class XRecyclerViewAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private LayoutInflater inflater;//布局器
    private List<AdapterTypeBean> mData;
    private XRecyclerViewAdapter.OnItemClickListener mOnItemClickListener;
    private @LayoutRes
    int[] resources;

    public abstract void onBindViewHolder(XRecyclerViewAdapter.XViewHolder viewHolder, AdapterTypeBean itemData, int position);

    public interface OnItemClickListener {
        void onItemClick(View itemView, AdapterTypeBean itemData, int viewType, int position);
    }

    public XRecyclerViewAdapter(Context context, List<AdapterTypeBean> data, XRecyclerViewAdapter.OnItemClickListener onItemClickListener, @LayoutRes int... resources) {
        this.inflater = LayoutInflater.from(context);
        this.mData = data;
        this.mOnItemClickListener = onItemClickListener;
        this.resources = resources;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, final int viewType) {
        final View itemView = inflater.inflate(resources[viewType], parent, false);
        final XRecyclerViewAdapter.XViewHolder viewHolder = new XViewHolder(itemView);
        itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    int position = viewHolder.getLayoutPosition();
                    mOnItemClickListener.onItemClick(itemView, mData.get(position), viewType, position);
                }
            }
        });
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof XRecyclerViewAdapter.XViewHolder) {
            XRecyclerViewAdapter.XViewHolder viewHolder = (XRecyclerViewAdapter.XViewHolder) holder;
            onBindViewHolder(viewHolder, mData.get(position), position);
        }
    }

    @Override
    public int getItemCount() {
        return (mData == null || mData.size() == 0) ? 0 : mData.size();//如果有header,若list不存在或大小为0就没有footView，反之则有
    }

    //根据位置判断这里该用哪个ViewHolder
    @Override
    public int getItemViewType(int position) {
        AdapterTypeBean typeBean = mData.get(position);
        return typeBean.getType();
    }

    public static class XViewHolder extends RecyclerView.ViewHolder {
        private SparseArray<View> viewHolder;
        private View view;

        XViewHolder(View itemView) {
            super(itemView);
            this.view = itemView;
            viewHolder = new SparseArray<>();
        }

        public View get(int id) {
            View childView = viewHolder.get(id);
            if (childView == null) {
                childView = view.findViewById(id);
                viewHolder.put(id, childView);
            }
            return childView;
        }

        public View getConvertView() {
            return view;
        }

        public TextView getTextView(int id) {
            return (TextView) get(id);
        }

        public Button getButton(int id) {
            return (Button) get(id);
        }

        public ImageView getImageView(int id) {
            return (ImageView) get(id);
        }
    }
}
