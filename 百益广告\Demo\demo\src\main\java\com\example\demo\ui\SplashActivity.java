package com.example.demo.ui;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v7.app.AppCompatActivity;
import android.view.Window;
import android.widget.FrameLayout;
import android.widget.Toast;

import com.example.demo.R;
import com.example.demo.TbConfig;

//开屏
public class SplashActivity extends AppCompatActivity {

    // 设置一个变量来控制当前开屏页面是否可以跳转，
    // 当开屏为普链类时，点击会打开一个落地页，此时开发者还不能打开自己的App主页。
    // 当从落地页返回以后，才可以跳转到开发者自己的App主页；
    // 当开屏是App类时只会下载App。
    private boolean canJump;

    private FrameLayout mSplashContainer;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        requestWindowFeature(Window.FEATURE_NO_TITLE);// 隐藏标题栏
        getSupportActionBar().hide();// 隐藏ActionBar
        //getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);// 隐藏状态栏

        setContentView(R.layout.activity_splash);
        mSplashContainer = (FrameLayout) findViewById(R.id.splash_container);

        loadSplash();
    }

    @Override
    protected void onResume() {
        super.onResume();
        next();
        canJump = true;
    }

    @Override
    protected void onPause() {
        super.onPause();
        canJump = false;
    }

    private void next() {
        if (canJump) {
            //跳转页面的逻辑
            finish();
        } else {
            canJump = true;
        }
    }

    private void loadSplash() {
        com.by.mob.config.BySplashConfig config = new com.by.mob.config.BySplashConfig.Builder()
                .codeId(TbConfig.splashCodeId)//平台申请的代码位id
                .container(mSplashContainer)//承载视图的容器。可⾃定义⾼度⽐例,但不能低于0.75
                .build();
        com.by.mob.ByManager.loadSplash(config, this, new com.by.mob.ByManager.SplashLoadListener() {
            @Override
            public void onFail(String s) {
                //加载失败
                Toast.makeText(SplashActivity.this, s, Toast.LENGTH_SHORT).show();
                mSplashContainer.removeAllViews();
                next();//使用【开屏+】，不要关闭本页面
            }

            @Override
            public void onTick(long l) {

            }

            @Override
            public void onClicked() {

            }

            @Override
            public void onDismiss() {
                //被关闭
                mSplashContainer.removeAllViews();
                next();//使用【开屏+】，不要关闭本页面
            }

            @Override
            public void onExposure(com.by.mob.bean.Position position) {

            }
        });
    }
}
