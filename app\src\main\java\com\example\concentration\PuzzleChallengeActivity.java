package com.example.concentration;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.material.button.MaterialButton;

public class PuzzleChallengeActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private TextView tvTitle, tvQuestion, tvScore;
    private MaterialButton btnAnswer1, btnAnswer2, btnAnswer3, btnAnswer4;
    private MaterialButton btnNext;
    
    private int currentNumber;
    private SharedPreferences prefs;
    private int currentQuestion = 0;
    private int score = 0;
    private long startTime;
    private int correctAnswer;
    private boolean answered = false;
    private SoundManager soundManager;
    private VoiceManager voiceManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_puzzle_challenge);
        
        currentNumber = getIntent().getIntExtra("number", 1);
        prefs = getSharedPreferences("multiplication_prefs", MODE_PRIVATE);
        soundManager = SoundManager.getInstance(this);
        voiceManager = VoiceManager.getInstance(this);

        initViews();
        setupClickListeners();
        setupQuestion();

        startTime = System.currentTimeMillis();
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        tvTitle = findViewById(R.id.tv_title);
        tvQuestion = findViewById(R.id.tv_question);
        tvScore = findViewById(R.id.tv_score);
        btnAnswer1 = findViewById(R.id.btn_answer1);
        btnAnswer2 = findViewById(R.id.btn_answer2);
        btnAnswer3 = findViewById(R.id.btn_answer3);
        btnAnswer4 = findViewById(R.id.btn_answer4);
        btnNext = findViewById(R.id.btn_next);
        
        tvTitle.setText(currentNumber + " 的答题挑战");
        tvScore.setText("得分: " + score);
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        btnNext.setOnClickListener(v -> nextQuestion());
        
        btnAnswer1.setOnClickListener(v -> checkAnswer(1));
        btnAnswer2.setOnClickListener(v -> checkAnswer(2));
        btnAnswer3.setOnClickListener(v -> checkAnswer(3));
        btnAnswer4.setOnClickListener(v -> checkAnswer(4));
    }
    
    private void setupQuestion() {
        int multiplier = currentQuestion + 1;
        correctAnswer = currentNumber * multiplier;
        answered = false;

        String questionText = currentNumber + " × " + multiplier + " = ?";
        tvQuestion.setText(questionText);

        // 语音读出题目
        voiceManager.speakMultiplicationQuestion(currentNumber, multiplier);

        // 生成4个选项，包括正确答案和3个错误答案
        int[] options = new int[4];
        options[0] = correctAnswer;
        options[1] = correctAnswer + (int)(Math.random() * 5) + 1;
        options[2] = correctAnswer - (int)(Math.random() * 5) - 1;
        options[3] = correctAnswer + (int)(Math.random() * 10) + 6;

        // 确保没有重复和负数
        for (int i = 1; i < 4; i++) {
            if (options[i] <= 0) options[i] = correctAnswer + i + 5;
            for (int j = 0; j < i; j++) {
                if (options[i] == options[j]) {
                    options[i] = correctAnswer + i * 3 + 10;
                }
            }
        }

        // 随机打乱选项位置
        for (int i = 0; i < 4; i++) {
            int randomIndex = (int)(Math.random() * 4);
            int temp = options[i];
            options[i] = options[randomIndex];
            options[randomIndex] = temp;
        }

        btnAnswer1.setText(String.valueOf(options[0]));
        btnAnswer2.setText(String.valueOf(options[1]));
        btnAnswer3.setText(String.valueOf(options[2]));
        btnAnswer4.setText(String.valueOf(options[3]));

        // 重置按钮状态
        resetButtonColors();
        btnNext.setEnabled(false);
    }

    private void resetButtonColors() {
        btnAnswer1.setBackgroundColor(0xFF6200EE);
        btnAnswer2.setBackgroundColor(0xFF6200EE);
        btnAnswer3.setBackgroundColor(0xFF6200EE);
        btnAnswer4.setBackgroundColor(0xFF6200EE);
    }
    
    private void checkAnswer(int answerIndex) {
        if (answered) return;
        answered = true;

        MaterialButton selectedButton = null;
        String selectedText = "";

        switch (answerIndex) {
            case 1:
                selectedButton = btnAnswer1;
                selectedText = btnAnswer1.getText().toString();
                break;
            case 2:
                selectedButton = btnAnswer2;
                selectedText = btnAnswer2.getText().toString();
                break;
            case 3:
                selectedButton = btnAnswer3;
                selectedText = btnAnswer3.getText().toString();
                break;
            case 4:
                selectedButton = btnAnswer4;
                selectedText = btnAnswer4.getText().toString();
                break;
        }

        int selectedValue = Integer.parseInt(selectedText);

        if (selectedValue == correctAnswer) {
            // 答对了
            selectedButton.setBackgroundColor(0xFF4CAF50); // 绿色
            score += 10;
            soundManager.playCorrectSound();
        } else {
            // 答错了
            selectedButton.setBackgroundColor(0xFFF44336); // 红色
            soundManager.playWrongSound();
            // 显示正确答案
            highlightCorrectAnswer();
        }

        tvScore.setText("得分: " + score);
        btnNext.setEnabled(true);
    }

    private void highlightCorrectAnswer() {
        if (btnAnswer1.getText().toString().equals(String.valueOf(correctAnswer))) {
            btnAnswer1.setBackgroundColor(0xFF4CAF50);
        } else if (btnAnswer2.getText().toString().equals(String.valueOf(correctAnswer))) {
            btnAnswer2.setBackgroundColor(0xFF4CAF50);
        } else if (btnAnswer3.getText().toString().equals(String.valueOf(correctAnswer))) {
            btnAnswer3.setBackgroundColor(0xFF4CAF50);
        } else if (btnAnswer4.getText().toString().equals(String.valueOf(correctAnswer))) {
            btnAnswer4.setBackgroundColor(0xFF4CAF50);
        }
    }
    
    private void nextQuestion() {
        currentQuestion++;
        if (currentQuestion >= 9) {
            // 挑战完成
            gameCompleted();
        } else {
            setupQuestion();
        }
    }
    
    private void gameCompleted() {
        long gameTime = System.currentTimeMillis() - startTime;

        // 奖励水晶碎片
        CrystalManager crystalManager = CrystalManager.getInstance(this);
        crystalManager.addCrystalFragments(CrystalManager.CrystalType.MULTIPLICATION, 12);

        // 返回结果
        Intent result = new Intent();
        result.putExtra("progress_gain", 25); // 答题挑战获得25%进度
        result.putExtra("game_time", gameTime);
        setResult(RESULT_OK, result);
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (voiceManager != null) {
            voiceManager.stop();
        }
        if (soundManager != null) {
            soundManager.stopBackgroundMusic();
        }
    }
}
