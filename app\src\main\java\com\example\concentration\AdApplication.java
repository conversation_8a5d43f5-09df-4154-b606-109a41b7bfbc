package com.example.concentration;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import androidx.multidex.MultiDex;

public class AdApplication extends Application {

    private static final String TAG = "AdApplication";

    public static com.by.mob.config.ByInitConfig config = new com.by.mob.config.ByInitConfig.Builder()
            .appId(AdConfig.appId)//初始化id（平台上申请：应用列表的应用id）
            //.isTest(1) // 测试模式，正式发布时请注释掉
            .build();

    @Override
    public void onCreate() {
        super.onCreate();
        
        // 检查是否是首次启动，如果不是首次启动则初始化广告SDK
        SharedPreferences prefs = getSharedPreferences("ad_prefs", MODE_PRIVATE);
        boolean isFirstInit = prefs.getBoolean("is_first_init", true);
        
        if (!isFirstInit) {
            initAdSdk();
        }
    }

    //SDK初始化
    public void initAdSdk() {
        Log.d(TAG, "initAdSdk");
        
        // 初始化OAID
        initOaid();
        
        // 初始化广告SDK
        com.by.mob.ByManager.init(this, config, new com.by.mob.ByManager.IsInitListener() {
            @Override
            public void onFail(String s) {
                Log.d(TAG, "Ad SDK init onFail=" + s);
            }

            @Override
            public void onSuccess() {
                Log.d(TAG, "Ad SDK init onSuccess");
            }

            @Override
            public void onDpSuccess() {
                Log.d(TAG, "Ad SDK init onDpSuccess");
            }
        });
    }
    
    //广告初始化【ByManager.init】之前执行oaid_sdk初始化操作
    private void initOaid() {
        try {
            int code = com.bun.miitmdid.core.MdidSdkHelper.InitSdk(this, true,
                    new com.bun.miitmdid.interfaces.IIdentifierListener() {
                        @Override
                        public void OnSupport(boolean b, com.bun.miitmdid.interfaces.IdSupplier idSupplier) {
                            Log.d(TAG, "OAID init OnSupport: " + b);
                        }
                    });
            Log.d(TAG, "OAID init code: " + code);
        } catch (Exception e) {
            Log.e(TAG, "OAID init failed", e);
        }
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        MultiDex.install(this);
    }
}
