package com.example.demo.ui;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v7.app.AppCompatActivity;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;

import com.example.demo.R;
import com.example.demo.TbConfig;

//激励视频
public class RewardVideoActivity extends AppCompatActivity implements View.OnClickListener {

    private Button tv_horizontal;
    private Button tv_vertical;
    private Button tv_vertical_now;

    private View progressBar;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_reward_video);
        tv_horizontal = findViewById(R.id.btn_reward_load);
        tv_vertical = findViewById(R.id.btn_reward_load_vertical);
        tv_vertical_now = findViewById(R.id.btn_reward_load_vertical_now);
        tv_horizontal.setOnClickListener(this);
        tv_vertical.setOnClickListener(this);
        tv_vertical_now.setOnClickListener(this);

        progressBar = findViewById(R.id.a_RewardVideo_ProgressBar);
        progressBar.setVisibility(View.GONE);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.btn_reward_load) {
            tv_horizontal.setClickable(false);//防止重复点击
            progressBar.setVisibility(View.VISIBLE);//提示用户加载中
            loadRewardVideo(false, com.by.mob.ByManager.Orientation.VIDEO_HORIZONTAL);
        } else if (id == R.id.btn_reward_load_vertical) {
            tv_vertical.setClickable(false);//防止重复点击
            progressBar.setVisibility(View.VISIBLE);//提示用户加载中
            loadRewardVideo(false, com.by.mob.ByManager.Orientation.VIDEO_VERTICAL);
        } else if (id == R.id.btn_reward_load_vertical_now) {
            tv_vertical_now.setClickable(false);//防止重复点击
            progressBar.setVisibility(View.VISIBLE);//提示用户加载中
            loadRewardVideo(true, com.by.mob.ByManager.Orientation.VIDEO_VERTICAL);
        }
    }

    private void loadRewardVideo(final boolean playNow, com.by.mob.ByManager.Orientation orientation) {
        com.by.mob.config.ByRewardVideoConfig config = new com.by.mob.config.ByRewardVideoConfig.Builder()
                .codeId(TbConfig.rewardVideoCodeId)//平台申请的代码位id
                .userId("userId")//必填参数，用户ID或者设备唯一标识（服务器回调时也需要）
                .callExtraData("extraData")//服务器回调额外信息（可不填）【如果包含特殊字符，请先用UrlEncode转译后再传入】
                .orientation(orientation)//必填参数，期望视频的播放方向：VIDEO_HORIZONTAL 或 VIDEO_VERTICAL
                .build();
        com.by.mob.ByManager.loadRewardVideo(config, this, new com.by.mob.ByManager.RewardVideoLoadListener() {
            @Override
            public void onFail(String s) {
                //加载失败
                tv_horizontal.setClickable(true);
                tv_vertical.setClickable(true);
                tv_vertical_now.setClickable(true);
                progressBar.setVisibility(View.GONE);//加载中弹窗隐藏
                Toast.makeText(RewardVideoActivity.this, s, Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onExposure(String orderNo, com.by.mob.bean.Position position) {//如果使用服务器回调，可以将orderNo传给自己服务器来做后续的判断，也可以直接根据服务器回调直接发放奖励。
                tv_horizontal.setClickable(true);
                tv_vertical.setClickable(true);
                tv_vertical_now.setClickable(true);
                progressBar.setVisibility(View.GONE);//加载中弹窗隐藏
            }

            @Override
            public void getSDKID(Integer integer, String s) {

            }

            @Override
            public void onClick() {

            }

            @Override
            public void onClose() {
                //视频被关闭
            }

            @Override
            public void onRewardVideoCached(com.ads.admob.bean.RewardPosition position) {
                //在线播放没有此回调
            }

            @Override
            public void onSkippedVideo() {

            }

            @Override
            public void onRewardVerify() {
                //激励视频触发激励（观看视频大于一定时长或者视频播放完毕）
            }
        });
    }
}
