package com.example.concentration;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import com.google.android.material.button.MaterialButton;

public class MultiplicationTableActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private MaterialButton btnTutorial;
    private CardView cardStage1, cardStage2, cardStage3;
    private TextView tvStage1Progress, tvStage2Progress, tvStage3Progress;
    private TextView tvStage1Status, tvStage2Status, tvStage3Status;
    private LinearLayout layoutRecentRecords;

    private SharedPreferences prefs;
    private GameDataManager gameDataManager;
    private TutorialManager tutorialManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_multiplication_table);
        
        initializeManagers();
        initViews();
        setupClickListeners();
        loadProgress();
        setupRecentRecords();

        // 检查是否需要显示教程
        checkAndShowTutorial();
    }
    
    private void initializeManagers() {
        prefs = getSharedPreferences("multiplication_table_prefs", MODE_PRIVATE);
        gameDataManager = new GameDataManager(this);
        tutorialManager = TutorialManager.getInstance(this);
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        btnTutorial = findViewById(R.id.btn_tutorial);

        // 三个学习阶段卡片
        cardStage1 = findViewById(R.id.card_stage1);
        cardStage2 = findViewById(R.id.card_stage2);
        cardStage3 = findViewById(R.id.card_stage3);
        
        // 进度显示
        tvStage1Progress = findViewById(R.id.tv_stage1_progress);
        tvStage2Progress = findViewById(R.id.tv_stage2_progress);
        tvStage3Progress = findViewById(R.id.tv_stage3_progress);
        
        // 状态显示
        tvStage1Status = findViewById(R.id.tv_stage1_status);
        tvStage2Status = findViewById(R.id.tv_stage2_status);
        tvStage3Status = findViewById(R.id.tv_stage3_status);
        
        // 最近学习记录
        layoutRecentRecords = findViewById(R.id.layout_recent_records);
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        btnTutorial.setOnClickListener(v -> showTutorial());

        cardStage1.setOnClickListener(v -> startStage1());
        cardStage2.setOnClickListener(v -> startStage2());
        cardStage3.setOnClickListener(v -> startStage3());
    }
    
    private void loadProgress() {
        // 加载各阶段进度
        int stage1Progress = prefs.getInt("stage1_progress", 0);
        int stage2Progress = prefs.getInt("stage2_progress", 0);
        int stage3Progress = prefs.getInt("stage3_progress", 0);
        
        // 更新进度显示
        tvStage1Progress.setText("进度: " + stage1Progress + "%");
        tvStage2Progress.setText("进度: " + stage2Progress + "%");
        tvStage3Progress.setText("进度: " + stage3Progress + "%");
        
        // 更新状态显示
        updateStageStatus(stage1Progress, stage2Progress, stage3Progress);
        
        // 更新卡片可用性
        updateCardAvailability(stage1Progress, stage2Progress, stage3Progress);
    }
    
    private void updateStageStatus(int stage1, int stage2, int stage3) {
        // 阶段1状态
        if (stage1 == 0) {
            tvStage1Status.setText("🌟 开始学习");
        } else if (stage1 < 100) {
            tvStage1Status.setText("📚 学习中...");
        } else {
            tvStage1Status.setText("✅ 已完成");
        }
        
        // 阶段2状态
        if (stage1 < 80) {
            tvStage2Status.setText("🔒 需要完成阶段1");
        } else if (stage2 == 0) {
            tvStage2Status.setText("🌟 可以开始");
        } else if (stage2 < 100) {
            tvStage2Status.setText("📚 学习中...");
        } else {
            tvStage2Status.setText("✅ 已完成");
        }
        
        // 阶段3状态
        if (stage2 < 80) {
            tvStage3Status.setText("🔒 需要完成阶段2");
        } else if (stage3 == 0) {
            tvStage3Status.setText("🌟 可以开始");
        } else if (stage3 < 100) {
            tvStage3Status.setText("📚 学习中...");
        } else {
            tvStage3Status.setText("✅ 已完成");
        }
    }
    
    private void updateCardAvailability(int stage1, int stage2, int stage3) {
        // 检查测试模式
        boolean testMode = SettingsActivity.isTestModeEnabled(this);

        // 阶段1始终可用
        cardStage1.setAlpha(1.0f);
        cardStage1.setClickable(true);

        // 阶段2需要阶段1达到80%或测试模式开启
        if (stage1 >= 80 || testMode) {
            cardStage2.setAlpha(1.0f);
            cardStage2.setClickable(true);
        } else {
            cardStage2.setAlpha(0.5f);
            cardStage2.setClickable(false);
        }

        // 阶段3需要阶段2达到80%或测试模式开启
        if (stage2 >= 80 || testMode) {
            cardStage3.setAlpha(1.0f);
            cardStage3.setClickable(true);
        } else {
            cardStage3.setAlpha(0.5f);
            cardStage3.setClickable(false);
        }
    }
    
    private void startStage1() {
        Intent intent = new Intent(this, MultiplicationConceptActivity.class);
        startActivity(intent);
    }
    
    private void startStage2() {
        int stage1Progress = prefs.getInt("stage1_progress", 0);
        boolean testMode = SettingsActivity.isTestModeEnabled(this);
        if (stage1Progress < 80 && !testMode) {
            // 显示提示需要先完成阶段1
            return;
        }
        Intent intent = new Intent(this, MultiplicationNumberActivity.class);
        startActivity(intent);
    }

    private void startStage3() {
        int stage2Progress = prefs.getInt("stage2_progress", 0);
        boolean testMode = SettingsActivity.isTestModeEnabled(this);
        if (stage2Progress < 80 && !testMode) {
            // 显示提示需要先完成阶段2
            return;
        }
        Intent intent = new Intent(this, MultiplicationChallengeActivity.class);
        startActivityForResult(intent, 3);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == 3 && resultCode == RESULT_OK && data != null) {
            // Stage 3 挑战完成
            int level = data.getIntExtra("level", 1);
            int score = data.getIntExtra("score", 0);
            int accuracy = data.getIntExtra("accuracy", 0);
            long gameTime = data.getLongExtra("game_time", 0);

            // 保存Stage 3进度
            int stage3Progress = Math.min(level * 10 + accuracy / 10, 100);
            prefs.edit().putInt("stage3_progress", stage3Progress).apply();

            // 刷新界面
            loadProgress();
        }
    }
    
    private void setupRecentRecords() {
        loadMultiplicationRecords(layoutRecentRecords);
    }

    private void loadMultiplicationRecords(LinearLayout container) {
        // 获取乘法表学习的统计数据
        int totalSessions = prefs.getInt("total_sessions", 0);
        int stage1Progress = prefs.getInt("stage1_progress", 0);
        int stage2Progress = prefs.getInt("stage2_progress", 0);
        int stage3Progress = prefs.getInt("stage3_progress", 0);

        // 获取挑战游戏统计
        int bestScore = gameDataManager.getMultiplicationBestScore();
        int bestLevel = gameDataManager.getMultiplicationBestLevel();
        int totalGames = gameDataManager.getMultiplicationTotalGames();
        int todayGames = gameDataManager.getMultiplicationTodayGames();

        if (totalSessions == 0 && totalGames == 0) {
            // 没有记录时显示占位文本
            TextView placeholder = new TextView(this);
            placeholder.setText("开始你的乘法表学习之旅吧！");
            placeholder.setTextSize(14);
            placeholder.setTextColor(getColor(R.color.md_theme_light_onSurfaceVariant));
            placeholder.setPadding(0, 16, 0, 16);
            placeholder.setGravity(android.view.Gravity.CENTER);
            container.addView(placeholder);
        } else {
            // 显示学习进度记录
            addRecordRow(container, "基础概念", "阶段1", stage1Progress + "%", getStageStatusText(stage1Progress));
            addRecordRow(container, "数字学习", "阶段2", stage2Progress + "%", getStageStatusText(stage2Progress));
            addRecordRow(container, "综合应用", "阶段3", stage3Progress + "%", getStageStatusText(stage3Progress));

            // 添加挑战游戏统计
            if (totalGames > 0) {
                addRecordRow(container, "最佳成绩", "挑战模式", bestScore + "分", "关卡" + bestLevel);
                addRecordRow(container, "游戏次数", "总计/今日", totalGames + "/" + todayGames, "继续挑战!");
            }

            // 添加总体统计
            int overallProgress = (stage1Progress + stage2Progress + stage3Progress) / 3;
            addRecordRow(container, "总体进度", "全部阶段", overallProgress + "%", "继续加油!");
        }
    }
    
    private String getStageStatusText(int progress) {
        if (progress == 0) return "未开始";
        if (progress < 50) return "初学者";
        if (progress < 80) return "进步中";
        if (progress < 100) return "接近完成";
        return "已掌握";
    }
    
    private void addRecordRow(LinearLayout container, String stage, String type, String progress, String status) {
        LinearLayout row = new LinearLayout(this);
        row.setOrientation(LinearLayout.HORIZONTAL);
        row.setPadding(0, 8, 0, 8);

        // 阶段
        TextView tvStage = new TextView(this);
        tvStage.setText(stage);
        tvStage.setTextSize(12);
        tvStage.setTextColor(getColor(R.color.md_theme_light_onSurface));
        tvStage.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));

        // 类型
        TextView tvType = new TextView(this);
        tvType.setText(type);
        tvType.setTextSize(12);
        tvType.setTextColor(getColor(R.color.md_theme_light_onSurface));
        tvType.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));
        tvType.setGravity(android.view.Gravity.CENTER);

        // 进度
        TextView tvProgress = new TextView(this);
        tvProgress.setText(progress);
        tvProgress.setTextSize(12);
        tvProgress.setTextColor(getColor(R.color.md_theme_light_onSurface));
        tvProgress.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));
        tvProgress.setGravity(android.view.Gravity.CENTER);

        // 状态
        TextView tvStatus = new TextView(this);
        tvStatus.setText(status);
        tvStatus.setTextSize(12);
        tvStatus.setTextColor(getColor(R.color.md_theme_light_primary));
        tvStatus.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));
        tvStatus.setGravity(android.view.Gravity.END);

        row.addView(tvStage);
        row.addView(tvType);
        row.addView(tvProgress);
        row.addView(tvStatus);

        container.addView(row);
    }
    
    /**
     * 检查并显示教程
     */
    private void checkAndShowTutorial() {
        if (tutorialManager.isFirstTime(TutorialManager.GameType.MULTIPLICATION_TABLE)) {
            showTutorial();
        }
    }

    /**
     * 显示教程
     */
    private void showTutorial() {
        Intent intent = new Intent(this, TutorialActivity.class);
        intent.putExtra("game_type", TutorialManager.GameType.MULTIPLICATION_TABLE.getKey());
        startActivity(intent);
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 重新加载进度，以防从子Activity返回后有更新
        loadProgress();
    }
}
