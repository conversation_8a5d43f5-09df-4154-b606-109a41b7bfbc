package com.example.concentration;

import android.content.Context;
import android.content.SharedPreferences;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class GameDataManager {
    private static final String PREFS_NAME = "concentration_game_data";
    private static final String KEY_BEST_TIME_3X3 = "best_time_3x3";
    private static final String KEY_BEST_TIME_4X4 = "best_time_4x4";
    private static final String KEY_BEST_TIME_5X5 = "best_time_5x5";
    private static final String KEY_BEST_TIME_6X6 = "best_time_6x6";

    // 序列记忆游戏最佳成绩
    private static final String KEY_SEQUENCE_BEST_3 = "sequence_best_3";
    private static final String KEY_SEQUENCE_BEST_4 = "sequence_best_4";
    private static final String KEY_SEQUENCE_BEST_5 = "sequence_best_5";
    private static final String KEY_SEQUENCE_BEST_6 = "sequence_best_6";
    private static final String KEY_SEQUENCE_BEST_7 = "sequence_best_7";

    // 乘法表游戏相关键
    private static final String KEY_MULTIPLICATION_BEST_SCORE = "multiplication_best_score";
    private static final String KEY_MULTIPLICATION_BEST_LEVEL = "multiplication_best_level";
    private static final String KEY_MULTIPLICATION_TOTAL_GAMES = "multiplication_total_games";
    private static final String KEY_MULTIPLICATION_TODAY_GAMES = "multiplication_today_games";
    private static final String KEY_TODAY_COUNT = "today_count";
    private static final String KEY_TOTAL_COUNT = "total_count";
    private static final String KEY_STREAK_DAYS = "streak_days";
    private static final String KEY_LAST_PLAY_DATE = "last_play_date";

    // 游戏次数相关键
    private static final String KEY_PLAY_COUNT_3X3 = "play_count_3x3";
    private static final String KEY_PLAY_COUNT_4X4 = "play_count_4x4";
    private static final String KEY_PLAY_COUNT_5X5 = "play_count_5x5";
    private static final String KEY_PLAY_COUNT_6X6 = "play_count_6x6";

    // 序列记忆游戏游戏次数
    private static final String KEY_SEQUENCE_COUNT_3 = "sequence_count_3";
    private static final String KEY_SEQUENCE_COUNT_4 = "sequence_count_4";
    private static final String KEY_SEQUENCE_COUNT_5 = "sequence_count_5";
    private static final String KEY_SEQUENCE_COUNT_6 = "sequence_count_6";
    private static final String KEY_SEQUENCE_COUNT_7 = "sequence_count_7";

    // 总时间相关键（用于计算平均时间）
    private static final String KEY_TOTAL_TIME_3X3 = "total_time_3x3";
    private static final String KEY_TOTAL_TIME_4X4 = "total_time_4x4";
    private static final String KEY_TOTAL_TIME_5X5 = "total_time_5x5";
    private static final String KEY_TOTAL_TIME_6X6 = "total_time_6x6";

    // 今日游戏次数相关键
    private static final String KEY_TODAY_COUNT_3X3 = "today_count_3x3";
    private static final String KEY_TODAY_COUNT_4X4 = "today_count_4x4";
    private static final String KEY_TODAY_COUNT_5X5 = "today_count_5x5";
    private static final String KEY_TODAY_COUNT_6X6 = "today_count_6x6";
    private static final String KEY_TODAY_DATE = "today_date";
    
    private SharedPreferences prefs;
    private Context context;
    
    public GameDataManager(Context context) {
        this.context = context;
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    // 保存最佳成绩
    public void saveBestTime(int gridSize, long timeInMillis) {
        String key = getBestTimeKey(gridSize);
        long currentBest = prefs.getLong(key, Long.MAX_VALUE);
        
        if (timeInMillis < currentBest) {
            prefs.edit().putLong(key, timeInMillis).apply();
        }
    }
    
    // 获取最佳成绩
    public long getBestTime(int gridSize) {
        String key = getBestTimeKey(gridSize);
        return prefs.getLong(key, 0);
    }
    
    // 获取格式化的最佳成绩
    public String getFormattedBestTime(int gridSize) {
        long bestTime = getBestTime(gridSize);
        if (bestTime == 0) {
            return "--";
        }
        return formatTime(bestTime);
    }

    // 获取游戏次数
    public int getPlayCount(int gridSize) {
        String key = getPlayCountKey(gridSize);
        return prefs.getInt(key, 0);
    }

    // 获取平均时间
    public long getAverageTime(int gridSize) {
        int playCount = getPlayCount(gridSize);
        if (playCount == 0) {
            return 0;
        }

        String totalTimeKey = getTotalTimeKey(gridSize);
        long totalTime = prefs.getLong(totalTimeKey, 0);
        return totalTime / playCount;
    }

    // 获取今日游戏次数
    public int getTodayGameCount(int gridSize) {
        String today = getTodayString();
        String savedDate = prefs.getString(KEY_TODAY_DATE, "");

        if (!today.equals(savedDate)) {
            // 新的一天，重置今日计数
            resetTodayCounts();
            return 0;
        }

        String key = getTodayCountKey(gridSize);
        return prefs.getInt(key, 0);
    }

    // 保存游戏结果（包含所有统计数据更新）
    public void saveGameResult(int gridSize, long timeInMillis) {
        // 保存最佳时间
        saveBestTime(gridSize, timeInMillis);

        // 更新游戏次数
        String playCountKey = getPlayCountKey(gridSize);
        int currentCount = prefs.getInt(playCountKey, 0);
        prefs.edit().putInt(playCountKey, currentCount + 1).apply();

        // 更新总时间（用于计算平均时间）
        String totalTimeKey = getTotalTimeKey(gridSize);
        long currentTotalTime = prefs.getLong(totalTimeKey, 0);
        prefs.edit().putLong(totalTimeKey, currentTotalTime + timeInMillis).apply();

        // 更新今日游戏次数
        updateTodayCount(gridSize);

        // 记录游戏完成
        recordGameCompletion();
    }
    
    // 记录一次游戏完成
    public void recordGameCompletion() {
        String today = getTodayString();
        String lastPlayDate = prefs.getString(KEY_LAST_PLAY_DATE, "");
        
        // 更新总次数
        int totalCount = prefs.getInt(KEY_TOTAL_COUNT, 0);
        prefs.edit().putInt(KEY_TOTAL_COUNT, totalCount + 1).apply();
        
        // 更新今日次数
        if (today.equals(lastPlayDate)) {
            // 同一天，增加今日次数
            int todayCount = prefs.getInt(KEY_TODAY_COUNT, 0);
            prefs.edit().putInt(KEY_TODAY_COUNT, todayCount + 1).apply();
        } else {
            // 新的一天
            prefs.edit().putInt(KEY_TODAY_COUNT, 1).apply();
            prefs.edit().putString(KEY_LAST_PLAY_DATE, today).apply();
            
            // 更新连续天数
            updateStreakDays(lastPlayDate, today);
        }
    }
    
    // 获取今日训练次数
    public int getTodayCount() {
        String today = getTodayString();
        String lastPlayDate = prefs.getString(KEY_LAST_PLAY_DATE, "");
        
        if (today.equals(lastPlayDate)) {
            return prefs.getInt(KEY_TODAY_COUNT, 0);
        } else {
            return 0;
        }
    }
    
    // 获取总训练次数
    public int getTotalCount() {
        return prefs.getInt(KEY_TOTAL_COUNT, 0);
    }

    // 获取总游戏次数（所有难度）
    public int getTotalGames() {
        return getPlayCount(3) + getPlayCount(4) + getPlayCount(5) + getPlayCount(6);
    }

    // 获取今日游戏次数（所有难度）
    public int getTodayGames() {
        return getTodayGameCount(3) + getTodayGameCount(4) + getTodayGameCount(5) + getTodayGameCount(6);
    }
    
    // 获取连续天数
    public int getStreakDays() {
        return prefs.getInt(KEY_STREAK_DAYS, 0);
    }
    
    // 更新连续天数
    private void updateStreakDays(String lastPlayDate, String today) {
        if (lastPlayDate.isEmpty()) {
            // 第一次玩游戏
            prefs.edit().putInt(KEY_STREAK_DAYS, 1).apply();
            return;
        }
        
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
            Date lastDate = sdf.parse(lastPlayDate);
            Date todayDate = sdf.parse(today);
            
            if (lastDate != null && todayDate != null) {
                long diffInMillis = todayDate.getTime() - lastDate.getTime();
                long diffInDays = diffInMillis / (24 * 60 * 60 * 1000);
                
                if (diffInDays == 1) {
                    // 连续的下一天
                    int currentStreak = prefs.getInt(KEY_STREAK_DAYS, 0);
                    prefs.edit().putInt(KEY_STREAK_DAYS, currentStreak + 1).apply();
                } else {
                    // 中断了连续记录
                    prefs.edit().putInt(KEY_STREAK_DAYS, 1).apply();
                }
            }
        } catch (Exception e) {
            // 解析日期失败，重置为1
            prefs.edit().putInt(KEY_STREAK_DAYS, 1).apply();
        }
    }
    
    // 序列记忆游戏 - 保存最佳成绩
    public void saveSequenceBestTime(int sequenceLength, long timeInMillis) {
        String key = getSequenceBestTimeKey(sequenceLength);
        long currentBest = prefs.getLong(key, Long.MAX_VALUE);

        if (timeInMillis < currentBest) {
            prefs.edit().putLong(key, timeInMillis).apply();
        }
    }

    // 序列记忆游戏 - 获取最佳成绩
    public long getSequenceBestTime(int sequenceLength) {
        String key = getSequenceBestTimeKey(sequenceLength);
        return prefs.getLong(key, 0);
    }

    // 序列记忆游戏 - 获取格式化的最佳成绩
    public String getFormattedSequenceBestTime(int sequenceLength) {
        long bestTime = getSequenceBestTime(sequenceLength);
        if (bestTime == 0) {
            return "--";
        }
        return formatTime(bestTime);
    }

    // 序列记忆游戏 - 保存游戏结果
    public void saveSequenceGameResult(int sequenceLength, long timeInMillis) {
        // 保存最佳时间
        saveSequenceBestTime(sequenceLength, timeInMillis);

        // 更新游戏次数
        String playCountKey = getSequencePlayCountKey(sequenceLength);
        int currentCount = prefs.getInt(playCountKey, 0);
        prefs.edit().putInt(playCountKey, currentCount + 1).apply();

        // 更新今日游戏次数
        updateTodayPlayCount();

        // 保存最近游戏记录
        saveRecentSequenceGame(sequenceLength, timeInMillis);
    }

    // 序列记忆游戏 - 获取游戏次数
    public int getSequencePlayCount(int sequenceLength) {
        String key = getSequencePlayCountKey(sequenceLength);
        return prefs.getInt(key, 0);
    }

    // 获取最佳成绩的键名
    private String getBestTimeKey(int gridSize) {
        switch (gridSize) {
            case 3:
                return KEY_BEST_TIME_3X3;
            case 4:
                return KEY_BEST_TIME_4X4;
            case 5:
                return KEY_BEST_TIME_5X5;
            case 6:
                return KEY_BEST_TIME_6X6;
            default:
                return KEY_BEST_TIME_3X3;
        }
    }

    // 序列记忆游戏 - 获取最佳成绩的键名
    private String getSequenceBestTimeKey(int sequenceLength) {
        switch (sequenceLength) {
            case 3:
                return KEY_SEQUENCE_BEST_3;
            case 4:
                return KEY_SEQUENCE_BEST_4;
            case 5:
                return KEY_SEQUENCE_BEST_5;
            case 6:
                return KEY_SEQUENCE_BEST_6;
            case 7:
                return KEY_SEQUENCE_BEST_7;
            default:
                return KEY_SEQUENCE_BEST_3;
        }
    }

    // 序列记忆游戏 - 获取游戏次数的键名
    private String getSequencePlayCountKey(int sequenceLength) {
        switch (sequenceLength) {
            case 3:
                return KEY_SEQUENCE_COUNT_3;
            case 4:
                return KEY_SEQUENCE_COUNT_4;
            case 5:
                return KEY_SEQUENCE_COUNT_5;
            case 6:
                return KEY_SEQUENCE_COUNT_6;
            case 7:
                return KEY_SEQUENCE_COUNT_7;
            default:
                return KEY_SEQUENCE_COUNT_3;
        }
    }

    // 获取游戏次数的键名
    private String getPlayCountKey(int gridSize) {
        switch (gridSize) {
            case 3:
                return KEY_PLAY_COUNT_3X3;
            case 4:
                return KEY_PLAY_COUNT_4X4;
            case 5:
                return KEY_PLAY_COUNT_5X5;
            case 6:
                return KEY_PLAY_COUNT_6X6;
            default:
                return KEY_PLAY_COUNT_3X3;
        }
    }

    // 获取总时间的键名
    private String getTotalTimeKey(int gridSize) {
        switch (gridSize) {
            case 3:
                return KEY_TOTAL_TIME_3X3;
            case 4:
                return KEY_TOTAL_TIME_4X4;
            case 5:
                return KEY_TOTAL_TIME_5X5;
            case 6:
                return KEY_TOTAL_TIME_6X6;
            default:
                return KEY_TOTAL_TIME_3X3;
        }
    }

    // 获取今日游戏次数的键名
    private String getTodayCountKey(int gridSize) {
        switch (gridSize) {
            case 3:
                return KEY_TODAY_COUNT_3X3;
            case 4:
                return KEY_TODAY_COUNT_4X4;
            case 5:
                return KEY_TODAY_COUNT_5X5;
            case 6:
                return KEY_TODAY_COUNT_6X6;
            default:
                return KEY_TODAY_COUNT_3X3;
        }
    }

    // 更新今日游戏次数
    private void updateTodayCount(int gridSize) {
        String today = getTodayString();
        String savedDate = prefs.getString(KEY_TODAY_DATE, "");

        if (!today.equals(savedDate)) {
            // 新的一天，重置所有今日计数
            resetTodayCounts();
            prefs.edit().putString(KEY_TODAY_DATE, today).apply();
        }

        String key = getTodayCountKey(gridSize);
        int currentCount = prefs.getInt(key, 0);
        prefs.edit().putInt(key, currentCount + 1).apply();
    }

    // 重置今日计数
    private void resetTodayCounts() {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt(KEY_TODAY_COUNT_3X3, 0);
        editor.putInt(KEY_TODAY_COUNT_4X4, 0);
        editor.putInt(KEY_TODAY_COUNT_5X5, 0);
        editor.putInt(KEY_TODAY_COUNT_6X6, 0);
        editor.apply();
    }
    
    // 获取今天的日期字符串
    private String getTodayString() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        return sdf.format(new Date());
    }
    
    // 格式化时间
    private String formatTime(long timeInMillis) {
        int seconds = (int) (timeInMillis / 1000);
        int minutes = seconds / 60;
        seconds = seconds % 60;
        return String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds);
    }
    
    // 重置所有数据（用于测试或重置功能）
    public void resetAllData() {
        prefs.edit().clear().apply();
    }
    
    // 更新今日游戏次数
    private void updateTodayPlayCount() {
        String today = getTodayString();
        String lastPlayDate = prefs.getString(KEY_LAST_PLAY_DATE, "");

        if (!today.equals(lastPlayDate)) {
            // 新的一天，重置今日次数
            prefs.edit()
                .putString(KEY_LAST_PLAY_DATE, today)
                .putInt(KEY_TODAY_COUNT, 1)
                .apply();
        } else {
            // 同一天，增加次数
            int todayCount = prefs.getInt(KEY_TODAY_COUNT, 0);
            prefs.edit().putInt(KEY_TODAY_COUNT, todayCount + 1).apply();
        }
    }

    // 保存最近序列游戏记录（简化版本）
    private void saveRecentSequenceGame(int sequenceLength, long timeInMillis) {
        // 这里可以实现保存最近游戏记录的逻辑
        // 暂时简化处理，只更新统计数据
    }

    // 获取序列记忆游戏统计信息
    public SequenceGameStats getSequenceGameStats() {
        return new SequenceGameStats(
            getTodaySequenceCount(),
            getTotalSequenceCount(),
            getFormattedSequenceBestTime(3),
            getFormattedSequenceBestTime(4),
            getFormattedSequenceBestTime(5),
            getFormattedSequenceBestTime(6),
            getFormattedSequenceBestTime(7)
        );
    }

    // 获取序列记忆游戏今日次数
    private int getTodaySequenceCount() {
        String today = getTodayString();
        String lastPlayDate = prefs.getString(KEY_LAST_PLAY_DATE, "");

        if (!today.equals(lastPlayDate)) {
            return 0;
        }

        return getSequencePlayCount(3) + getSequencePlayCount(4) +
               getSequencePlayCount(5) + getSequencePlayCount(6) +
               getSequencePlayCount(7);
    }

    // 获取序列记忆游戏总次数
    private int getTotalSequenceCount() {
        return getSequencePlayCount(3) + getSequencePlayCount(4) +
               getSequencePlayCount(5) + getSequencePlayCount(6) +
               getSequencePlayCount(7);
    }

    // 获取游戏统计信息
    public GameStats getGameStats() {
        return new GameStats(
            getTodayCount(),
            getTotalCount(),
            getStreakDays(),
            getFormattedBestTime(3),
            getFormattedBestTime(4),
            getFormattedBestTime(5)
        );
    }
    
    // 游戏统计数据类
    public static class GameStats {
        public final int todayCount;
        public final int totalCount;
        public final int streakDays;
        public final String bestTime3x3;
        public final String bestTime4x4;
        public final String bestTime5x5;

        public GameStats(int todayCount, int totalCount, int streakDays,
                        String bestTime3x3, String bestTime4x4, String bestTime5x5) {
            this.todayCount = todayCount;
            this.totalCount = totalCount;
            this.streakDays = streakDays;
            this.bestTime3x3 = bestTime3x3;
            this.bestTime4x4 = bestTime4x4;
            this.bestTime5x5 = bestTime5x5;
        }
    }

    // 序列记忆游戏统计数据类
    public static class SequenceGameStats {
        public final int todayCount;
        public final int totalCount;
        public final String bestTime3;
        public final String bestTime4;
        public final String bestTime5;
        public final String bestTime6;
        public final String bestTime7;

        public SequenceGameStats(int todayCount, int totalCount,
                               String bestTime3, String bestTime4, String bestTime5,
                               String bestTime6, String bestTime7) {
            this.todayCount = todayCount;
            this.totalCount = totalCount;
            this.bestTime3 = bestTime3;
            this.bestTime4 = bestTime4;
            this.bestTime5 = bestTime5;
            this.bestTime6 = bestTime6;
            this.bestTime7 = bestTime7;
        }
    }

    // 记忆游戏统计方法
    public void saveMemoryGameResult(String gameKey, long time, boolean success) {
        SharedPreferences.Editor editor = prefs.edit();

        // 保存最新时间
        editor.putLong("memory_" + gameKey + "_latest_time", time);
        editor.putBoolean("memory_" + gameKey + "_latest_success", success);

        if (success) {
            // 更新最佳时间
            long bestTime = prefs.getLong("memory_" + gameKey + "_best_time", Long.MAX_VALUE);
            if (time < bestTime) {
                editor.putLong("memory_" + gameKey + "_best_time", time);
            }

            // 计算平均时间
            long totalTime = prefs.getLong("memory_" + gameKey + "_total_time", 0);
            int successCount = prefs.getInt("memory_" + gameKey + "_success_count", 0);
            editor.putLong("memory_" + gameKey + "_total_time", totalTime + time);
            editor.putInt("memory_" + gameKey + "_success_count", successCount + 1);
        }

        // 增加游戏次数
        int totalGames = prefs.getInt("memory_" + gameKey + "_total_games", 0);
        editor.putInt("memory_" + gameKey + "_total_games", totalGames + 1);

        // 增加今日游戏次数
        String today = getTodayString();
        String todayKey = "memory_" + gameKey + "_today_" + today;
        int todayGames = prefs.getInt(todayKey, 0);
        editor.putInt(todayKey, todayGames + 1);

        editor.apply();
    }

    public int getMemoryGameCount(String gameKey) {
        return prefs.getInt("memory_" + gameKey + "_total_games", 0);
    }

    public long getMemoryGameBestTime(String gameKey) {
        long bestTime = prefs.getLong("memory_" + gameKey + "_best_time", Long.MAX_VALUE);
        return bestTime == Long.MAX_VALUE ? 0 : bestTime;
    }

    public long getMemoryGameAverageTime(String gameKey) {
        long totalTime = prefs.getLong("memory_" + gameKey + "_total_time", 0);
        int successCount = prefs.getInt("memory_" + gameKey + "_success_count", 0);
        return successCount > 0 ? totalTime / successCount : 0;
    }

    public int getMemoryGameTodayCount(String gameKey) {
        String today = getTodayString();
        String todayKey = "memory_" + gameKey + "_today_" + today;
        return prefs.getInt(todayKey, 0);
    }

    // 乘法表游戏统计方法
    public void saveMultiplicationGameResult(int level, int score, long gameTime) {
        SharedPreferences.Editor editor = prefs.edit();

        // 更新最佳成绩
        int bestScore = prefs.getInt(KEY_MULTIPLICATION_BEST_SCORE, 0);
        if (score > bestScore) {
            editor.putInt(KEY_MULTIPLICATION_BEST_SCORE, score);
        }

        // 更新最高关卡
        int bestLevel = prefs.getInt(KEY_MULTIPLICATION_BEST_LEVEL, 0);
        if (level > bestLevel) {
            editor.putInt(KEY_MULTIPLICATION_BEST_LEVEL, level);
        }

        // 更新游戏次数
        int totalGames = prefs.getInt(KEY_MULTIPLICATION_TOTAL_GAMES, 0);
        editor.putInt(KEY_MULTIPLICATION_TOTAL_GAMES, totalGames + 1);

        // 更新今日游戏次数
        String today = getTodayString();
        String todayKey = KEY_MULTIPLICATION_TODAY_GAMES + "_" + today;
        int todayGames = prefs.getInt(todayKey, 0);
        editor.putInt(todayKey, todayGames + 1);

        editor.apply();
    }

    public int getMultiplicationBestScore() {
        return prefs.getInt(KEY_MULTIPLICATION_BEST_SCORE, 0);
    }

    public int getMultiplicationBestLevel() {
        return prefs.getInt(KEY_MULTIPLICATION_BEST_LEVEL, 0);
    }

    public int getMultiplicationTotalGames() {
        return prefs.getInt(KEY_MULTIPLICATION_TOTAL_GAMES, 0);
    }

    public int getMultiplicationTodayGames() {
        String today = getTodayString();
        String todayKey = KEY_MULTIPLICATION_TODAY_GAMES + "_" + today;
        return prefs.getInt(todayKey, 0);
    }
}
