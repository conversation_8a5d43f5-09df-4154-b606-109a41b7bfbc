package com.example.demo.ui;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v4.app.FragmentActivity;

import com.example.demo.R;

public class VideoFragmentActivity extends FragmentActivity {

    //com.kwad.sdk.api.KsContentPage mContentPage;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_video);

        //显示短视频
        /*com.by.mob.config.ByVideoConfig config = new com.by.mob.config.ByVideoConfig.Builder()
                .codeId("1400375964994650194")//平台申请的代码位id
                .containerIdRes(R.id.fragment_container)
                .reward_switch(true)
                .build();
        com.by.mob.ByVideoManager.replaceFrameLayout4Video(this, config, new com.by.mob.ByVideoManager.IReplaceListener() {
            @Override
            public void onFail(String s) {

            }

            @Override
            public void getContentPage(com.kwad.sdk.api.KsContentPage contentPage, int containerIdRes) {
                VideoFragmentActivity.this.mContentPage = contentPage;
                getSupportFragmentManager().beginTransaction().replace(containerIdRes, contentPage.getFragment()).commitAllowingStateLoss();
            }

            @Override
            public void onVideoPlayStart(com.ads.admob.bean.VideoTypeEnum videoTypeEnum) {

            }

            @Override
            public void onVideoPlayPaused(com.ads.admob.bean.VideoTypeEnum videoTypeEnum) {

            }

            @Override
            public void onVideoPlayResume(com.ads.admob.bean.VideoTypeEnum videoTypeEnum) {

            }

            @Override
            public void onVideoPlayCompleted(com.ads.admob.bean.VideoTypeEnum videoTypeEnum) {

            }

            @Override
            public void onPageLeave(com.ads.admob.bean.VideoTypeEnum videoTypeEnum) {

            }

            @Override
            public void onRewardVerify() {
                android.widget.Toast.makeText(VideoFragmentActivity.this, "奖励已发放", android.widget.Toast.LENGTH_SHORT).show();
            }
        });*/
    }

    @Override
    public void onBackPressed() {
        /*com.by.mob.ByVideoManager.cleanVideo();
        if (mContentPage != null && mContentPage.onBackPressed()) {
            return;
        }*/
        super.onBackPressed();
    }
}
