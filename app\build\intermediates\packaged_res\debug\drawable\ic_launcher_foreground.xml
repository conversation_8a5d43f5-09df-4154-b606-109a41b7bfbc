<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- 背景圆形 -->
    <path
        android:fillColor="@color/primary_color"
        android:pathData="M54,54m-50,0a50,50 0,1 1,100 0a50,50 0,1 1,-100 0" />
    
    <!-- 方格图案 -->
    <group android:translateX="54" android:translateY="54">
        <!-- 3x3 方格 -->
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M-15,-15 L-5,-15 L-5,-5 L-15,-5 Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M-5,-15 L5,-15 L5,-5 L-5,-5 Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M5,-15 L15,-15 L15,-5 L5,-5 Z" />
        
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M-15,-5 L-5,-5 L-5,5 L-15,5 Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M-5,-5 L5,-5 L5,5 L-5,5 Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M5,-5 L15,-5 L15,5 L5,5 Z" />
        
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M-15,5 L-5,5 L-5,15 L-15,15 Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M-5,5 L5,5 L5,15 L-5,15 Z" />
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M5,5 L15,5 L15,15 L5,15 Z" />
        
        <!-- 数字1 -->
        <path
            android:fillColor="@color/primary_color"
            android:pathData="M-12,-12 L-8,-12 L-8,-8 L-12,-8 Z" />
        
        <!-- 数字2 -->
        <path
            android:fillColor="@color/primary_color"
            android:pathData="M-2,-12 L2,-12 L2,-8 L-2,-8 Z" />
    </group>
</vector>
