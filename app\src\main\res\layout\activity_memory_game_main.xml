<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/light_gradient_background"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="24dp">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="🧠 数字序列记忆"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onBackground"
                android:gravity="center"
                android:layout_marginHorizontal="16dp" />

            <!-- 教程按钮 - 灯泡图标 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_tutorial"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:text="💡"
                android:textSize="20sp"
                style="@style/Widget.Material3.Button.IconButton"
                android:background="@android:color/transparent" />

        </LinearLayout>

        <!-- 游戏设置卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🎮 游戏设置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onBackground"
                    android:layout_marginBottom="16dp" />

                <!-- 模式选择 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="游戏模式："
                        android:textSize="16sp"
                        android:textColor="@color/md_theme_light_onBackground" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/tv_current_mode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="数字内容"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/white"
                        app:backgroundTint="@color/md_theme_light_primary"
                        app:cornerRadius="20dp"
                        android:paddingLeft="16dp"
                        android:paddingRight="16dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        style="@style/Widget.Material3.Button" />

                </LinearLayout>

                <!-- 难度选择 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="游戏难度："
                        android:textSize="16sp"
                        android:textColor="@color/md_theme_light_onBackground" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/tv_current_difficulty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="7个数字"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/white"
                        app:backgroundTint="@color/md_theme_light_secondary"
                        app:cornerRadius="20dp"
                        android:paddingLeft="16dp"
                        android:paddingRight="16dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        style="@style/Widget.Material3.Button" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 水晶积分显示 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🔮"
                    android:textSize="24sp"
                    android:layout_marginEnd="12dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="记忆水晶"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_onBackground"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/tv_crystal_progress"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0个 + 0/30碎片"
                        android:textSize="12sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant" />

                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="完成游戏获得碎片"
                    android:textSize="10sp"
                    android:textColor="@color/md_theme_light_onSurfaceVariant"
                    android:gravity="end" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 统计信息卡片 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_stats"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📊 游戏统计"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onBackground"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="最佳成绩："
                        android:textSize="16sp"
                        android:textColor="@color/md_theme_light_onBackground" />

                    <TextView
                        android:id="@+id/tv_best_score"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="--"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_primary" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="总游戏次数："
                        android:textSize="16sp"
                        android:textColor="@color/md_theme_light_onBackground" />

                    <TextView
                        android:id="@+id/tv_total_games"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_primary" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 最近训练记录卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📊 最近训练记录"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onBackground"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:id="@+id/layout_recent_records"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 按钮区域 -->
        <!-- 开始按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_start"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginTop="16dp"
            android:text="🚀 开始训练"
            android:textSize="18sp"
            android:textStyle="bold"
            app:cornerRadius="28dp"
            app:backgroundTint="@color/md_theme_light_primary"
            android:textColor="@android:color/white" />

    </LinearLayout>

</ScrollView>
