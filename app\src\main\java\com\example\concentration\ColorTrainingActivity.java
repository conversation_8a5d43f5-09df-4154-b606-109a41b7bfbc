package com.example.concentration;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.google.android.material.button.MaterialButton;
import java.util.List;
import android.view.View;
import android.widget.LinearLayout;

public class ColorTrainingActivity extends AppCompatActivity {
    
    private TextView tvBestScore, tvAvgTime, tvTotalGames, tvCrystalProgress;
    private MaterialButton btnEasy, btnMedium, btnHard;
    private MaterialButton btnColorMatch, btnColorName, btnInterference;
    private MaterialButton btnStart;
    private Button btnTutorial;
    private ImageButton btnBack;
    private LinearLayout layoutRecentRecords;
    
    private String selectedDifficulty = "easy";
    private String selectedGameMode = "color_match";
    
    private SharedPreferences prefs;
    private GameDataManager gameDataManager;
    private TutorialManager tutorialManager;
    private CrystalManager crystalManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_color_training);
        
        initializeManagers();
        initViews();
        setupClickListeners();
        loadStatistics();

        // 检查是否需要显示教程
        checkAndShowTutorial();
    }
    
    private void initializeManagers() {
        prefs = getSharedPreferences("color_training_prefs", MODE_PRIVATE);
        gameDataManager = new GameDataManager(this);
        tutorialManager = TutorialManager.getInstance(this);
        crystalManager = CrystalManager.getInstance(this);
    }
    
    private void initViews() {
        // 统计显示
        tvBestScore = findViewById(R.id.tv_best_score);
        tvAvgTime = findViewById(R.id.tv_avg_time);
        tvTotalGames = findViewById(R.id.tv_total_games);
        tvCrystalProgress = findViewById(R.id.tv_crystal_progress);
        
        // 难度按钮
        btnEasy = findViewById(R.id.btn_easy);
        btnMedium = findViewById(R.id.btn_medium);
        btnHard = findViewById(R.id.btn_hard);

        // 游戏模式按钮
        btnColorMatch = findViewById(R.id.btn_color_match);
        btnColorName = findViewById(R.id.btn_color_name);
        btnInterference = findViewById(R.id.btn_interference);
        
        // 控制按钮
        btnStart = findViewById(R.id.btn_start);
        btnTutorial = findViewById(R.id.btn_tutorial);
        btnBack = findViewById(R.id.btn_back);

        // 最近训练记录
        layoutRecentRecords = findViewById(R.id.layout_recent_records);
        setupRecentRecords();

        // 默认选择简单难度和颜色匹配模式
        updateDifficultySelection("easy");
        updateGameModeSelection("color_match");
    }
    
    private void setupClickListeners() {
        // 返回按钮
        btnBack.setOnClickListener(v -> finish());
        
        // 难度选择
        btnEasy.setOnClickListener(v -> updateDifficultySelection("easy"));
        btnMedium.setOnClickListener(v -> updateDifficultySelection("medium"));
        btnHard.setOnClickListener(v -> updateDifficultySelection("hard"));
        
        // 游戏模式选择
        btnColorMatch.setOnClickListener(v -> updateGameModeSelection("color_match"));
        btnColorName.setOnClickListener(v -> updateGameModeSelection("color_name"));
        btnInterference.setOnClickListener(v -> updateGameModeSelection("interference"));
        
        // 开始游戏
        btnStart.setOnClickListener(v -> startColorGame());

        // 教程按钮
        btnTutorial.setOnClickListener(v -> showTutorial());
    }
    
    private void updateDifficultySelection(String difficulty) {
        selectedDifficulty = difficulty;
        
        // 重置所有按钮样式
        resetDifficultyButtons();
        
        // 高亮选中的按钮
        MaterialButton selectedButton = null;
        switch (difficulty) {
            case "easy":
                selectedButton = btnEasy;
                break;
            case "medium":
                selectedButton = btnMedium;
                break;
            case "hard":
                selectedButton = btnHard;
                break;
        }
        
        if (selectedButton != null) {
            selectedButton.setBackgroundTintList(getColorStateList(R.color.md_theme_light_primary));
            selectedButton.setTextColor(getColor(android.R.color.white));
        }
    }
    
    private void resetDifficultyButtons() {
        MaterialButton[] buttons = {btnEasy, btnMedium, btnHard};
        for (MaterialButton button : buttons) {
            button.setBackgroundTintList(getColorStateList(android.R.color.transparent));
            button.setTextColor(getColor(R.color.md_theme_light_primary));
        }
    }

    private void updateGameModeSelection(String gameMode) {
        selectedGameMode = gameMode;

        // 重置所有游戏模式按钮样式
        resetGameModeButtons();

        // 高亮选中的按钮
        MaterialButton selectedButton = null;
        switch (gameMode) {
            case "color_match":
                selectedButton = btnColorMatch;
                break;
            case "color_name":
                selectedButton = btnColorName;
                break;
            case "interference":
                selectedButton = btnInterference;
                break;
        }

        if (selectedButton != null) {
            selectedButton.setBackgroundTintList(getColorStateList(R.color.md_theme_light_primary));
            selectedButton.setTextColor(getColor(android.R.color.white));
        }
    }

    private void resetGameModeButtons() {
        // 重置为outline样式
        btnColorMatch.setBackgroundTintList(getColorStateList(android.R.color.transparent));
        btnColorMatch.setTextColor(getColor(R.color.md_theme_light_primary));

        btnColorName.setBackgroundTintList(getColorStateList(android.R.color.transparent));
        btnColorName.setTextColor(getColor(R.color.md_theme_light_primary));

        btnInterference.setBackgroundTintList(getColorStateList(android.R.color.transparent));
        btnInterference.setTextColor(getColor(R.color.md_theme_light_primary));
    }

    private void loadStatistics() {
        // 加载颜色训练统计数据
        float bestAccuracy = prefs.getFloat("best_accuracy", 0f);
        float avgReactionTime = prefs.getFloat("avg_reaction_time", 0f);
        int totalGames = prefs.getInt("total_games", 0);
        
        // 显示统计数据
        tvBestScore.setText(String.format("%.0f%%", bestAccuracy * 100));
        tvAvgTime.setText(String.format("%.1fs", avgReactionTime));
        tvTotalGames.setText(String.valueOf(totalGames));

        // 更新水晶进度
        String crystalProgress = crystalManager.getCrystalProgressText(CrystalManager.CrystalType.COLOR_TRAINING);
        tvCrystalProgress.setText(crystalProgress);
    }
    
    private void startColorGame() {
        Intent intent = new Intent(this, CountdownActivity.class);
        
        // 传递游戏参数
        intent.putExtra("game_type", "color_training");
        intent.putExtra("difficulty", selectedDifficulty);
        intent.putExtra("game_mode", selectedGameMode);
        
        // 根据难度设置网格大小
        int gridSize = getGridSizeForDifficulty(selectedDifficulty);
        intent.putExtra("grid_size", gridSize);
        
        // 设置游戏轮数
        intent.putExtra("total_rounds", 10);
        
        startActivity(intent);
    }
    
    private int getGridSizeForDifficulty(String difficulty) {
        switch (difficulty) {
            case "easy":
                return 3; // 3x3
            case "medium":
                return 4; // 4x4
            case "hard":
                return 5; // 5x5
            default:
                return 3;
        }
    }
    
    private void setupRecentRecords() {
        // 清空现有记录
        layoutRecentRecords.removeAllViews();

        // 添加记录行（参考舒尔特方格的样式）
        loadColorTrainingRecords(layoutRecentRecords);
    }

    private void loadColorTrainingRecords(LinearLayout container) {
        // 获取颜色训练的统计数据
        SharedPreferences colorPrefs = getSharedPreferences("color_training_prefs", MODE_PRIVATE);
        int totalGames = colorPrefs.getInt("total_games", 0);

        if (totalGames == 0) {
            // 没有记录时显示占位文本
            TextView placeholder = new TextView(this);
            placeholder.setText("暂无训练记录");
            placeholder.setTextSize(14);
            placeholder.setTextColor(getColor(R.color.md_theme_light_onSurfaceVariant));
            placeholder.setPadding(0, 16, 0, 16);
            placeholder.setGravity(android.view.Gravity.CENTER);
            container.addView(placeholder);
        } else {
            // 显示统计信息作为记录
            float bestAccuracy = colorPrefs.getFloat("best_accuracy", 0f);
            float avgReactionTime = colorPrefs.getFloat("avg_reaction_time", 0f);

            if (bestAccuracy > 0) {
                addColorRecordRow(container, "最佳准确率", String.format("%.1f%%", bestAccuracy * 100), "--", "完成");
            }
            if (avgReactionTime > 0) {
                addColorRecordRow(container, "平均反应", String.format("%.2f秒", avgReactionTime), "--", "统计");
            }
            addColorRecordRow(container, "总训练次数", String.valueOf(totalGames) + "次", "--", "进行中");

            // 如果记录太少，添加鼓励信息
            if (totalGames < 3) {
                addColorRecordRow(container, "继续训练", "解锁更多", "记录", "加油!");
            }
        }
    }

    private void addColorRecordRow(LinearLayout container, String date, String info, String time, String status) {
        LinearLayout row = new LinearLayout(this);
        row.setOrientation(LinearLayout.HORIZONTAL);
        row.setPadding(0, 8, 0, 8);

        // 日期/类型
        TextView tvDate = new TextView(this);
        tvDate.setText(date);
        tvDate.setTextSize(12);
        tvDate.setTextColor(getColor(R.color.md_theme_light_onSurface));
        tvDate.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));

        // 信息
        TextView tvInfo = new TextView(this);
        tvInfo.setText(info);
        tvInfo.setTextSize(12);
        tvInfo.setTextColor(getColor(R.color.md_theme_light_onSurface));
        tvInfo.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));
        tvInfo.setGravity(android.view.Gravity.CENTER);

        // 用时
        TextView tvTime = new TextView(this);
        tvTime.setText(time);
        tvTime.setTextSize(12);
        tvTime.setTextColor(getColor(R.color.md_theme_light_primary));
        tvTime.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));
        tvTime.setGravity(android.view.Gravity.CENTER);

        // 状态
        TextView tvStatus = new TextView(this);
        tvStatus.setText(status);
        tvStatus.setTextSize(12);
        tvStatus.setTextColor(getColor(R.color.md_theme_light_onSurfaceVariant));
        tvStatus.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));
        tvStatus.setGravity(android.view.Gravity.CENTER);

        row.addView(tvDate);
        row.addView(tvInfo);
        row.addView(tvTime);
        row.addView(tvStatus);

        container.addView(row);
    }

    /**
     * 检查并显示教程
     */
    private void checkAndShowTutorial() {
        if (tutorialManager.isFirstTime(TutorialManager.GameType.COLOR_TRAINING)) {
            showTutorial();
        }
    }

    /**
     * 显示教程
     */
    private void showTutorial() {
        Intent intent = new Intent(this, TutorialActivity.class);
        intent.putExtra("game_type", TutorialManager.GameType.COLOR_TRAINING.getKey());
        startActivity(intent);
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 重新加载统计数据，以防游戏后有更新
        loadStatistics();
        setupRecentRecords(); // 重新加载最近记录
    }
}
