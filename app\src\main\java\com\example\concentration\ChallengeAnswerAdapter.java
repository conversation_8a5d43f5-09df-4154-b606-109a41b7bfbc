package com.example.concentration;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.button.MaterialButton;
import java.util.List;

public class ChallengeAnswerAdapter extends RecyclerView.Adapter<ChallengeAnswerAdapter.ViewHolder> {
    
    private List<Integer> answers;
    private OnAnswerClickListener listener;
    private boolean answered = false;
    private int correctAnswer = -1;
    
    public interface OnAnswerClickListener {
        void onAnswerClick(int answer);
    }
    
    public ChallengeAnswerAdapter(List<Integer> answers, OnAnswerClickListener listener) {
        this.answers = answers;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_challenge_answer, parent, false);
        return new ViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        int answer = answers.get(position);
        holder.btnAnswer.setText(String.valueOf(answer));
        
        // 设置点击事件
        holder.btnAnswer.setOnClickListener(v -> {
            if (!answered && listener != null) {
                answered = true;
                listener.onAnswerClick(answer);
            }
        });
        
        // 根据答题状态设置按钮样式
        if (answered) {
            if (answer == correctAnswer) {
                holder.btnAnswer.setBackgroundColor(0xFF4CAF50); // 绿色 - 正确答案
            } else {
                holder.btnAnswer.setBackgroundColor(0xFFF44336); // 红色 - 错误答案
            }
            holder.btnAnswer.setEnabled(false);
        } else {
            holder.btnAnswer.setBackgroundColor(0xFF6200EE); // 紫色 - 默认状态
            holder.btnAnswer.setEnabled(true);
        }
    }
    
    @Override
    public int getItemCount() {
        return answers.size();
    }
    
    public void showResult(int correctAnswer) {
        this.correctAnswer = correctAnswer;
        this.answered = true;
        notifyDataSetChanged();
    }
    
    public void reset() {
        this.answered = false;
        this.correctAnswer = -1;
        notifyDataSetChanged();
    }
    
    static class ViewHolder extends RecyclerView.ViewHolder {
        MaterialButton btnAnswer;
        
        ViewHolder(View itemView) {
            super(itemView);
            btnAnswer = itemView.findViewById(R.id.btn_answer);
        }
    }
}
