<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_step"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/md_theme_light_surface">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- 步骤图标 -->
        <TextView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:text="📝"
            android:textSize="20sp"
            android:gravity="center"
            android:layout_marginEnd="16dp" />

        <!-- 步骤文本 -->
        <TextView
            android:id="@+id/tv_step"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="步骤内容"
            android:textSize="16sp"
            android:textColor="@color/md_theme_light_onSurface"
            android:lineSpacingExtra="2dp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
