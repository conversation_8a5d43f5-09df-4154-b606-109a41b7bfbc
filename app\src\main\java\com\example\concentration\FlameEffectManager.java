package com.example.concentration;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import java.util.Random;

public class FlameEffectManager {
    private Context context;
    private Random random;
    
    public FlameEffectManager(Context context) {
        this.context = context;
        this.random = new Random();
    }
    
    public void playGoldenFlameEffect(View targetView) {
        // 1. 创建金色火焰边框
        createGoldenBorder(targetView);
        
        // 2. 播放发光动画
        playGlowAnimation(targetView);
        
        // 3. 创建火焰粒子效果
        createFlameParticles(targetView);
        
        // 4. 播放缩放脉冲动画
        playPulseAnimation(targetView);
    }
    
    private void createGoldenBorder(View view) {
        GradientDrawable flameDrawable = new GradientDrawable();
        flameDrawable.setShape(GradientDrawable.RECTANGLE);
        flameDrawable.setCornerRadius(16 * context.getResources().getDisplayMetrics().density);
        
        // 创建金色渐变边框
        flameDrawable.setStroke(8, Color.parseColor("#FFD700"));
        
        // 金色渐变背景
        int[] colors = {
            Color.parseColor("#FFF8DC"), // 浅金色
            Color.parseColor("#FFEB3B"), // 金黄色
            Color.parseColor("#FFC107")  // 深金色
        };
        flameDrawable.setColors(colors);
        flameDrawable.setGradientType(GradientDrawable.RADIAL_GRADIENT);
        flameDrawable.setGradientRadius(100);
        
        view.setBackground(flameDrawable);
    }
    
    private void playGlowAnimation(View view) {
        // 创建发光效果
        ObjectAnimator glowAnimation = ObjectAnimator.ofFloat(view, "alpha", 1.0f, 0.6f, 1.0f);
        glowAnimation.setDuration(400);
        glowAnimation.setRepeatCount(3);
        
        // 添加阴影效果
        ObjectAnimator elevationAnim = ObjectAnimator.ofFloat(view, "elevation", 
            4 * context.getResources().getDisplayMetrics().density,
            12 * context.getResources().getDisplayMetrics().density,
            4 * context.getResources().getDisplayMetrics().density);
        elevationAnim.setDuration(400);
        elevationAnim.setRepeatCount(3);
        
        AnimatorSet glowSet = new AnimatorSet();
        glowSet.playTogether(glowAnimation, elevationAnim);
        glowSet.start();
    }
    
    private void createFlameParticles(View targetView) {
        if (!(targetView.getParent() instanceof ViewGroup)) return;
        
        ViewGroup parent = (ViewGroup) targetView.getParent();
        
        // 创建多个火焰粒子
        for (int i = 0; i < 8; i++) {
            createSingleFlameParticle(parent, targetView, i);
        }
    }
    
    private void createSingleFlameParticle(ViewGroup parent, View targetView, int index) {
        ImageView particle = new ImageView(context);
        
        // 创建火焰粒子drawable
        GradientDrawable particleDrawable = new GradientDrawable();
        particleDrawable.setShape(GradientDrawable.OVAL);
        
        // 随机火焰颜色
        int[] flameColors = {
            Color.parseColor("#FFD700"), // 金色
            Color.parseColor("#FFA500"), // 橙色
            Color.parseColor("#FF6347"), // 红橙色
            Color.parseColor("#FFFF00")  // 黄色
        };
        particleDrawable.setColor(flameColors[random.nextInt(flameColors.length)]);
        
        particle.setBackground(particleDrawable);
        
        // 设置粒子大小
        int particleSize = (int) (8 + random.nextFloat() * 16); // 8-24dp
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(particleSize, particleSize);
        
        // 获取目标视图位置
        int[] targetLocation = new int[2];
        targetView.getLocationInWindow(targetLocation);
        
        // 设置粒子初始位置（目标视图中心）
        particle.setX(targetLocation[0] + targetView.getWidth() / 2f - particleSize / 2f);
        particle.setY(targetLocation[1] + targetView.getHeight() / 2f - particleSize / 2f);
        
        parent.addView(particle, params);
        
        // 创建粒子动画
        animateFlameParticle(particle, targetView, index);
    }
    
    private void animateFlameParticle(ImageView particle, View targetView, int index) {
        // 计算随机方向
        float angle = (360f / 8f) * index + random.nextFloat() * 45f - 22.5f; // 每个粒子有不同角度
        float distance = 80 + random.nextFloat() * 40; // 80-120dp距离
        
        float endX = particle.getX() + (float) Math.cos(Math.toRadians(angle)) * distance;
        float endY = particle.getY() + (float) Math.sin(Math.toRadians(angle)) * distance;
        
        // 位置动画
        ObjectAnimator moveX = ObjectAnimator.ofFloat(particle, "x", particle.getX(), endX);
        ObjectAnimator moveY = ObjectAnimator.ofFloat(particle, "y", particle.getY(), endY);
        
        // 透明度动画（淡出）
        ObjectAnimator fadeOut = ObjectAnimator.ofFloat(particle, "alpha", 1.0f, 0.0f);
        
        // 缩放动画
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(particle, "scaleX", 1.0f, 0.2f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(particle, "scaleY", 1.0f, 0.2f);
        
        // 旋转动画
        ObjectAnimator rotation = ObjectAnimator.ofFloat(particle, "rotation", 0f, 360f * (random.nextFloat() * 2 - 1));
        
        AnimatorSet particleSet = new AnimatorSet();
        particleSet.playTogether(moveX, moveY, fadeOut, scaleX, scaleY, rotation);
        particleSet.setDuration(800 + random.nextInt(400)); // 800-1200ms
        
        particleSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {}
            
            @Override
            public void onAnimationEnd(Animator animation) {
                // 移除粒子
                if (particle.getParent() instanceof ViewGroup) {
                    ((ViewGroup) particle.getParent()).removeView(particle);
                }
            }
            
            @Override
            public void onAnimationCancel(Animator animation) {
                if (particle.getParent() instanceof ViewGroup) {
                    ((ViewGroup) particle.getParent()).removeView(particle);
                }
            }
            
            @Override
            public void onAnimationRepeat(Animator animation) {}
        });
        
        particleSet.start();
    }
    
    private void playPulseAnimation(View view) {
        // 创建脉冲效果
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(view, "scaleX", 1.0f, 1.15f, 1.0f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(view, "scaleY", 1.0f, 1.15f, 1.0f);
        
        AnimatorSet pulseSet = new AnimatorSet();
        pulseSet.playTogether(scaleX, scaleY);
        pulseSet.setDuration(300);
        pulseSet.setStartDelay(100); // 稍微延迟开始
        pulseSet.start();
    }
    
    public void playSuccessFlameRing(View targetView) {
        if (!(targetView.getParent() instanceof ViewGroup)) return;
        
        ViewGroup parent = (ViewGroup) targetView.getParent();
        
        // 创建环形火焰效果
        for (int i = 0; i < 12; i++) {
            createRingFlameParticle(parent, targetView, i, 12);
        }
    }
    
    private void createRingFlameParticle(ViewGroup parent, View targetView, int index, int total) {
        ImageView particle = new ImageView(context);
        
        // 创建火焰粒子
        GradientDrawable particleDrawable = new GradientDrawable();
        particleDrawable.setShape(GradientDrawable.OVAL);
        particleDrawable.setColor(Color.parseColor("#FFD700"));
        particle.setBackground(particleDrawable);
        
        int particleSize = (int) (12 * context.getResources().getDisplayMetrics().density);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(particleSize, particleSize);
        
        // 计算环形位置
        float angle = (360f / total) * index;
        float radius = targetView.getWidth() / 2f + 20;
        
        int[] targetLocation = new int[2];
        targetView.getLocationInWindow(targetLocation);
        
        float centerX = targetLocation[0] + targetView.getWidth() / 2f;
        float centerY = targetLocation[1] + targetView.getHeight() / 2f;
        
        float startX = centerX + (float) Math.cos(Math.toRadians(angle)) * radius;
        float startY = centerY + (float) Math.sin(Math.toRadians(angle)) * radius;
        
        particle.setX(startX - particleSize / 2f);
        particle.setY(startY - particleSize / 2f);
        
        parent.addView(particle, params);
        
        // 环形扩散动画
        float endRadius = radius + 60;
        float endX = centerX + (float) Math.cos(Math.toRadians(angle)) * endRadius;
        float endY = centerY + (float) Math.sin(Math.toRadians(angle)) * endRadius;
        
        ObjectAnimator moveX = ObjectAnimator.ofFloat(particle, "x", startX - particleSize / 2f, endX - particleSize / 2f);
        ObjectAnimator moveY = ObjectAnimator.ofFloat(particle, "y", startY - particleSize / 2f, endY - particleSize / 2f);
        ObjectAnimator fadeOut = ObjectAnimator.ofFloat(particle, "alpha", 1.0f, 0.0f);
        ObjectAnimator scale = ObjectAnimator.ofFloat(particle, "scaleX", 1.0f, 0.3f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(particle, "scaleY", 1.0f, 0.3f);
        
        AnimatorSet ringSet = new AnimatorSet();
        ringSet.playTogether(moveX, moveY, fadeOut, scale, scaleY);
        ringSet.setDuration(600);
        ringSet.setStartDelay(index * 50); // 错开时间创造波浪效果
        
        ringSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {}
            
            @Override
            public void onAnimationEnd(Animator animation) {
                if (particle.getParent() instanceof ViewGroup) {
                    ((ViewGroup) particle.getParent()).removeView(particle);
                }
            }
            
            @Override
            public void onAnimationCancel(Animator animation) {
                if (particle.getParent() instanceof ViewGroup) {
                    ((ViewGroup) particle.getParent()).removeView(particle);
                }
            }
            
            @Override
            public void onAnimationRepeat(Animator animation) {}
        });
        
        ringSet.start();
    }
}
