# 颜色识别训练设计规划（完善版）

## 🎯 游戏概述
颜色识别训练是专注力训练应用的第三个核心功能，采用魔法水晶收集的故事背景，通过颜色识别和匹配训练来提升用户的视觉注意力、反应速度和色彩敏感度。

## 🎮 游戏模式

### 1. 基础模式 - 颜色形状识别
**核心玩法**：
- 屏幕中央随机出现一个形状（圆形、方形、三角形、星形、心形等）
- 形状被填充上随机颜色（从基础颜色库中选取）
- 形状上方显示3个颜色名称按钮，其中只有1个是正确答案
- 玩家需要在10秒内点击正确的颜色名称按钮
- 每成功识别一次，收集一颗对应颜色的元素水晶碎片

**奖励机制**：
- 收集5个同色碎片 = 1颗完整水晶
- 收集不同颜色水晶解锁新关卡和形状

### 2. 进阶模式 - 多形状挑战
**核心玩法**：
- 同时出现2-3个不同形状，每个填充不同颜色
- 每个形状对应一个颜色名称按钮
- 玩家需要在15秒内依次点击正确的颜色名称，匹配相应图形
- 必须按照从左到右或从上到下的顺序点击

**增强难度**：
- **干扰元素**：周围出现相似颜色的干扰图形
- **颜色名称干扰**：按钮中混入相似读音的错误名称（如"蓝"和"篮"）

### 3. 高级模式 - 动态干扰挑战
**核心玩法**：
- 图形在屏幕中缓慢移动
- 图形颜色会动态变化（每2-3秒变换一次）
- 玩家需要在移动过程中准确识别当前颜色并点击正确按钮

**Stroop效应挑战**：
- 显示颜色词汇，但文字颜色与词汇含义不符
- 例如：用蓝色字体显示"红色"，玩家需要选择"蓝色"
- 增加认知负荷，训练注意力控制

## 🎨 颜色系统

### 基础颜色（10种）
红色、橙色、黄色、绿色、蓝色、紫色、粉色、棕色、黑色、白色

### 进阶颜色（20种）
深红、浅蓝、橄榄绿、柠檬黄、玫瑰红、天蓝、草绿、橘红、淡紫、米白、
深紫、浅绿、金黄、海蓝、桃红、灰色、银色、青色、洋红、深棕

### 高级颜色（30种）
青绿、靛蓝、赭石、象牙白、午夜蓝、珊瑚红、薄荷绿、香槟色、酒红、
孔雀蓝、薰衣草紫、橄榄黄、石板灰、奶油色、森林绿、宝石蓝、胭脂红、
柠檬绿、古铜色、珍珠白、墨绿、玫瑰金、湖蓝、杏色、茄紫、
翡翠绿、琥珀色、钢蓝、象牙黄、深海蓝

## 🏆 难度设置

### 网格大小
- 简单：3x3网格（9个颜色选项）
- 中等：4x4网格（16个颜色选项）
- 困难：5x5网格（25个颜色选项）

### 时间限制
- 基础模式：10秒/题
- 进阶模式：15秒/题
- 高级模式：8秒/题

### 颜色相似度
- 低相似度：颜色差异明显
- 中相似度：颜色有一定相似性
- 高相似度：颜色非常接近，需要仔细辨别

## 📊 统计系统

### 基础统计
- 总游戏次数
- 今日游戏次数
- 最佳准确率
- 平均反应时间
- 连续正确次数记录

### 详细记录
- 每次游戏的准确率
- 每种颜色的识别准确率
- 不同难度下的表现
- 进步趋势图表

## 🎁 成就系统

### 基础成就
- 颜色新手：完成10次训练
- 色彩达人：准确率达到90%
- 快速反应：平均反应时间少于3秒

### 进阶成就
- 彩虹收集者：收集所有基础颜色水晶
- 色彩大师：在高级模式下连续答对20题
- 抗干扰专家：在Stroop模式下准确率达到80%

## 🎨 UI设计

### 主界面
- 清新明亮的背景色调
- 水晶收集进度展示
- 统计数据卡片
- 难度选择按钮

### 游戏界面
- 中央形状显示区域
- 上方颜色名称按钮区
- 底部计时器和分数
- 右上角暂停按钮

### 庆祝界面
- 水晶收集动画
- 成绩展示
- 再来一次/返回主页按钮

## 🔧 技术实现

### 文件结构
```
ColorTrainingActivity.java - 颜色训练主页
ColorGameActivity.java - 颜色游戏界面
ColorShape.java - 形状绘制类
ColorManager.java - 颜色管理器
ColorGameStats.java - 游戏统计
```

### 核心功能
1. 随机颜色生成算法
2. 形状绘制系统
3. 颜色相似度计算
4. 反应时间精确计时
5. 水晶收集动画系统

### 数据存储
- SharedPreferences存储游戏设置和统计
- 本地数据库存储详细游戏记录
- 水晶收集进度持久化

## 📅 开发计划

### 第一阶段（基础功能）
1. 创建ColorTrainingActivity主页面
2. 实现基础模式的颜色识别游戏
3. 添加基础统计功能
4. 集成到主应用

### 第二阶段（进阶功能）
1. 实现多形状挑战模式
2. 添加水晶收集系统
3. 完善统计和成就系统

### 第三阶段（高级功能）
1. 实现动态干扰模式
2. 添加Stroop效应挑战
3. 优化UI和动画效果

## 🎯 预期效果
通过颜色识别训练，用户将能够：
- 提升视觉注意力和色彩敏感度
- 增强快速反应能力
- 训练抗干扰专注力
- 在游戏化环境中享受学习过程
