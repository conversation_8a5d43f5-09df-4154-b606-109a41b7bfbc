<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/background_view">

    <!-- 渐变背景 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/countdown_gradient_background" />

    <!-- 准备消息 -->
    <TextView
        android:id="@+id/tv_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_above="@+id/countdown_container"
        android:layout_marginBottom="60dp"
        android:text="准备开始挑战！"
        android:textSize="24sp"
        android:textColor="#FFFFFF"
        android:textStyle="bold"
        android:gravity="center"
        android:shadowColor="#40000000"
        android:shadowDx="2"
        android:shadowDy="2"
        android:shadowRadius="4" />

    <!-- 倒计时容器 - 给足够的空间防止裁剪 -->
    <FrameLayout
        android:id="@+id/countdown_container"
        android:layout_width="300dp"
        android:layout_height="300dp"
        android:layout_centerInParent="true"
        android:clipChildren="false"
        android:clipToPadding="false">

        <!-- 倒计时数字 -->
        <TextView
            android:id="@+id/tv_countdown"
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:layout_gravity="center"
            android:text="3"
            android:textSize="80sp"
            android:textColor="#FFFFFF"
            android:textStyle="bold"
            android:gravity="center"
            android:background="@drawable/countdown_circle_background"
            android:shadowColor="#40000000"
            android:shadowDx="4"
            android:shadowDy="4"
            android:shadowRadius="8"
            android:maxLines="1"
            android:singleLine="true" />

    </FrameLayout>

    <!-- 装饰性粒子效果容器 -->
    <FrameLayout
        android:id="@+id/particles_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</RelativeLayout>
