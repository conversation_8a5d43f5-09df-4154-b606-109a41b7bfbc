// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        jcenter()
        mavenCentral()
        google()
        maven { url 'https://jitpack.io' }
        maven {
            url "https://artifact.bytedance.com/repository/pangle"
        }
    }
    dependencies {
        //https://developer.android.google.cn/studio/releases/gradle-plugin
        classpath 'com.android.tools.build:gradle:4.0.1'
        //classpath 'com.android.tools.build:gradle:3.6.4'
    }
}

allprojects {
    repositories {
        jcenter()
        mavenCentral()
        google()
        maven { url 'https://jitpack.io' }
        maven {
            url "https://artifact.bytedance.com/repository/pangle"
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

tasks.withType(JavaCompile) {
    options.encoding = "UTF-8"
}