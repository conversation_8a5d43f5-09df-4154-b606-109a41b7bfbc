package com.example.concentration;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.material.button.MaterialButton;
import java.util.ArrayList;
import java.util.List;

public class ConceptAnimationDemoActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private TextView tvTitle, tvNarration, tvFormula, tvQuestion;
    private LinearLayout containerGroups, containerTotal;
    private MaterialButton btnPlay, btnNext, btnComplete, btnAnswer1, btnAnswer2, btnAnswer3;
    
    private int currentDemo = 0;
    private final int totalDemos = 3;
    private Handler handler = new Handler();
    private boolean isAnimating = false;
    private VoiceManager voiceManager;
    private SharedPreferences prefs;

    // 演示数据：{组数, 每组数量, 物品emoji, 描述, 正确答案}
    private final String[][] demos = {
        {"3", "4", "🎁", "礼物盒", "12"},
        {"4", "3", "🍭", "棒棒糖", "12"},
        {"2", "5", "🌟", "小星星", "10"}
    };
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_concept_animation_demo);

        prefs = getSharedPreferences("multiplication_table_prefs", MODE_PRIVATE);

        initViews();
        setupClickListeners();
        initVoiceManager();
        loadProgress();
        startDemo();
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        tvTitle = findViewById(R.id.tv_title);
        tvNarration = findViewById(R.id.tv_narration);
        tvFormula = findViewById(R.id.tv_formula);
        tvQuestion = findViewById(R.id.tv_question);
        containerGroups = findViewById(R.id.container_groups);
        containerTotal = findViewById(R.id.container_total);
        btnPlay = findViewById(R.id.btn_play);
        btnNext = findViewById(R.id.btn_next);
        btnComplete = findViewById(R.id.btn_complete);
        btnAnswer1 = findViewById(R.id.btn_answer1);
        btnAnswer2 = findViewById(R.id.btn_answer2);
        btnAnswer3 = findViewById(R.id.btn_answer3);
    }

    private void initVoiceManager() {
        voiceManager = VoiceManager.getInstance(this);
    }

    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        
        btnPlay.setOnClickListener(v -> {
            if (!isAnimating) {
                // 开始详细的语音引导和动画
                String[] demo = demos[currentDemo];
                int groups = Integer.parseInt(demo[0]);
                int itemsPerGroup = Integer.parseInt(demo[1]);
                String description = demo[3];

                // 先显示所有动物，然后开始语音引导
                createGroups(groups, itemsPerGroup, demo[2]);
                startDetailedVoiceGuide(groups, itemsPerGroup, description);
            }
        });
        
        btnNext.setOnClickListener(v -> {
            if (currentDemo < totalDemos - 1) {
                currentDemo++;
                // 每次进入下一演示都保存进度
                saveCurrentProgress();
                startDemo();
            } else {
                // 所有演示完成，保存进度并返回
                saveProgress();
                setResult(RESULT_OK);
                finish();
            }
        });
        
        btnComplete.setOnClickListener(v -> {
            // 保存完成进度
            saveProgress();
            setResult(RESULT_OK);
            finish();
        });
        
        // 答案按钮点击事件
        View.OnClickListener answerClickListener = v -> {
            MaterialButton clickedButton = (MaterialButton) v;
            String answer = clickedButton.getText().toString();
            String correctAnswer = demos[currentDemo][4];
            
            if (answer.equals(correctAnswer)) {
                showCorrectAnswer(clickedButton);
            } else {
                showWrongAnswer(clickedButton);
            }
        };
        
        btnAnswer1.setOnClickListener(answerClickListener);
        btnAnswer2.setOnClickListener(answerClickListener);
        btnAnswer3.setOnClickListener(answerClickListener);
    }
    
    private void startDemo() {
        String[] demo = demos[currentDemo];
        int groups = Integer.parseInt(demo[0]);
        int itemsPerGroup = Integer.parseInt(demo[1]);
        String item = demo[2];
        String description = demo[3];
        String correctAnswer = demo[4];

        // 更新标题
        tvTitle.setText("第" + (currentDemo + 1) + "个演示：" + description);

        // 重置UI状态
        resetUI();

        // 创建分组显示
        createGroups(groups, itemsPerGroup, item);

        // 只播放初始提示，等待用户点击播放按钮
        String initialPrompt = "观察小动物的排列，点击播放动画按钮开始学习";
        tvNarration.setText(initialPrompt);
        voiceManager.speakWithCallback(initialPrompt, null);

        // 设置问题和答案选项
        setupQuestion(correctAnswer);

        // 更新按钮状态
        updateButtons();
    }

    private void startDetailedVoiceGuide(int groups, int itemsPerGroup, String description) {
        isAnimating = true;
        btnPlay.setEnabled(false);

        // 第一步：观察排列
        String observeText = "观察小动物的排列，有几行呀，每行有几个小动物呀";
        tvNarration.setText(observeText);
        voiceManager.speakWithCallback(observeText, () -> {
            // 等待语音完成后再开始下一步
            handler.postDelayed(() -> {
                // 第二步：逐行闪烁和语音
                highlightRowsSequentially(groups, itemsPerGroup, description);
            }, 500);
        });
    }

    private void highlightRowsSequentially(int groups, int itemsPerGroup, String description) {
        highlightRowIndex = 0;
        highlightNextRow(groups, itemsPerGroup, description);
    }

    private int highlightRowIndex = 0;

    private void highlightNextRow(int groups, int itemsPerGroup, String description) {
        if (highlightRowIndex >= groups) {
            // 所有行都高亮完毕，开始数每行的个数
            countItemsInFirstRow(groups, itemsPerGroup, description);
            return;
        }

        // 高亮当前行
        LinearLayout currentRow = (LinearLayout) containerGroups.getChildAt(highlightRowIndex);
        if (currentRow != null) {
            // 放大闪烁动画
            currentRow.animate()
                .scaleX(1.2f)
                .scaleY(1.2f)
                .setDuration(300)
                .withEndAction(() -> {
                    currentRow.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(300)
                        .start();
                })
                .start();
        }

        // 语音说明当前行
        String rowText;
        if (highlightRowIndex == 0) {
            rowText = "这是第1行";
        } else {
            rowText = "这是第" + (highlightRowIndex + 1) + "行";
        }

        tvNarration.setText(rowText);
        voiceManager.speakWithCallback(rowText, () -> {
            highlightRowIndex++;
            // 等待语音完成后再继续下一行
            handler.postDelayed(() -> highlightNextRow(groups, itemsPerGroup, description), 800);
        });
    }

    private void countItemsInFirstRow(int groups, int itemsPerGroup, String description) {
        String countText = "一共有" + groups + "行";
        tvNarration.setText(countText);
        voiceManager.speakWithCallback(countText, () -> {
            // 开始数第一行的个数
            countItemsSequentially(itemsPerGroup, description, groups);
        });
    }

    private void countItemsSequentially(int itemsPerGroup, String description, int groups) {
        String countIntro = "每一行有：";
        tvNarration.setText(countIntro);
        voiceManager.speakWithCallback(countIntro, () -> {
            // 等待语音完成后开始逐个数数
            handler.postDelayed(() -> {
                countIndex = 1;
                countNextItem(itemsPerGroup, description, groups);
            }, 500);
        });
    }

    private int countIndex = 1;

    private void countNextItem(int itemsPerGroup, String description, int groups) {
        if (countIndex > itemsPerGroup) {
            // 数完了，说总结
            String summary = itemsPerGroup + "个小动物";
            tvNarration.setText(summary);
            voiceManager.speakWithCallback(summary, () -> {
                // 最后说明乘法含义
                explainMultiplication(groups, itemsPerGroup);
            });
            return;
        }

        // 高亮第一行的当前物品
        LinearLayout firstRow = (LinearLayout) containerGroups.getChildAt(0);
        if (firstRow != null && countIndex <= firstRow.getChildCount()) {
            TextView currentItem = (TextView) firstRow.getChildAt(countIndex - 1);
            if (currentItem != null) {
                // 放大闪烁动画
                currentItem.animate()
                    .scaleX(1.3f)
                    .scaleY(1.3f)
                    .setDuration(200)
                    .withEndAction(() -> {
                        currentItem.animate()
                            .scaleX(1.0f)
                            .scaleY(1.0f)
                            .setDuration(200)
                            .start();
                    })
                    .start();
            }
        }

        // 语音数数
        String countText = String.valueOf(countIndex);
        voiceManager.speakWithCallback(countText, () -> {
            countIndex++;
            // 等待语音完成后继续下一个
            handler.postDelayed(() -> countNextItem(itemsPerGroup, description, groups), 800);
        });
    }

    private void explainMultiplication(int groups, int itemsPerGroup) {
        int total = groups * itemsPerGroup;
        String explanation = "这就是乘法的意思，有" + groups + "行，每行" + itemsPerGroup + "个，总共" + total + "个，就是" + groups + "乘以" + itemsPerGroup + "等于" + total;
        tvNarration.setText(explanation);
        voiceManager.speakWithCallback(explanation, () -> {
            // 语音完成后，等待一段时间再显示下一课按钮
            handler.postDelayed(() -> {
                showNextLessonButton();
            }, 1000);
        });
    }

    private void showNextLessonButton() {
        // 这里可以显示"下一课"按钮或其他UI元素
        btnNext.setVisibility(View.VISIBLE);
        isAnimating = false;
        btnPlay.setEnabled(true);
    }
    
    private void createGroups(int groups, int itemsPerGroup, String item) {
        containerGroups.removeAllViews();
        containerTotal.removeAllViews();
        
        for (int g = 0; g < groups; g++) {
            // 创建组容器
            LinearLayout groupLayout = new LinearLayout(this);
            groupLayout.setOrientation(LinearLayout.HORIZONTAL);
            groupLayout.setPadding(16, 8, 16, 8);
            
            // 添加组内物品
            for (int i = 0; i < itemsPerGroup; i++) {
                TextView itemView = new TextView(this);
                itemView.setText(item);
                itemView.setTextSize(24);
                itemView.setPadding(8, 8, 8, 8);
                itemView.setAlpha(0f); // 初始透明
                groupLayout.addView(itemView);
            }
            
            containerGroups.addView(groupLayout);
        }
    }
    
    private void setupQuestion(String correctAnswer) {
        tvQuestion.setText("总共有多少个？");
        
        // 生成答案选项（正确答案 + 2个错误答案）
        int correct = Integer.parseInt(correctAnswer);
        int wrong1 = correct + 2;
        int wrong2 = correct - 2;
        if (wrong2 <= 0) wrong2 = correct + 4;
        
        // 随机分配到三个按钮
        List<String> answers = new ArrayList<>();
        answers.add(String.valueOf(correct));
        answers.add(String.valueOf(wrong1));
        answers.add(String.valueOf(wrong2));
        
        // 简单打乱（这里用固定模式，实际可以随机）
        btnAnswer1.setText(answers.get(0));
        btnAnswer2.setText(answers.get(1));
        btnAnswer3.setText(answers.get(2));
        
        // 重置按钮状态
        resetAnswerButtons();
    }
    
    private void playAnimation() {
        isAnimating = true;
        btnPlay.setEnabled(false);
        
        String[] demo = demos[currentDemo];
        int groups = Integer.parseInt(demo[0]);
        int itemsPerGroup = Integer.parseInt(demo[1]);
        
        // 逐组显示动画
        for (int g = 0; g < groups; g++) {
            final int groupIndex = g;
            handler.postDelayed(() -> {
                animateGroup(groupIndex, itemsPerGroup);
                
                // 更新旁白
                String groupNarration;
                if (groupIndex == 0) {
                    groupNarration = "第1组：" + itemsPerGroup + "个";
                } else {
                    groupNarration = "第" + (groupIndex + 1) + "组：又是" + itemsPerGroup + "个";
                }
                tvNarration.setText(groupNarration);
                voiceManager.speakWithCallback(groupNarration, null);
                
            }, g * 1500);
        }
        
        // 显示最终结果
        handler.postDelayed(() -> {
            showFinalResult();
        }, groups * 1500 + 1000);
    }
    
    private void animateGroup(int groupIndex, int itemsPerGroup) {
        LinearLayout groupLayout = (LinearLayout) containerGroups.getChildAt(groupIndex);
        
        for (int i = 0; i < itemsPerGroup; i++) {
            TextView itemView = (TextView) groupLayout.getChildAt(i);
            
            handler.postDelayed(() -> {
                ObjectAnimator fadeIn = ObjectAnimator.ofFloat(itemView, "alpha", 0f, 1f);
                ObjectAnimator scaleX = ObjectAnimator.ofFloat(itemView, "scaleX", 0.5f, 1f);
                ObjectAnimator scaleY = ObjectAnimator.ofFloat(itemView, "scaleY", 0.5f, 1f);
                
                AnimatorSet animatorSet = new AnimatorSet();
                animatorSet.playTogether(fadeIn, scaleX, scaleY);
                animatorSet.setDuration(300);
                animatorSet.start();
            }, i * 200);
        }
    }
    
    private void showFinalResult() {
        String[] demo = demos[currentDemo];
        int groups = Integer.parseInt(demo[0]);
        int itemsPerGroup = Integer.parseInt(demo[1]);
        int total = groups * itemsPerGroup;
        
        // 显示公式
        String formula = groups + " × " + itemsPerGroup + " = " + total;
        tvFormula.setText(formula);
        tvFormula.setVisibility(View.VISIBLE);
        
        // 更新旁白
        String finalNarration = "所以，" + groups + "个" + itemsPerGroup + "相加，就是" + groups + "×" + itemsPerGroup + "=" + total;
        tvNarration.setText(finalNarration);
        voiceManager.speakWithCallback(finalNarration, null);
        
        // 显示问题
        tvQuestion.setVisibility(View.VISIBLE);
        btnAnswer1.setVisibility(View.VISIBLE);
        btnAnswer2.setVisibility(View.VISIBLE);
        btnAnswer3.setVisibility(View.VISIBLE);
        
        isAnimating = false;
        btnPlay.setEnabled(true);
    }
    
    private void showCorrectAnswer(MaterialButton button) {
        button.setBackgroundTintList(getColorStateList(android.R.color.holo_green_light));
        button.setText(button.getText() + " ✅");
        
        // 禁用所有答案按钮
        btnAnswer1.setEnabled(false);
        btnAnswer2.setEnabled(false);
        btnAnswer3.setEnabled(false);
        
        String correctText = "太棒了！答对了！";
        tvNarration.setText(correctText + "🎉");
        voiceManager.speakWithCallback(correctText, null);
    }
    
    private void showWrongAnswer(MaterialButton button) {
        button.setBackgroundTintList(getColorStateList(android.R.color.holo_red_light));
        button.setText(button.getText() + " ❌");
        
        // 显示正确答案
        String correctAnswer = demos[currentDemo][4];
        if (btnAnswer1.getText().toString().startsWith(correctAnswer)) {
            btnAnswer1.setBackgroundTintList(getColorStateList(android.R.color.holo_green_light));
        } else if (btnAnswer2.getText().toString().startsWith(correctAnswer)) {
            btnAnswer2.setBackgroundTintList(getColorStateList(android.R.color.holo_green_light));
        } else if (btnAnswer3.getText().toString().startsWith(correctAnswer)) {
            btnAnswer3.setBackgroundTintList(getColorStateList(android.R.color.holo_green_light));
        }
        
        // 禁用所有答案按钮
        btnAnswer1.setEnabled(false);
        btnAnswer2.setEnabled(false);
        btnAnswer3.setEnabled(false);
        
        String wrongText = "再想想看，正确答案是" + correctAnswer + "哦！";
        tvNarration.setText(wrongText);
        voiceManager.speakWithCallback(wrongText, null);
    }
    
    private void resetUI() {
        tvFormula.setVisibility(View.INVISIBLE);
        tvQuestion.setVisibility(View.INVISIBLE);
        btnAnswer1.setVisibility(View.INVISIBLE);
        btnAnswer2.setVisibility(View.INVISIBLE);
        btnAnswer3.setVisibility(View.INVISIBLE);
        resetAnswerButtons();
    }
    
    private void resetAnswerButtons() {
        btnAnswer1.setEnabled(true);
        btnAnswer2.setEnabled(true);
        btnAnswer3.setEnabled(true);
        
        btnAnswer1.setBackgroundTintList(getColorStateList(R.color.md_theme_light_primary));
        btnAnswer2.setBackgroundTintList(getColorStateList(R.color.md_theme_light_primary));
        btnAnswer3.setBackgroundTintList(getColorStateList(R.color.md_theme_light_primary));
    }
    
    private void updateButtons() {
        // 始终显示"下一课"按钮，最后一课点击后自动完成
        btnNext.setText(currentDemo < totalDemos - 1 ? "下一课" : "完成学习");
        btnNext.setVisibility(View.VISIBLE);
        btnComplete.setVisibility(View.GONE);
    }
    
    private void loadProgress() {
        // 加载上次学习的课程进度
        currentDemo = prefs.getInt("animation_demo_current_demo", 0);
        if (currentDemo >= totalDemos) {
            currentDemo = 0; // 如果已完成所有课程，从头开始
        }
    }

    private void saveCurrentProgress() {
        // 保存当前演示进度（不标记为完成）
        prefs.edit().putInt("animation_demo_current_demo", currentDemo).apply();
    }

    private void saveProgress() {
        // 保存当前课程进度
        prefs.edit().putInt("animation_demo_current_demo", currentDemo + 1).apply();

        // 标记课程3已完成
        prefs.edit().putBoolean("animation_demo_completed", true).apply();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
        if (voiceManager != null) {
            voiceManager.stop();
        }
    }
}
