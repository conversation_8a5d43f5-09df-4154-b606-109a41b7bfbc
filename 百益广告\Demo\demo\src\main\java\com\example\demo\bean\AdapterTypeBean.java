package com.example.demo.bean;

import java.util.Map;

public class AdapterTypeBean {
    private int type;
    private Map<String, Object> data;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }
}
