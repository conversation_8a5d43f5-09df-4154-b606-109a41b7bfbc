<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_number_bg">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="1 的乘法表"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:gravity="center"
            android:layout_marginEnd="48dp" />

    </LinearLayout>

    <!-- 进度信息卡片 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@android:color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="📊 学习统计"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onSurface"
                android:gravity="center"
                android:layout_marginBottom="16dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_progress"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="学习进度：0%"
                        android:textSize="12sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_best_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="最佳时间：暂无记录"
                        android:textSize="12sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_total_games"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="总游戏次数：0次"
                        android:textSize="12sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 学习模式选择 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 节拍学习 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginBottom="12dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@android:color/white">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_rhythm_learning"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="🎵 节拍学习\n跟着节奏学乘法"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_secondary"
                style="@style/Widget.Material3.Button.TextButton"
                android:background="@android:color/transparent" />

        </androidx.cardview.widget.CardView>

        <!-- 记忆卡片游戏 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginBottom="12dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@android:color/white">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_memory_card"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="🃏 记忆卡片游戏\n翻牌记忆乘法表"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_primary"
                style="@style/Widget.Material3.Button.TextButton"
                android:background="@android:color/transparent" />

        </androidx.cardview.widget.CardView>

        <!-- 拼图挑战 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@android:color/white">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_puzzle_challenge"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="📝 答题挑战\n回答乘法问题"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_tertiary"
                style="@style/Widget.Material3.Button.TextButton"
                android:background="@android:color/transparent" />

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</LinearLayout>
