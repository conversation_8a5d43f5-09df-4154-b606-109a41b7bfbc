<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_light_bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 顶部标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="概念测试 - 第1题"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onBackground"
                android:gravity="center"
                android:layout_marginEnd="48dp" />

        </LinearLayout>

        <!-- 进度和得分 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/md_theme_light_primaryContainer">

                <TextView
                    android:id="@+id/tv_progress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="进度：1/6"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onPrimaryContainer"
                    android:gravity="center"
                    android:padding="12dp" />

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/md_theme_light_secondaryContainer">

                <TextView
                    android:id="@+id/tv_score"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="得分：0/6"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSecondaryContainer"
                    android:gravity="center"
                    android:padding="12dp" />

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- 题目显示区域 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🤔 请选择正确答案"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSurface"
                    android:gravity="center"
                    android:layout_marginBottom="20dp" />

                <TextView
                    android:id="@+id/tv_question"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="3行小鸟，每行4只，总共多少只？"
                    android:textSize="18sp"
                    android:textColor="@color/md_theme_light_onSurface"
                    android:gravity="center"
                    android:lineSpacingExtra="4dp"
                    android:layout_marginBottom="20dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 选项区域 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/md_theme_light_surfaceVariant">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📝 选择答案"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSurfaceVariant"
                    android:gravity="center"
                    android:layout_marginBottom="16dp" />

                <!-- 选项容器 -->
                <LinearLayout
                    android:id="@+id/container_options"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 控制按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_next"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:text="下一题 ▶️"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="28dp"
                app:backgroundTint="@color/md_theme_light_primary"
                android:textColor="@android:color/white"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_complete"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:text="完成测试 ✅"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="28dp"
                app:backgroundTint="@color/md_theme_light_tertiary"
                android:textColor="@android:color/white"
                android:layout_marginStart="8dp"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
