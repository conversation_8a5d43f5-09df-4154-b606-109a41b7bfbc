package com.example.concentration;

import android.graphics.Color;
import java.util.*;

public class ColorManager {
    private static ColorManager instance;
    
    // 颜色定义类
    public static class ColorInfo {
        public final String name;
        public final int color;
        public final String category;
        
        public ColorInfo(String name, int color, String category) {
            this.name = name;
            this.color = color;
            this.category = category;
        }
    }
    
    // 基础颜色库
    private final List<ColorInfo> basicColors = Arrays.asList(
        new ColorInfo("红色", Color.parseColor("#F44336"), "basic"),
        new ColorInfo("橙色", Color.parseColor("#FF9800"), "basic"),
        new ColorInfo("黄色", Color.parseColor("#FFEB3B"), "basic"),
        new ColorInfo("绿色", Color.parseColor("#4CAF50"), "basic"),
        new ColorInfo("蓝色", Color.parseColor("#2196F3"), "basic"),
        new ColorInfo("紫色", Color.parseColor("#9C27B0"), "basic"),
        new ColorInfo("粉色", Color.parseColor("#E91E63"), "basic"),
        new ColorInfo("棕色", Color.parseColor("#795548"), "basic"),
        new ColorInfo("黑色", Color.parseColor("#424242"), "basic"),
        new ColorInfo("白色", Color.parseColor("#FAFAFA"), "basic")
    );
    
    // 进阶颜色库
    private final List<ColorInfo> advancedColors = Arrays.asList(
        new ColorInfo("深红", Color.parseColor("#C62828"), "advanced"),
        new ColorInfo("浅蓝", Color.parseColor("#81D4FA"), "advanced"),
        new ColorInfo("橄榄绿", Color.parseColor("#689F38"), "advanced"),
        new ColorInfo("柠檬黄", Color.parseColor("#F9A825"), "advanced"),
        new ColorInfo("玫瑰红", Color.parseColor("#AD1457"), "advanced"),
        new ColorInfo("天蓝", Color.parseColor("#29B6F6"), "advanced"),
        new ColorInfo("草绿", Color.parseColor("#66BB6A"), "advanced"),
        new ColorInfo("橘红", Color.parseColor("#FF5722"), "advanced"),
        new ColorInfo("淡紫", Color.parseColor("#BA68C8"), "advanced"),
        new ColorInfo("米白", Color.parseColor("#F5F5F5"), "advanced"),
        new ColorInfo("深紫", Color.parseColor("#7B1FA2"), "advanced"),
        new ColorInfo("浅绿", Color.parseColor("#A5D6A7"), "advanced"),
        new ColorInfo("金黄", Color.parseColor("#FFD54F"), "advanced"),
        new ColorInfo("海蓝", Color.parseColor("#0277BD"), "advanced"),
        new ColorInfo("桃红", Color.parseColor("#F06292"), "advanced"),
        new ColorInfo("灰色", Color.parseColor("#757575"), "advanced"),
        new ColorInfo("银色", Color.parseColor("#BDBDBD"), "advanced"),
        new ColorInfo("青色", Color.parseColor("#00BCD4"), "advanced"),
        new ColorInfo("洋红", Color.parseColor("#E040FB"), "advanced"),
        new ColorInfo("深棕", Color.parseColor("#5D4037"), "advanced")
    );
    
    // 高级颜色库
    private final List<ColorInfo> expertColors = Arrays.asList(
        new ColorInfo("青绿", Color.parseColor("#26A69A"), "expert"),
        new ColorInfo("靛蓝", Color.parseColor("#3F51B5"), "expert"),
        new ColorInfo("赭石", Color.parseColor("#8D6E63"), "expert"),
        new ColorInfo("象牙白", Color.parseColor("#FFFEF7"), "expert"),
        new ColorInfo("午夜蓝", Color.parseColor("#1A237E"), "expert"),
        new ColorInfo("珊瑚红", Color.parseColor("#FF7043"), "expert"),
        new ColorInfo("薄荷绿", Color.parseColor("#81C784"), "expert"),
        new ColorInfo("香槟色", Color.parseColor("#FFF8E1"), "expert"),
        new ColorInfo("酒红", Color.parseColor("#880E4F"), "expert"),
        new ColorInfo("孔雀蓝", Color.parseColor("#006064"), "expert"),
        new ColorInfo("薰衣草紫", Color.parseColor("#CE93D8"), "expert"),
        new ColorInfo("橄榄黄", Color.parseColor("#827717"), "expert"),
        new ColorInfo("石板灰", Color.parseColor("#455A64"), "expert"),
        new ColorInfo("奶油色", Color.parseColor("#FFF9C4"), "expert"),
        new ColorInfo("森林绿", Color.parseColor("#2E7D32"), "expert"),
        new ColorInfo("宝石蓝", Color.parseColor("#1565C0"), "expert"),
        new ColorInfo("胭脂红", Color.parseColor("#C2185B"), "expert"),
        new ColorInfo("柠檬绿", Color.parseColor("#8BC34A"), "expert"),
        new ColorInfo("古铜色", Color.parseColor("#BF360C"), "expert"),
        new ColorInfo("珍珠白", Color.parseColor("#ECEFF1"), "expert"),
        new ColorInfo("墨绿", Color.parseColor("#1B5E20"), "expert"),
        new ColorInfo("玫瑰金", Color.parseColor("#FFAB91"), "expert"),
        new ColorInfo("湖蓝", Color.parseColor("#0288D1"), "expert"),
        new ColorInfo("杏色", Color.parseColor("#FFCC80"), "expert"),
        new ColorInfo("茄紫", Color.parseColor("#6A1B9A"), "expert"),
        new ColorInfo("翡翠绿", Color.parseColor("#00695C"), "expert"),
        new ColorInfo("琥珀色", Color.parseColor("#FF8F00"), "expert"),
        new ColorInfo("钢蓝", Color.parseColor("#37474F"), "expert"),
        new ColorInfo("象牙黄", Color.parseColor("#FFF59D"), "expert"),
        new ColorInfo("深海蓝", Color.parseColor("#01579B"), "expert")
    );
    
    private ColorManager() {}
    
    public static ColorManager getInstance() {
        if (instance == null) {
            instance = new ColorManager();
        }
        return instance;
    }
    
    // 根据难度获取颜色库
    public List<ColorInfo> getColorsByDifficulty(String difficulty) {
        switch (difficulty.toLowerCase()) {
            case "easy":
                return new ArrayList<>(basicColors);
            case "medium":
                List<ColorInfo> mediumColors = new ArrayList<>(basicColors);
                mediumColors.addAll(advancedColors.subList(0, 10));
                return mediumColors;
            case "hard":
                List<ColorInfo> hardColors = new ArrayList<>(basicColors);
                hardColors.addAll(advancedColors);
                hardColors.addAll(expertColors.subList(0, 10));
                return hardColors;
            default:
                return new ArrayList<>(basicColors);
        }
    }
    
    // 获取随机目标颜色
    public ColorInfo getRandomTargetColor(String difficulty) {
        List<ColorInfo> colors = getColorsByDifficulty(difficulty);
        Random random = new Random();
        return colors.get(random.nextInt(colors.size()));
    }
    
    // 获取颜色选择选项（包含正确答案和干扰项）
    public List<ColorInfo> getColorOptions(ColorInfo targetColor, String difficulty, int gridSize) {
        List<ColorInfo> allColors = getColorsByDifficulty(difficulty);
        List<ColorInfo> options = new ArrayList<>();
        
        // 添加正确答案
        options.add(targetColor);
        
        // 添加干扰项
        Random random = new Random();
        while (options.size() < gridSize * gridSize) {
            ColorInfo randomColor = allColors.get(random.nextInt(allColors.size()));
            if (!options.contains(randomColor)) {
                options.add(randomColor);
            }
        }
        
        // 打乱顺序
        Collections.shuffle(options);
        return options;
    }
    
    // 获取相似颜色（用于增加难度）
    public List<ColorInfo> getSimilarColors(ColorInfo targetColor, String difficulty, int count) {
        List<ColorInfo> allColors = getColorsByDifficulty(difficulty);
        List<ColorInfo> similarColors = new ArrayList<>();
        
        // 简单的相似度判断（可以后续优化为更复杂的颜色空间距离计算）
        for (ColorInfo color : allColors) {
            if (!color.equals(targetColor) && isSimilarColor(targetColor, color)) {
                similarColors.add(color);
                if (similarColors.size() >= count) {
                    break;
                }
            }
        }
        
        // 如果相似颜色不够，用随机颜色补充
        Random random = new Random();
        while (similarColors.size() < count) {
            ColorInfo randomColor = allColors.get(random.nextInt(allColors.size()));
            if (!randomColor.equals(targetColor) && !similarColors.contains(randomColor)) {
                similarColors.add(randomColor);
            }
        }
        
        return similarColors;
    }
    
    // 简单的颜色相似度判断
    private boolean isSimilarColor(ColorInfo color1, ColorInfo color2) {
        // 基于颜色名称的简单相似度判断
        String name1 = color1.name;
        String name2 = color2.name;
        
        // 同类颜色判断
        if ((name1.contains("红") && name2.contains("红")) ||
            (name1.contains("蓝") && name2.contains("蓝")) ||
            (name1.contains("绿") && name2.contains("绿")) ||
            (name1.contains("黄") && name2.contains("黄")) ||
            (name1.contains("紫") && name2.contains("紫"))) {
            return true;
        }
        
        return false;
    }
    
    // 获取颜色的RGB值
    public int[] getRGB(int color) {
        return new int[]{
            Color.red(color),
            Color.green(color),
            Color.blue(color)
        };
    }
    
    // 计算颜色亮度
    public double getBrightness(int color) {
        int[] rgb = getRGB(color);
        return (rgb[0] * 0.299 + rgb[1] * 0.587 + rgb[2] * 0.114) / 255.0;
    }
    
    // 获取对比文字颜色
    public int getContrastTextColor(int backgroundColor) {
        return getBrightness(backgroundColor) > 0.5 ? Color.BLACK : Color.WHITE;
    }

    // 生成干扰颜色名称（用于Stroop效应）
    public String getInterferenceColorName(ColorInfo targetColor) {
        List<ColorInfo> allColors = new ArrayList<>(basicColors);
        allColors.addAll(advancedColors);

        Random random = new Random();
        ColorInfo interferenceColor;
        do {
            interferenceColor = allColors.get(random.nextInt(allColors.size()));
        } while (interferenceColor.equals(targetColor));

        return interferenceColor.name;
    }
}
