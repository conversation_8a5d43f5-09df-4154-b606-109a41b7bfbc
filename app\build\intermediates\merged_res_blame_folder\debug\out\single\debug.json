[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_item_game_record.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\item_game_record.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_game_cell_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\game_cell_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_concept_interactive_count.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_concept_interactive_count.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_item_challenge_answer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\item_challenge_answer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_ic_trophy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\ic_trophy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_number_highlight_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\number_highlight_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_animal_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\animal_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_space_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\space_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_status_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\status_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_cartoon_game_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\cartoon_game_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_target_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\target_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_gradient_number_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\gradient_number_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_difficulty_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\difficulty_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_splash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_modern_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\modern_card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_clickable_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\clickable_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\xml_anythink_bk_tt_file_path.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\xml\\anythink_bk_tt_file_path.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_item_tutorial_step.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\item_tutorial_step.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_animal_game_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\animal_game_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_concept_animation_demo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_concept_animation_demo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_grid_cell_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\grid_cell_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_countdown.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_countdown.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_item_number_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\item_number_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_grid_cell_cartoon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\grid_cell_cartoon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_countdown_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\countdown_gradient_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_grid_container_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\grid_container_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_default_game_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\default_game_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_game.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_game.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_grid_cell_space.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\grid_cell_space.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_timer_background_light.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\timer_background_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_modern_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\modern_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_multiplication_table.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_multiplication_table.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_gradient_background_secondary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\gradient_background_secondary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_dialog_sequence_completion.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\dialog_sequence_completion.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_memory_card_game.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_memory_card_game.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_multiplication_challenge.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_multiplication_challenge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_gradient_light_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\gradient_light_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\mipmap-xhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\mipmap-xhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_multiplication_concept.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_multiplication_concept.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_space_game_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\space_game_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_clean_game_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\clean_game_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_ic_pause.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\ic_pause.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_concept_visual_demo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_concept_visual_demo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_item_memory_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\item_memory_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_ic_brain_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\ic_brain_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_memory_game.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_memory_game.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_item_achievement.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\item_achievement.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_timer_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\timer_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\xml_anythink_bk_gdt_file_path.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\xml\\anythink_bk_gdt_file_path.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "com.example.concentration.app-merged_res-50:/layout_activity_rewards.xml.flat", "source": "com.example.concentration.app-main-52:/layout/activity_rewards.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_light_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\light_gradient_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_multiplication_number.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_multiplication_number.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_color_training.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_color_training.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_schulte_grid.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_schulte_grid.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_ic_skip_next.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\ic_skip_next.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_color_game.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_color_game.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_grid_cell_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\grid_cell_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_gradient_gold.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\gradient_gold.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_number_learning.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_number_learning.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\xml_anythink_bk_sigmob_file_path.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\xml\\anythink_bk_sigmob_file_path.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_celebration.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_celebration.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_dialog_game_completion.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\dialog_game_completion.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_countdown_circle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\countdown_circle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_gradient_stage1_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\gradient_stage1_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_theme_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_theme_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_number_display_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\number_display_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_tutorial.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_tutorial.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_memory_game_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_memory_game_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_rhythm_learning.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_rhythm_learning.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_gradient_stage2_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\gradient_stage2_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_gradient_stage3_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\gradient_stage3_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_gradient_multiplication_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\gradient_multiplication_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_grid_cell_clicked.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\grid_cell_clicked.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_grid_cell_modern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\grid_cell_modern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_grid_cell_wrong.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\grid_cell_wrong.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_cartoon_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\cartoon_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_ic_arrow_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\ic_arrow_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_rewards.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_rewards.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\mipmap-mdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\mipmap-mdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\gradient_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_default_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\default_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_concept_test_game.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_concept_test_game.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\mipmap-xxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\mipmap-xxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\layout_activity_puzzle_challenge.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\layout\\activity_puzzle_challenge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-merged_res-50:\\drawable_countdown_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.concentration.app-main-52:\\drawable\\countdown_background.xml"}]