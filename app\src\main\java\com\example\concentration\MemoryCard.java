package com.example.concentration;

public class MemoryCard {
    public int id;
    public String content;
    public int matchId;
    public boolean isQuestion;
    public boolean isFlipped = false;
    public boolean isMatched = false;

    // 动物图片相关
    public String animalEmoji;
    public int backgroundColor;

    public MemoryCard() {
    }
    
    public MemoryCard(int id, String content, int matchId, boolean isQuestion) {
        this.id = id;
        this.content = content;
        this.matchId = matchId;
        this.isQuestion = isQuestion;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        MemoryCard that = (MemoryCard) obj;
        return id == that.id;
    }
    
    @Override
    public int hashCode() {
        return Integer.hashCode(id);
    }
}
