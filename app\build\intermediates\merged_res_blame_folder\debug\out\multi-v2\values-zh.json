{"logs": [{"outputFile": "com.example.concentration.app-mergeDebugResources-48:/values-zh/values-zh.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\369091b8ae125b1df315664f32e06581\\transformed\\jetified-anythink_core\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,110,177,239,319,399,454,522,577,663,760,833,895,953,1031,1147,1213,1280,1348,1425,1498,1567,1635,1707,1782,1852,1918,1982,2052,2114,2186,2263,2334,2419,2492,2561,2634,2718,2785,2870,2947,3018,3092,3163,3247,3333,3405,3490,3565,3634,3722,3795,3878,3966,4047,4121,4193,4265,4322,4395,4473,4546,4623,4696,4777,4844,4917,4990,5073,5146,5226,5306,5386,5451,5510,5577,5639,5704,5766,5843,5913,5980,6063,6152,6225,6306,6386,6478,6557,6621,6693,6769,6845,6925,7001,7066,7142,7217,7287,7356,7422,7483,7559,7674,7794,7885,7962,8031,8113,8178,8254,8325,8399,8466,8534,8605,8672,8737,8803,8882,8950,9024,9092,9153", "endColumns": "54,66,61,79,79,54,67,54,85,96,72,61,57,77,115,65,66,67,76,72,68,67,71,74,69,65,63,69,61,71,76,70,84,72,68,72,83,66,84,76,70,73,70,83,85,71,84,74,68,87,72,82,87,80,73,71,71,56,72,77,72,76,72,80,66,72,72,82,72,79,79,79,64,58,66,61,64,61,76,69,66,82,88,72,80,79,91,78,63,71,75,75,79,75,64,75,74,69,68,65,60,75,114,119,90,76,68,81,64,75,70,73,66,67,70,66,64,65,78,67,73,67,60,63", "endOffsets": "105,172,234,314,394,449,517,572,658,755,828,890,948,1026,1142,1208,1275,1343,1420,1493,1562,1630,1702,1777,1847,1913,1977,2047,2109,2181,2258,2329,2414,2487,2556,2629,2713,2780,2865,2942,3013,3087,3158,3242,3328,3400,3485,3560,3629,3717,3790,3873,3961,4042,4116,4188,4260,4317,4390,4468,4541,4618,4691,4772,4839,4912,4985,5068,5141,5221,5301,5381,5446,5505,5572,5634,5699,5761,5838,5908,5975,6058,6147,6220,6301,6381,6473,6552,6616,6688,6764,6840,6920,6996,7061,7137,7212,7282,7351,7417,7478,7554,7669,7789,7880,7957,8026,8108,8173,8249,8320,8394,8461,8529,8600,8667,8732,8798,8877,8945,9019,9087,9148,9212"}, "to": {"startLines": "2,3,4,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,145,146,147,148,149,150,151,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,110,177,1279,1359,1439,1494,1562,1617,1703,1800,1873,1935,1993,2071,2187,2253,2320,2388,2465,2538,2607,2675,2747,2822,2892,2958,3022,3092,3154,3226,3303,3374,3459,3532,3601,3674,3758,3825,3910,3987,4058,4132,4203,4287,4373,4445,4530,4605,4674,4762,4835,4918,5006,5087,5161,5233,5305,5362,5435,5513,5586,5663,5736,5817,5884,5957,6030,6113,6186,6266,6346,6426,6491,6550,6617,6679,6744,6806,6883,6953,7020,7103,7192,7265,7346,7426,7518,7597,7661,7733,7809,7885,7965,8041,8106,8182,8257,8327,8396,8462,8523,8599,8714,8834,8925,9002,9071,9153,9218,9294,9365,9439,9506,9574,9645,10540,10605,10671,10750,10818,10892,10960,11021", "endColumns": "54,66,61,79,79,54,67,54,85,96,72,61,57,77,115,65,66,67,76,72,68,67,71,74,69,65,63,69,61,71,76,70,84,72,68,72,83,66,84,76,70,73,70,83,85,71,84,74,68,87,72,82,87,80,73,71,71,56,72,77,72,76,72,80,66,72,72,82,72,79,79,79,64,58,66,61,64,61,76,69,66,82,88,72,80,79,91,78,63,71,75,75,79,75,64,75,74,69,68,65,60,75,114,119,90,76,68,81,64,75,70,73,66,67,70,66,64,65,78,67,73,67,60,63", "endOffsets": "105,172,234,1354,1434,1489,1557,1612,1698,1795,1868,1930,1988,2066,2182,2248,2315,2383,2460,2533,2602,2670,2742,2817,2887,2953,3017,3087,3149,3221,3298,3369,3454,3527,3596,3669,3753,3820,3905,3982,4053,4127,4198,4282,4368,4440,4525,4600,4669,4757,4830,4913,5001,5082,5156,5228,5300,5357,5430,5508,5581,5658,5731,5812,5879,5952,6025,6108,6181,6261,6341,6421,6486,6545,6612,6674,6739,6801,6878,6948,7015,7098,7187,7260,7341,7421,7513,7592,7656,7728,7804,7880,7960,8036,8101,8177,8252,8322,8391,8457,8518,8594,8709,8829,8920,8997,9066,9148,9213,9289,9360,9434,9501,9569,9640,9707,10600,10666,10745,10813,10887,10955,11016,11080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\33a65179fb977c89f954a242644f692a\\transformed\\jetified-anythink_china_core\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,106,158,230,279,348,420,500,570,635,710,779,846,910,975,1037,1095,1167,1236,1312,1390,1463,1551,1628,1721,1784,1860", "endColumns": "50,51,71,48,68,71,79,69,64,74,68,66,63,64,61,57,71,68,75,77,72,87,76,92,62,75,62", "endOffsets": "101,153,225,274,343,415,495,565,630,705,774,841,905,970,1032,1090,1162,1231,1307,1385,1458,1546,1623,1716,1779,1855,1918"}, "to": {"startLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,134,135,136,137,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "239,290,342,414,463,532,604,684,754,819,894,963,1030,1094,1159,1221,9712,9784,9853,9929,10007,10080,10168,10245,10338,10401,10477", "endColumns": "50,51,71,48,68,71,79,69,64,74,68,66,63,64,61,57,71,68,75,77,72,87,76,92,62,75,62", "endOffsets": "285,337,409,458,527,599,679,749,814,889,958,1025,1089,1154,1216,1274,9779,9848,9924,10002,10075,10163,10240,10333,10396,10472,10535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\02d81beee2b37ae2d502f14e157c45a2\\transformed\\jetified-wind-sdk-4.21.1\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,93,133,174,227,299,348,405,456,511,563", "endColumns": "37,39,40,52,71,48,56,50,54,51,51", "endOffsets": "88,128,169,222,294,343,400,451,506,558,610"}, "to": {"startLines": "153,154,155,156,157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11085,11123,11163,11204,11257,11329,11378,11435,11486,11541,11593", "endColumns": "37,39,40,52,71,48,56,50,54,51,51", "endOffsets": "11118,11158,11199,11252,11324,11373,11430,11481,11536,11588,11640"}}]}]}