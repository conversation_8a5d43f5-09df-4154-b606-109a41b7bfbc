<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_stage2_bg">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="🔢 分数字学习"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:gravity="center"
            android:layout_marginEnd="48dp" />

    </LinearLayout>

    <!-- 进度显示 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@android:color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="📊 学习进度"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onSurface"
                android:gravity="center"
                android:layout_marginBottom="12dp" />

            <TextView
                android:id="@+id/tv_progress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="整体进度：0% (1/9 已解锁)"
                android:textSize="14sp"
                android:textColor="@color/md_theme_light_onSurfaceVariant"
                android:gravity="center" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 数字选择网格 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_margin="16dp"
        app:cardCornerRadius="20dp"
        app:cardElevation="8dp"
        app:cardBackgroundColor="@android:color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🎯 选择要学习的数字"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onSurface"
                android:gravity="center"
                android:layout_marginBottom="20dp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_numbers"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 底部按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_back_to_stages"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="返回阶段选择"
            android:textSize="16sp"
            android:textStyle="bold"
            app:cornerRadius="28dp"
            app:backgroundTint="@color/md_theme_light_secondary"
            android:textColor="@android:color/white" />

    </LinearLayout>

</LinearLayout>
