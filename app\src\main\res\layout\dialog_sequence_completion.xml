<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/dialog_background">

    <!-- 标题 -->
    <TextView
        android:id="@+id/tv_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="🎉 挑战成功！"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/md_theme_light_primary"
        android:layout_gravity="center"
        android:layout_marginBottom="12dp" />

    <!-- 消息 -->
    <TextView
        android:id="@+id/tv_dialog_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="恭喜完成数字序列记忆！"
        android:textSize="16sp"
        android:textColor="@color/md_theme_light_onSurface"
        android:layout_marginBottom="12dp"
        android:gravity="center" />

    <!-- 完成时间卡片 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/md_theme_light_surfaceVariant">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_completion_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="完成时间：00:30.123"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_primary"
                android:fontFamily="monospace"
                android:gravity="center" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_back_to_main"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="返回主页"
            android:textSize="14sp"
            android:textStyle="bold"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            app:strokeColor="@color/md_theme_light_primary"
            app:strokeWidth="2dp"
            android:textColor="@color/md_theme_light_primary"
            android:backgroundTint="@android:color/white" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_play_again"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="再来一次"
            android:textSize="14sp"
            android:textStyle="bold"
            style="@style/Widget.MaterialComponents.Button"
            app:backgroundTint="@color/md_theme_light_primary"
            android:textColor="@android:color/white" />

    </LinearLayout>

</LinearLayout>
