package com.example.concentration;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

public class MainActivity extends AppCompatActivity {
    private TextView tvTodayCount;
    private TextView tvStreakDays;
    private TextView tvTotalCount;
    private CardView cardSchulte;
    private CardView cardSequenceMemory;
    private CardView cardColorTraining;
    private LinearLayout tabGames, tabRewards, tabSettings;
    private TextView tvUsername;
    private GameDataManager gameDataManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        gameDataManager = new GameDataManager(this);
        initViews();
        setupClickListeners();
        loadStatistics();
        loadUsername();

        // 设置游戏标签为选中状态
        updateTabSelection(0);

        // 预热语音系统，减少首次使用延迟
        VoiceManager.getInstance(this).warmUp();

        // 每次启动都重置测试模式为关闭状态
        resetTestMode();
    }

    private void initViews() {
        tvTodayCount = findViewById(R.id.tv_today_count);
        tvStreakDays = findViewById(R.id.tv_streak_days);
        tvTotalCount = findViewById(R.id.tv_total_count);
        cardSchulte = findViewById(R.id.card_schulte);
        cardSequenceMemory = findViewById(R.id.card_sequence_memory);
        cardColorTraining = findViewById(R.id.card_color_training);
        tabGames = findViewById(R.id.tab_games);
        tabRewards = findViewById(R.id.tab_rewards);
        tabSettings = findViewById(R.id.tab_settings);
        tvUsername = findViewById(R.id.tv_username);
    }

    private void setupClickListeners() {
        cardSchulte.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, SchulteGridActivity.class);
                startActivity(intent);
            }
        });

        cardSequenceMemory.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, MemoryGameMainActivity.class);
                startActivity(intent);
            }
        });

        cardColorTraining.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, ColorTrainingActivity.class);
                startActivity(intent);
            }
        });

        // 九九乘法表学习卡片点击事件
        CardView cardMultiplicationTable = findViewById(R.id.card_multiplication_table);
        cardMultiplicationTable.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, MultiplicationTableActivity.class);
                startActivity(intent);
            }
        });

        // 底部标签栏点击事件
        tabGames.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 游戏标签已经是当前页面，不需要跳转
                updateTabSelection(0);
            }
        });

        tabRewards.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String username = getSharedPreferences("user_prefs", MODE_PRIVATE).getString("username", "");
                if (username.isEmpty()) {
                    // 跳转到用户名设置页面
                    Intent intent = new Intent(MainActivity.this, UsernameSetupActivity.class);
                    startActivity(intent);
                } else {
                    // 直接进入奖励中心
                    try {
                        Intent intent = new Intent(MainActivity.this, RewardsActivity.class);
                        startActivity(intent);
                    } catch (Exception e) {
                        android.widget.Toast.makeText(MainActivity.this, "启动奖励中心失败，请重试", android.widget.Toast.LENGTH_SHORT).show();
                    }
                }
            }
        });

        tabSettings.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, SettingsActivity.class);
                startActivity(intent);
            }
        });
    }

    /**
     * 更新标签选中状态
     * @param selectedTab 0=游戏, 1=奖励, 2=设置
     */
    private void updateTabSelection(int selectedTab) {
        // 重置所有标签的颜色
        TextView gameText = (TextView) tabGames.getChildAt(1);
        TextView rewardText = (TextView) tabRewards.getChildAt(1);
        TextView settingText = (TextView) tabSettings.getChildAt(1);

        gameText.setTextColor(getResources().getColor(R.color.md_theme_light_onSurfaceVariant));
        rewardText.setTextColor(getResources().getColor(R.color.md_theme_light_onSurfaceVariant));
        settingText.setTextColor(getResources().getColor(R.color.md_theme_light_onSurfaceVariant));

        gameText.setTypeface(null, android.graphics.Typeface.NORMAL);
        rewardText.setTypeface(null, android.graphics.Typeface.NORMAL);
        settingText.setTypeface(null, android.graphics.Typeface.NORMAL);

        // 设置选中标签的颜色
        switch (selectedTab) {
            case 0:
                gameText.setTextColor(getResources().getColor(R.color.md_theme_light_primary));
                gameText.setTypeface(null, android.graphics.Typeface.BOLD);
                break;
            case 1:
                rewardText.setTextColor(getResources().getColor(R.color.md_theme_light_primary));
                rewardText.setTypeface(null, android.graphics.Typeface.BOLD);
                break;
            case 2:
                settingText.setTextColor(getResources().getColor(R.color.md_theme_light_primary));
                settingText.setTypeface(null, android.graphics.Typeface.BOLD);
                break;
        }
    }

    private void loadStatistics() {
        GameDataManager.GameStats stats = gameDataManager.getGameStats();
        tvTodayCount.setText(stats.todayCount + "次");
        tvStreakDays.setText(stats.streakDays + "天");
        tvTotalCount.setText(stats.totalCount + "次");
    }

    private void resetTestMode() {
        // 每次应用启动都重置测试模式为关闭状态
        android.content.SharedPreferences prefs = getSharedPreferences("app_settings", MODE_PRIVATE);
        prefs.edit().putBoolean("test_mode_enabled", false).apply();
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 每次返回主界面时刷新统计数据
        loadStatistics();
        loadUsername();
    }

    private void loadUsername() {
        String username = getSharedPreferences("user_prefs", MODE_PRIVATE).getString("username", "");
        if (!username.isEmpty()) {
            // 限制用户名最多5个汉字
            if (username.length() > 5) {
                username = username.substring(0, 5);
            }
            tvUsername.setText(username);
            tvUsername.setVisibility(View.VISIBLE);
        } else {
            tvUsername.setVisibility(View.GONE);
        }
    }

    // 旧的对话框方法已移除，现在使用专门的用户名设置页面
}