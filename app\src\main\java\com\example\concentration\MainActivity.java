package com.example.concentration;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

public class MainActivity extends AppCompatActivity {
    private TextView tvTodayCount;
    private TextView tvStreakDays;
    private TextView tvTotalCount;
    private CardView cardSchulte;
    private CardView cardSequenceMemory;
    private CardView cardColorTraining;
    private ImageButton btnSettings, btnRewards;
    private TextView tvUsername;
    private GameDataManager gameDataManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        gameDataManager = new GameDataManager(this);
        initViews();
        setupClickListeners();
        loadStatistics();
        loadUsername();

        // 预热语音系统，减少首次使用延迟
        VoiceManager.getInstance(this).warmUp();

        // 每次启动都重置测试模式为关闭状态
        resetTestMode();
    }

    private void initViews() {
        tvTodayCount = findViewById(R.id.tv_today_count);
        tvStreakDays = findViewById(R.id.tv_streak_days);
        tvTotalCount = findViewById(R.id.tv_total_count);
        cardSchulte = findViewById(R.id.card_schulte);
        cardSequenceMemory = findViewById(R.id.card_sequence_memory);
        cardColorTraining = findViewById(R.id.card_color_training);
        btnSettings = findViewById(R.id.btn_settings);
        btnRewards = findViewById(R.id.btn_rewards);
        tvUsername = findViewById(R.id.tv_username);
    }

    private void setupClickListeners() {
        cardSchulte.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, SchulteGridActivity.class);
                startActivity(intent);
            }
        });

        cardSequenceMemory.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, MemoryGameMainActivity.class);
                startActivity(intent);
            }
        });

        cardColorTraining.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, ColorTrainingActivity.class);
                startActivity(intent);
            }
        });

        // 九九乘法表学习卡片点击事件
        CardView cardMultiplicationTable = findViewById(R.id.card_multiplication_table);
        cardMultiplicationTable.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, MultiplicationTableActivity.class);
                startActivity(intent);
            }
        });

        // 设置按钮点击事件
        btnSettings.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, SettingsActivity.class);
                startActivity(intent);
            }
        });

        // 奖励中心按钮点击事件
        btnRewards.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String username = getSharedPreferences("user_prefs", MODE_PRIVATE).getString("username", "");
                if (username.isEmpty()) {
                    showUsernameInputDialog();
                } else {
                    Intent intent = new Intent(MainActivity.this, RewardsActivity.class);
                    startActivity(intent);
                }
            }
        });
    }

    private void loadStatistics() {
        GameDataManager.GameStats stats = gameDataManager.getGameStats();
        tvTodayCount.setText(stats.todayCount + "次");
        tvStreakDays.setText(stats.streakDays + "天");
        tvTotalCount.setText(stats.totalCount + "次");
    }

    private void resetTestMode() {
        // 每次应用启动都重置测试模式为关闭状态
        android.content.SharedPreferences prefs = getSharedPreferences("app_settings", MODE_PRIVATE);
        prefs.edit().putBoolean("test_mode_enabled", false).apply();
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 每次返回主界面时刷新统计数据
        loadStatistics();
        loadUsername();
    }

    private void loadUsername() {
        String username = getSharedPreferences("user_prefs", MODE_PRIVATE).getString("username", "");
        if (!username.isEmpty()) {
            tvUsername.setText(username);
            tvUsername.setVisibility(View.VISIBLE);
        } else {
            tvUsername.setVisibility(View.GONE);
        }
    }

    private void showUsernameInputDialog() {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("🎮 欢迎来到疯狂大脑！");
        builder.setMessage("请输入您的名字，开始您的专注力训练之旅：");

        final android.widget.EditText input = new android.widget.EditText(this);
        input.setHint("请输入您的名字");
        input.setInputType(android.text.InputType.TYPE_CLASS_TEXT);
        builder.setView(input);

        builder.setPositiveButton("开始训练", (dialog, which) -> {
            String username = input.getText().toString().trim();
            if (!username.isEmpty()) {
                // 保存用户名
                getSharedPreferences("user_prefs", MODE_PRIVATE)
                    .edit()
                    .putString("username", username)
                    .apply();

                // 更新显示
                loadUsername();

                // 进入奖励中心
                Intent intent = new Intent(MainActivity.this, RewardsActivity.class);
                startActivity(intent);
            } else {
                android.widget.Toast.makeText(this, "请输入您的名字", android.widget.Toast.LENGTH_SHORT).show();
            }
        });

        builder.setNegativeButton("稍后再说", (dialog, which) -> dialog.cancel());
        builder.setCancelable(false);
        builder.show();
    }
}