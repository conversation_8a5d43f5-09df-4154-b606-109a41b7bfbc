{"logs": [{"outputFile": "com.example.concentration.app-mergeDebugResources-48:/values-sw480dp-v13/values-sw480dp-v13.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\369091b8ae125b1df315664f32e06581\\transformed\\jetified-anythink_core\\res\\values-sw480dp-v13\\values-sw480dp-v13.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "69", "endOffsets": "120"}, "to": {"startLines": "5", "startColumns": "4", "startOffsets": "264", "endColumns": "69", "endOffsets": "329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\33a65179fb977c89f954a242644f692a\\transformed\\jetified-anythink_china_core\\res\\values-sw480dp-v13\\values-sw480dp-v13.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,121,192", "endColumns": "65,70,71", "endOffsets": "116,187,259"}}]}]}