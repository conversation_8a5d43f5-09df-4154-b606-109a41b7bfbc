<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\专注力训练\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\专注力训练\app\src\main\res"><file name="animal_background" path="C:\专注力训练\app\src\main\res\drawable\animal_background.xml" qualifiers="" type="drawable"/><file name="animal_game_background" path="C:\专注力训练\app\src\main\res\drawable\animal_game_background.xml" qualifiers="" type="drawable"/><file name="cartoon_background" path="C:\专注力训练\app\src\main\res\drawable\cartoon_background.xml" qualifiers="" type="drawable"/><file name="cartoon_game_background" path="C:\专注力训练\app\src\main\res\drawable\cartoon_game_background.xml" qualifiers="" type="drawable"/><file name="clean_game_background" path="C:\专注力训练\app\src\main\res\drawable\clean_game_background.xml" qualifiers="" type="drawable"/><file name="clickable_background" path="C:\专注力训练\app\src\main\res\drawable\clickable_background.xml" qualifiers="" type="drawable"/><file name="countdown_background" path="C:\专注力训练\app\src\main\res\drawable\countdown_background.xml" qualifiers="" type="drawable"/><file name="countdown_circle_background" path="C:\专注力训练\app\src\main\res\drawable\countdown_circle_background.xml" qualifiers="" type="drawable"/><file name="countdown_gradient_background" path="C:\专注力训练\app\src\main\res\drawable\countdown_gradient_background.xml" qualifiers="" type="drawable"/><file name="default_background" path="C:\专注力训练\app\src\main\res\drawable\default_background.xml" qualifiers="" type="drawable"/><file name="default_game_background" path="C:\专注力训练\app\src\main\res\drawable\default_game_background.xml" qualifiers="" type="drawable"/><file name="dialog_background" path="C:\专注力训练\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="difficulty_background" path="C:\专注力训练\app\src\main\res\drawable\difficulty_background.xml" qualifiers="" type="drawable"/><file name="game_cell_background" path="C:\专注力训练\app\src\main\res\drawable\game_cell_background.xml" qualifiers="" type="drawable"/><file name="gradient_background" path="C:\专注力训练\app\src\main\res\drawable\gradient_background.xml" qualifiers="" type="drawable"/><file name="gradient_background_secondary" path="C:\专注力训练\app\src\main\res\drawable\gradient_background_secondary.xml" qualifiers="" type="drawable"/><file name="gradient_gold" path="C:\专注力训练\app\src\main\res\drawable\gradient_gold.xml" qualifiers="" type="drawable"/><file name="gradient_light_bg" path="C:\专注力训练\app\src\main\res\drawable\gradient_light_bg.xml" qualifiers="" type="drawable"/><file name="gradient_multiplication_bg" path="C:\专注力训练\app\src\main\res\drawable\gradient_multiplication_bg.xml" qualifiers="" type="drawable"/><file name="gradient_number_bg" path="C:\专注力训练\app\src\main\res\drawable\gradient_number_bg.xml" qualifiers="" type="drawable"/><file name="gradient_stage1_bg" path="C:\专注力训练\app\src\main\res\drawable\gradient_stage1_bg.xml" qualifiers="" type="drawable"/><file name="gradient_stage2_bg" path="C:\专注力训练\app\src\main\res\drawable\gradient_stage2_bg.xml" qualifiers="" type="drawable"/><file name="gradient_stage3_bg" path="C:\专注力训练\app\src\main\res\drawable\gradient_stage3_bg.xml" qualifiers="" type="drawable"/><file name="grid_cell_background" path="C:\专注力训练\app\src\main\res\drawable\grid_cell_background.xml" qualifiers="" type="drawable"/><file name="grid_cell_cartoon" path="C:\专注力训练\app\src\main\res\drawable\grid_cell_cartoon.xml" qualifiers="" type="drawable"/><file name="grid_cell_clicked" path="C:\专注力训练\app\src\main\res\drawable\grid_cell_clicked.xml" qualifiers="" type="drawable"/><file name="grid_cell_gradient" path="C:\专注力训练\app\src\main\res\drawable\grid_cell_gradient.xml" qualifiers="" type="drawable"/><file name="grid_cell_modern" path="C:\专注力训练\app\src\main\res\drawable\grid_cell_modern.xml" qualifiers="" type="drawable"/><file name="grid_cell_space" path="C:\专注力训练\app\src\main\res\drawable\grid_cell_space.xml" qualifiers="" type="drawable"/><file name="grid_cell_wrong" path="C:\专注力训练\app\src\main\res\drawable\grid_cell_wrong.xml" qualifiers="" type="drawable"/><file name="grid_container_background" path="C:\专注力训练\app\src\main\res\drawable\grid_container_background.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="C:\专注力训练\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_brain_launcher" path="C:\专注力训练\app\src\main\res\drawable\ic_brain_launcher.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\专注力训练\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="C:\专注力训练\app\src\main\res\drawable\ic_pause.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\专注力训练\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_skip_next" path="C:\专注力训练\app\src\main\res\drawable\ic_skip_next.xml" qualifiers="" type="drawable"/><file name="ic_trophy" path="C:\专注力训练\app\src\main\res\drawable\ic_trophy.xml" qualifiers="" type="drawable"/><file name="light_gradient_background" path="C:\专注力训练\app\src\main\res\drawable\light_gradient_background.xml" qualifiers="" type="drawable"/><file name="modern_button_background" path="C:\专注力训练\app\src\main\res\drawable\modern_button_background.xml" qualifiers="" type="drawable"/><file name="modern_card_background" path="C:\专注力训练\app\src\main\res\drawable\modern_card_background.xml" qualifiers="" type="drawable"/><file name="number_display_background" path="C:\专注力训练\app\src\main\res\drawable\number_display_background.xml" qualifiers="" type="drawable"/><file name="number_highlight_background" path="C:\专注力训练\app\src\main\res\drawable\number_highlight_background.xml" qualifiers="" type="drawable"/><file name="space_background" path="C:\专注力训练\app\src\main\res\drawable\space_background.xml" qualifiers="" type="drawable"/><file name="space_game_background" path="C:\专注力训练\app\src\main\res\drawable\space_game_background.xml" qualifiers="" type="drawable"/><file name="status_background" path="C:\专注力训练\app\src\main\res\drawable\status_background.xml" qualifiers="" type="drawable"/><file name="target_background" path="C:\专注力训练\app\src\main\res\drawable\target_background.xml" qualifiers="" type="drawable"/><file name="timer_background" path="C:\专注力训练\app\src\main\res\drawable\timer_background.xml" qualifiers="" type="drawable"/><file name="timer_background_light" path="C:\专注力训练\app\src\main\res\drawable\timer_background_light.xml" qualifiers="" type="drawable"/><file name="activity_celebration" path="C:\专注力训练\app\src\main\res\layout\activity_celebration.xml" qualifiers="" type="layout"/><file name="activity_color_game" path="C:\专注力训练\app\src\main\res\layout\activity_color_game.xml" qualifiers="" type="layout"/><file name="activity_color_training" path="C:\专注力训练\app\src\main\res\layout\activity_color_training.xml" qualifiers="" type="layout"/><file name="activity_concept_animation_demo" path="C:\专注力训练\app\src\main\res\layout\activity_concept_animation_demo.xml" qualifiers="" type="layout"/><file name="activity_concept_interactive_count" path="C:\专注力训练\app\src\main\res\layout\activity_concept_interactive_count.xml" qualifiers="" type="layout"/><file name="activity_concept_test_game" path="C:\专注力训练\app\src\main\res\layout\activity_concept_test_game.xml" qualifiers="" type="layout"/><file name="activity_concept_visual_demo" path="C:\专注力训练\app\src\main\res\layout\activity_concept_visual_demo.xml" qualifiers="" type="layout"/><file name="activity_countdown" path="C:\专注力训练\app\src\main\res\layout\activity_countdown.xml" qualifiers="" type="layout"/><file name="activity_game" path="C:\专注力训练\app\src\main\res\layout\activity_game.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\专注力训练\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_memory_card_game" path="C:\专注力训练\app\src\main\res\layout\activity_memory_card_game.xml" qualifiers="" type="layout"/><file name="activity_memory_game" path="C:\专注力训练\app\src\main\res\layout\activity_memory_game.xml" qualifiers="" type="layout"/><file name="activity_memory_game_main" path="C:\专注力训练\app\src\main\res\layout\activity_memory_game_main.xml" qualifiers="" type="layout"/><file name="activity_multiplication_challenge" path="C:\专注力训练\app\src\main\res\layout\activity_multiplication_challenge.xml" qualifiers="" type="layout"/><file name="activity_multiplication_concept" path="C:\专注力训练\app\src\main\res\layout\activity_multiplication_concept.xml" qualifiers="" type="layout"/><file name="activity_multiplication_number" path="C:\专注力训练\app\src\main\res\layout\activity_multiplication_number.xml" qualifiers="" type="layout"/><file name="activity_multiplication_table" path="C:\专注力训练\app\src\main\res\layout\activity_multiplication_table.xml" qualifiers="" type="layout"/><file name="activity_number_learning" path="C:\专注力训练\app\src\main\res\layout\activity_number_learning.xml" qualifiers="" type="layout"/><file name="activity_puzzle_challenge" path="C:\专注力训练\app\src\main\res\layout\activity_puzzle_challenge.xml" qualifiers="" type="layout"/><file name="activity_rewards" path="C:\专注力训练\app\src\main\res\layout\activity_rewards.xml" qualifiers="" type="layout"/><file name="activity_rhythm_learning" path="C:\专注力训练\app\src\main\res\layout\activity_rhythm_learning.xml" qualifiers="" type="layout"/><file name="activity_schulte_grid" path="C:\专注力训练\app\src\main\res\layout\activity_schulte_grid.xml" qualifiers="" type="layout"/><file name="activity_settings" path="C:\专注力训练\app\src\main\res\layout\activity_settings.xml" qualifiers="" type="layout"/><file name="activity_splash" path="C:\专注力训练\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="activity_theme_selection" path="C:\专注力训练\app\src\main\res\layout\activity_theme_selection.xml" qualifiers="" type="layout"/><file name="activity_tutorial" path="C:\专注力训练\app\src\main\res\layout\activity_tutorial.xml" qualifiers="" type="layout"/><file name="activity_username_setup" path="C:\专注力训练\app\src\main\res\layout\activity_username_setup.xml" qualifiers="" type="layout"/><file name="dialog_game_completion" path="C:\专注力训练\app\src\main\res\layout\dialog_game_completion.xml" qualifiers="" type="layout"/><file name="dialog_sequence_completion" path="C:\专注力训练\app\src\main\res\layout\dialog_sequence_completion.xml" qualifiers="" type="layout"/><file name="item_achievement" path="C:\专注力训练\app\src\main\res\layout\item_achievement.xml" qualifiers="" type="layout"/><file name="item_challenge_answer" path="C:\专注力训练\app\src\main\res\layout\item_challenge_answer.xml" qualifiers="" type="layout"/><file name="item_game_record" path="C:\专注力训练\app\src\main\res\layout\item_game_record.xml" qualifiers="" type="layout"/><file name="item_memory_card" path="C:\专注力训练\app\src\main\res\layout\item_memory_card.xml" qualifiers="" type="layout"/><file name="item_number_selection" path="C:\专注力训练\app\src\main\res\layout\item_number_selection.xml" qualifiers="" type="layout"/><file name="item_tutorial_step" path="C:\专注力训练\app\src\main\res\layout\item_tutorial_step.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\专注力训练\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\专注力训练\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\专注力训练\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\专注力训练\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\专注力训练\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\专注力训练\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary_color">#2196F3</color><color name="primary_dark">#1976D2</color><color name="primary_light">#BBDEFB</color><color name="secondary_color">#FF9800</color><color name="accent_color">#FF5722</color><color name="accent_light">#FFCCBC</color><color name="success_color">#4CAF50</color><color name="success_light">#C8E6C9</color><color name="warning_color">#FF9800</color><color name="warning_light">#FFE0B2</color><color name="error_color">#F44336</color><color name="error_light">#FFCDD2</color><color name="text_primary">#333333</color><color name="text_secondary">#666666</color><color name="text_hint">#888888</color><color name="text_disabled">#CCCCCC</color><color name="background_primary">#FFFFFF</color><color name="background_secondary">#F5F5F5</color><color name="background_card">#FFFFFF</color><color name="divider_color">#E0E0E0</color><color name="grid_cell_normal">#FFFFFF</color><color name="grid_cell_pressed">#E3F2FD</color><color name="grid_cell_correct">#C8E6C9</color><color name="grid_cell_wrong">#FFCDD2</color><color name="grid_border">#DDDDDD</color><color name="overlay_background">#80000000</color><color name="md_theme_light_primary">#6750A4</color><color name="md_theme_light_onPrimary">#FFFFFF</color><color name="md_theme_light_primaryContainer">#EADDFF</color><color name="md_theme_light_onPrimaryContainer">#21005D</color><color name="md_theme_light_secondary">#625B71</color><color name="md_theme_light_onSecondary">#FFFFFF</color><color name="md_theme_light_secondaryContainer">#E8DEF8</color><color name="md_theme_light_onSecondaryContainer">#1D192B</color><color name="md_theme_light_surface">#FFFBFE</color><color name="md_theme_light_onSurface">#1C1B1F</color><color name="md_theme_light_surfaceVariant">#E7E0EC</color><color name="md_theme_light_onSurfaceVariant">#49454F</color><color name="md_theme_light_background">#FFFBFE</color><color name="md_theme_light_onBackground">#1C1B1F</color><color name="md_theme_light_outline">#79747E</color><color name="md_theme_light_tertiary">#7D5260</color><color name="md_theme_light_onTertiary">#FFFFFF</color><color name="md_theme_light_tertiaryContainer">#FFD8E4</color><color name="md_theme_light_onTertiaryContainer">#31111D</color><color name="gradient_start">#667eea</color><color name="gradient_end">#764ba2</color><color name="gradient_start_secondary">#f093fb</color><color name="gradient_end_secondary">#f5576c</color><color name="gradient_start_tertiary">#a8edea</color><color name="gradient_end_tertiary">#fed6e3</color><color name="theme_animal_primary">#4CAF50</color><color name="theme_animal_secondary">#8BC34A</color><color name="theme_animal_background">#E8F5E8</color><color name="theme_animal_accent">#FF9800</color><color name="theme_space_primary">#3F51B5</color><color name="theme_space_secondary">#9C27B0</color><color name="theme_space_background">#1A1A2E</color><color name="theme_space_accent">#00BCD4</color><color name="theme_cartoon_primary">#FF9800</color><color name="theme_cartoon_secondary">#FF5722</color><color name="theme_cartoon_background">#FFF3E0</color><color name="theme_cartoon_accent">#E91E63</color><color name="grid_correct">#4CAF50</color><color name="grid_wrong">#F44336</color><color name="grid_selected">#2196F3</color><color name="grid_hover">#E3F2FD</color><color name="star_color">#FFD700</color><color name="particle_color">#FF6B6B</color><color name="celebration_primary">#FF6B35</color><color name="celebration_secondary">#F7931E</color><color name="black_overlay">#80000000</color></file><file path="C:\专注力训练\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">疯狂大脑</string></file><file path="C:\专注力训练\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="colorAccent">@color/accent_color</item>
        <item name="android:windowBackground">@color/background_secondary</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
    </style><style name="SchulteGridCell">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_margin">2dp</item>
        <item name="android:background">@drawable/grid_cell_background</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style><style name="CardTitle">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style><style name="CardDescription">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:layout_marginBottom">12dp</item>
    </style><style name="StatNumber">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style><style name="StatLabel">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/text_hint</item>
    </style><style name="GameTimer">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/primary_color</item>
    </style><style name="NextNumber">
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/accent_color</item>
    </style><style name="DifficultyText">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/success_color</item>
    </style><style name="CustomDialogTheme" parent="Theme.MaterialComponents.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
    </style></file><file name="anythink_bk_gdt_file_path" path="C:\专注力训练\app\src\main\res\xml\anythink_bk_gdt_file_path.xml" qualifiers="" type="xml"/><file name="anythink_bk_sigmob_file_path" path="C:\专注力训练\app\src\main\res\xml\anythink_bk_sigmob_file_path.xml" qualifiers="" type="xml"/><file name="anythink_bk_tt_file_path" path="C:\专注力训练\app\src\main\res\xml\anythink_bk_tt_file_path.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\专注力训练\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\专注力训练\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\专注力训练\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\专注力训练\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>