1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.concentration"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\专注力训练\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\专注力训练\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\专注力训练\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\专注力训练\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\专注力训练\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\专注力训练\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- 可选权限 动态权限 -->
17    <!-- 获取设备标识IMEI。用于标识用户 -->
18    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
18-->C:\专注力训练\app\src\main\AndroidManifest.xml:12:5-75
18-->C:\专注力训练\app\src\main\AndroidManifest.xml:12:22-72
19    <!-- 读写存储权限 -->
20    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
20-->C:\专注力训练\app\src\main\AndroidManifest.xml:14:5-80
20-->C:\专注力训练\app\src\main\AndroidManifest.xml:14:22-77
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->C:\专注力训练\app\src\main\AndroidManifest.xml:15:5-81
21-->C:\专注力训练\app\src\main\AndroidManifest.xml:15:22-78
22    <!-- 定位权限 -->
23    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
23-->C:\专注力训练\app\src\main\AndroidManifest.xml:17:5-81
23-->C:\专注力训练\app\src\main\AndroidManifest.xml:17:22-78
24    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
24-->C:\专注力训练\app\src\main\AndroidManifest.xml:18:5-79
24-->C:\专注力训练\app\src\main\AndroidManifest.xml:18:22-76
25    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> <!-- 可选权限 -->
25-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:13:5-83
25-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:13:22-80
26    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- suppress DeprecatedClassUsageInspection -->
26-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:15:5-76
26-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:15:22-73
27    <uses-permission android:name="android.permission.GET_TASKS" /> <!-- 如果有视频相关的广告且使用textureView播放，请务必添加，否则黑屏 -->
27-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:17:5-68
27-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:17:22-65
28    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- MTG将通过此权限在Android R系统上判定广告对应的应用是否在用户的app上安装，避免投放错误的广告，以此提高用户的广告体验。 -->
28-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:25:5-68
28-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:25:22-65
29    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
29-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:28:5-77
29-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:28:22-74
30    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
30-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:14:5-79
30-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:14:22-76
31
32    <queries>
32-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:16:5-22:15
33        <intent>
33-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:17:9-21:18
34            <action android:name="android.intent.action.MAIN" />
34-->C:\专注力训练\app\src\main\AndroidManifest.xml:76:17-69
34-->C:\专注力训练\app\src\main\AndroidManifest.xml:76:25-66
35
36            <category android:name="android.intent.category.LAUNCHER" />
36-->C:\专注力训练\app\src\main\AndroidManifest.xml:77:17-77
36-->C:\专注力训练\app\src\main\AndroidManifest.xml:77:27-74
37        </intent>
38        <intent>
38-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:13:9-19:18
39            <action android:name="android.intent.action.VIEW" />
39-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:14:13-64
39-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:14:21-62
40
41            <category android:name="android.intent.category.BROWSABLE" />
41-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:16:13-73
41-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:16:23-71
42
43            <data android:scheme="https" />
43-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:18:13-43
43-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:18:19-41
44        </intent>
45    </queries>
46
47    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
47-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:8:5-79
47-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:8:22-76
48    <uses-permission android:name="android.permission.REORDER_TASKS" />
48-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:24:5-72
48-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:24:22-69
49    <uses-permission android:name="android.permission.VIBRATE" /> <!-- 动态壁纸权限 -->
49-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:25:5-66
49-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:25:22-63
50    <uses-permission android:name="android.permission.SET_WALLPAPER" />
50-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:20:5-72
50-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:20:22-69
51    <uses-permission android:name="android.permission.BLUETOOTH" />
51-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:24:5-68
51-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:24:22-65
52    <uses-permission android:name="com.asus.msa.SupplementaryDID.ACCESS" />
52-->[oaid_sdk_1.0.25.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01bc7fca446bdde30d72de23c77cdbfa\transformed\jetified-oaid_sdk_1.0.25\AndroidManifest.xml:13:5-76
52-->[oaid_sdk_1.0.25.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01bc7fca446bdde30d72de23c77cdbfa\transformed\jetified-oaid_sdk_1.0.25\AndroidManifest.xml:13:22-73
53    <uses-permission android:name="freemme.permission.msa" />
53-->[oaid_sdk_1.0.25.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01bc7fca446bdde30d72de23c77cdbfa\transformed\jetified-oaid_sdk_1.0.25\AndroidManifest.xml:15:5-62
53-->[oaid_sdk_1.0.25.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01bc7fca446bdde30d72de23c77cdbfa\transformed\jetified-oaid_sdk_1.0.25\AndroidManifest.xml:15:22-59
54    <uses-permission android:name="com.example.concentration.openadsdk.permission.TT_PANGOLIN" />
54-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:7:5-88
54-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:7:22-86
55
56    <permission
56-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:10:5-119
57        android:name="com.example.concentration.openadsdk.permission.TT_PANGOLIN"
57-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:10:17-81
58        android:protectionLevel="signature" />
58-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:10:82-117
59    <permission
59-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
60        android:name="com.example.concentration.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
60-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
61        android:protectionLevel="signature" />
61-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
62
63    <uses-permission android:name="com.example.concentration.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
63-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
63-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
64
65    <application
65-->C:\专注力训练\app\src\main\AndroidManifest.xml:20:5-207:19
66        android:name="com.example.concentration.AdApplication"
66-->C:\专注力训练\app\src\main\AndroidManifest.xml:21:9-38
67        android:allowBackup="false"
67-->C:\专注力训练\app\src\main\AndroidManifest.xml:22:9-36
68        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
68-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
69        android:debuggable="true"
70        android:extractNativeLibs="false"
71        android:icon="@mipmap/ic_launcher"
71-->C:\专注力训练\app\src\main\AndroidManifest.xml:23:9-43
72        android:label="@string/app_name"
72-->C:\专注力训练\app\src\main\AndroidManifest.xml:24:9-41
73        android:requestLegacyExternalStorage="false"
73-->C:\专注力训练\app\src\main\AndroidManifest.xml:28:9-53
74        android:roundIcon="@mipmap/ic_launcher"
74-->C:\专注力训练\app\src\main\AndroidManifest.xml:25:9-48
75        android:supportsRtl="true"
75-->C:\专注力训练\app\src\main\AndroidManifest.xml:26:9-35
76        android:theme="@style/AppTheme"
76-->C:\专注力训练\app\src\main\AndroidManifest.xml:27:9-40
77        android:usesCleartextTraffic="true" >
77-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:16:18-53
78        <uses-library
78-->C:\专注力训练\app\src\main\AndroidManifest.xml:30:9-32:40
79            android:name="org.apache.http.legacy"
79-->C:\专注力训练\app\src\main\AndroidManifest.xml:31:13-50
80            android:required="false" />
80-->C:\专注力训练\app\src\main\AndroidManifest.xml:32:13-37
81
82        <provider
83            android:name="com.bytedance.sdk.openadsdk.multipro.TTMultiProvider"
83-->C:\专注力训练\app\src\main\AndroidManifest.xml:35:13-80
84            android:authorities="com.example.concentration.TTMultiProvider"
84-->C:\专注力训练\app\src\main\AndroidManifest.xml:36:13-67
85            android:exported="false" />
85-->C:\专注力训练\app\src\main\AndroidManifest.xml:37:13-37
86        <provider
87            android:name="com.bytedance.sdk.openadsdk.TTFileProvider"
87-->C:\专注力训练\app\src\main\AndroidManifest.xml:39:13-70
88            android:authorities="com.example.concentration.TTFileProvider"
88-->C:\专注力训练\app\src\main\AndroidManifest.xml:40:13-66
89            android:exported="false"
89-->C:\专注力训练\app\src\main\AndroidManifest.xml:41:13-37
90            android:grantUriPermissions="true" >
90-->C:\专注力训练\app\src\main\AndroidManifest.xml:42:13-47
91            <meta-data
91-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
92                android:name="android.support.FILE_PROVIDER_PATHS"
92-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
93                android:resource="@xml/anythink_bk_tt_file_path" />
93-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
94        </provider>
95        <provider
96            android:name="com.qq.e.comm.GDTFileProvider"
96-->C:\专注力训练\app\src\main\AndroidManifest.xml:49:13-57
97            android:authorities="com.example.concentration.gdt.fileprovider"
97-->C:\专注力训练\app\src\main\AndroidManifest.xml:50:13-68
98            android:exported="false"
98-->C:\专注力训练\app\src\main\AndroidManifest.xml:51:13-37
99            android:grantUriPermissions="true" >
99-->C:\专注力训练\app\src\main\AndroidManifest.xml:52:13-47
100            <meta-data
100-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
101                android:name="android.support.FILE_PROVIDER_PATHS"
101-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
102                android:resource="@xml/anythink_bk_gdt_file_path" />
102-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
103        </provider>
104        <provider
105            android:name="com.sigmob.sdk.SigmobFileV4Provider"
105-->C:\专注力训练\app\src\main\AndroidManifest.xml:59:13-63
106            android:authorities="com.example.concentration.sigprovider"
106-->C:\专注力训练\app\src\main\AndroidManifest.xml:60:13-63
107            android:exported="false"
107-->C:\专注力训练\app\src\main\AndroidManifest.xml:61:13-37
108            android:grantUriPermissions="true"
108-->C:\专注力训练\app\src\main\AndroidManifest.xml:63:13-47
109            android:initOrder="200" >
109-->C:\专注力训练\app\src\main\AndroidManifest.xml:62:13-36
110            <meta-data
110-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
111                android:name="android.support.FILE_PROVIDER_PATHS"
111-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
112                android:resource="@xml/anythink_bk_sigmob_file_path" />
112-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
113        </provider>
114
115        <!-- 开屏广告Activity -->
116        <activity
116-->C:\专注力训练\app\src\main\AndroidManifest.xml:70:9-79:20
117            android:name="com.example.concentration.SplashActivity"
117-->C:\专注力训练\app\src\main\AndroidManifest.xml:71:13-43
118            android:exported="true"
118-->C:\专注力训练\app\src\main\AndroidManifest.xml:74:13-36
119            android:screenOrientation="portrait"
119-->C:\专注力训练\app\src\main\AndroidManifest.xml:72:13-49
120            android:theme="@style/AppTheme" >
120-->C:\专注力训练\app\src\main\AndroidManifest.xml:73:13-44
121            <intent-filter>
121-->C:\专注力训练\app\src\main\AndroidManifest.xml:75:13-78:29
122                <action android:name="android.intent.action.MAIN" />
122-->C:\专注力训练\app\src\main\AndroidManifest.xml:76:17-69
122-->C:\专注力训练\app\src\main\AndroidManifest.xml:76:25-66
123
124                <category android:name="android.intent.category.LAUNCHER" />
124-->C:\专注力训练\app\src\main\AndroidManifest.xml:77:17-77
124-->C:\专注力训练\app\src\main\AndroidManifest.xml:77:27-74
125            </intent-filter>
126        </activity>
127        <activity
127-->C:\专注力训练\app\src\main\AndroidManifest.xml:81:9-84:40
128            android:name="com.example.concentration.MainActivity"
128-->C:\专注力训练\app\src\main\AndroidManifest.xml:81:19-47
129            android:exported="false"
129-->C:\专注力训练\app\src\main\AndroidManifest.xml:84:13-37
130            android:screenOrientation="portrait"
130-->C:\专注力训练\app\src\main\AndroidManifest.xml:82:13-49
131            android:theme="@style/AppTheme" />
131-->C:\专注力训练\app\src\main\AndroidManifest.xml:83:13-44
132        <activity
132-->C:\专注力训练\app\src\main\AndroidManifest.xml:86:9-90:40
133            android:name="com.example.concentration.SchulteGridActivity"
133-->C:\专注力训练\app\src\main\AndroidManifest.xml:87:13-48
134            android:exported="false"
134-->C:\专注力训练\app\src\main\AndroidManifest.xml:90:13-37
135            android:screenOrientation="portrait"
135-->C:\专注力训练\app\src\main\AndroidManifest.xml:88:13-49
136            android:theme="@style/AppTheme" />
136-->C:\专注力训练\app\src\main\AndroidManifest.xml:89:13-44
137        <activity
137-->C:\专注力训练\app\src\main\AndroidManifest.xml:91:9-95:40
138            android:name="com.example.concentration.ThemeSelectionActivity"
138-->C:\专注力训练\app\src\main\AndroidManifest.xml:92:13-51
139            android:exported="false"
139-->C:\专注力训练\app\src\main\AndroidManifest.xml:95:13-37
140            android:screenOrientation="portrait"
140-->C:\专注力训练\app\src\main\AndroidManifest.xml:93:13-49
141            android:theme="@style/AppTheme" />
141-->C:\专注力训练\app\src\main\AndroidManifest.xml:94:13-44
142        <activity
142-->C:\专注力训练\app\src\main\AndroidManifest.xml:96:9-100:40
143            android:name="com.example.concentration.CountdownActivity"
143-->C:\专注力训练\app\src\main\AndroidManifest.xml:97:13-46
144            android:exported="false"
144-->C:\专注力训练\app\src\main\AndroidManifest.xml:100:13-37
145            android:screenOrientation="portrait"
145-->C:\专注力训练\app\src\main\AndroidManifest.xml:98:13-49
146            android:theme="@style/AppTheme" />
146-->C:\专注力训练\app\src\main\AndroidManifest.xml:99:13-44
147        <activity
147-->C:\专注力训练\app\src\main\AndroidManifest.xml:101:9-105:40
148            android:name="com.example.concentration.GameActivity"
148-->C:\专注力训练\app\src\main\AndroidManifest.xml:102:13-41
149            android:exported="false"
149-->C:\专注力训练\app\src\main\AndroidManifest.xml:105:13-37
150            android:screenOrientation="portrait"
150-->C:\专注力训练\app\src\main\AndroidManifest.xml:103:13-49
151            android:theme="@style/AppTheme" />
151-->C:\专注力训练\app\src\main\AndroidManifest.xml:104:13-44
152        <activity
152-->C:\专注力训练\app\src\main\AndroidManifest.xml:107:9-111:40
153            android:name="com.example.concentration.ColorTrainingActivity"
153-->C:\专注力训练\app\src\main\AndroidManifest.xml:108:13-50
154            android:exported="false"
154-->C:\专注力训练\app\src\main\AndroidManifest.xml:111:13-37
155            android:screenOrientation="portrait"
155-->C:\专注力训练\app\src\main\AndroidManifest.xml:109:13-49
156            android:theme="@style/AppTheme" />
156-->C:\专注力训练\app\src\main\AndroidManifest.xml:110:13-44
157        <activity
157-->C:\专注力训练\app\src\main\AndroidManifest.xml:112:9-116:40
158            android:name="com.example.concentration.ColorGameActivity"
158-->C:\专注力训练\app\src\main\AndroidManifest.xml:113:13-46
159            android:exported="false"
159-->C:\专注力训练\app\src\main\AndroidManifest.xml:116:13-37
160            android:screenOrientation="portrait"
160-->C:\专注力训练\app\src\main\AndroidManifest.xml:114:13-49
161            android:theme="@style/AppTheme" />
161-->C:\专注力训练\app\src\main\AndroidManifest.xml:115:13-44
162        <activity
162-->C:\专注力训练\app\src\main\AndroidManifest.xml:117:9-121:40
163            android:name="com.example.concentration.MemoryGameMainActivity"
163-->C:\专注力训练\app\src\main\AndroidManifest.xml:118:13-51
164            android:exported="false"
164-->C:\专注力训练\app\src\main\AndroidManifest.xml:121:13-37
165            android:screenOrientation="portrait"
165-->C:\专注力训练\app\src\main\AndroidManifest.xml:119:13-49
166            android:theme="@style/AppTheme" />
166-->C:\专注力训练\app\src\main\AndroidManifest.xml:120:13-44
167        <activity
167-->C:\专注力训练\app\src\main\AndroidManifest.xml:122:9-126:40
168            android:name="com.example.concentration.MemoryGameActivity"
168-->C:\专注力训练\app\src\main\AndroidManifest.xml:123:13-47
169            android:exported="false"
169-->C:\专注力训练\app\src\main\AndroidManifest.xml:126:13-37
170            android:screenOrientation="portrait"
170-->C:\专注力训练\app\src\main\AndroidManifest.xml:124:13-49
171            android:theme="@style/AppTheme" />
171-->C:\专注力训练\app\src\main\AndroidManifest.xml:125:13-44
172        <activity
172-->C:\专注力训练\app\src\main\AndroidManifest.xml:127:9-131:40
173            android:name="com.example.concentration.CelebrationActivity"
173-->C:\专注力训练\app\src\main\AndroidManifest.xml:128:13-48
174            android:exported="false"
174-->C:\专注力训练\app\src\main\AndroidManifest.xml:131:13-37
175            android:screenOrientation="portrait"
175-->C:\专注力训练\app\src\main\AndroidManifest.xml:129:13-49
176            android:theme="@style/AppTheme" />
176-->C:\专注力训练\app\src\main\AndroidManifest.xml:130:13-44
177        <activity
177-->C:\专注力训练\app\src\main\AndroidManifest.xml:132:9-136:40
178            android:name="com.example.concentration.MultiplicationTableActivity"
178-->C:\专注力训练\app\src\main\AndroidManifest.xml:133:13-56
179            android:exported="false"
179-->C:\专注力训练\app\src\main\AndroidManifest.xml:136:13-37
180            android:screenOrientation="portrait"
180-->C:\专注力训练\app\src\main\AndroidManifest.xml:134:13-49
181            android:theme="@style/AppTheme" />
181-->C:\专注力训练\app\src\main\AndroidManifest.xml:135:13-44
182        <activity
182-->C:\专注力训练\app\src\main\AndroidManifest.xml:137:9-141:40
183            android:name="com.example.concentration.MultiplicationConceptActivity"
183-->C:\专注力训练\app\src\main\AndroidManifest.xml:138:13-58
184            android:exported="false"
184-->C:\专注力训练\app\src\main\AndroidManifest.xml:141:13-37
185            android:screenOrientation="portrait"
185-->C:\专注力训练\app\src\main\AndroidManifest.xml:139:13-49
186            android:theme="@style/AppTheme" />
186-->C:\专注力训练\app\src\main\AndroidManifest.xml:140:13-44
187        <activity
187-->C:\专注力训练\app\src\main\AndroidManifest.xml:142:9-146:40
188            android:name="com.example.concentration.ConceptVisualDemoActivity"
188-->C:\专注力训练\app\src\main\AndroidManifest.xml:143:13-54
189            android:exported="false"
189-->C:\专注力训练\app\src\main\AndroidManifest.xml:146:13-37
190            android:screenOrientation="portrait"
190-->C:\专注力训练\app\src\main\AndroidManifest.xml:144:13-49
191            android:theme="@style/AppTheme" />
191-->C:\专注力训练\app\src\main\AndroidManifest.xml:145:13-44
192        <activity
192-->C:\专注力训练\app\src\main\AndroidManifest.xml:147:9-151:40
193            android:name="com.example.concentration.ConceptInteractiveCountActivity"
193-->C:\专注力训练\app\src\main\AndroidManifest.xml:148:13-60
194            android:exported="false"
194-->C:\专注力训练\app\src\main\AndroidManifest.xml:151:13-37
195            android:screenOrientation="portrait"
195-->C:\专注力训练\app\src\main\AndroidManifest.xml:149:13-49
196            android:theme="@style/AppTheme" />
196-->C:\专注力训练\app\src\main\AndroidManifest.xml:150:13-44
197        <activity
197-->C:\专注力训练\app\src\main\AndroidManifest.xml:152:9-156:40
198            android:name="com.example.concentration.ConceptAnimationDemoActivity"
198-->C:\专注力训练\app\src\main\AndroidManifest.xml:153:13-57
199            android:exported="false"
199-->C:\专注力训练\app\src\main\AndroidManifest.xml:156:13-37
200            android:screenOrientation="portrait"
200-->C:\专注力训练\app\src\main\AndroidManifest.xml:154:13-49
201            android:theme="@style/AppTheme" />
201-->C:\专注力训练\app\src\main\AndroidManifest.xml:155:13-44
202        <activity
202-->C:\专注力训练\app\src\main\AndroidManifest.xml:157:9-161:40
203            android:name="com.example.concentration.ConceptTestGameActivity"
203-->C:\专注力训练\app\src\main\AndroidManifest.xml:158:13-52
204            android:exported="false"
204-->C:\专注力训练\app\src\main\AndroidManifest.xml:161:13-37
205            android:screenOrientation="portrait"
205-->C:\专注力训练\app\src\main\AndroidManifest.xml:159:13-49
206            android:theme="@style/AppTheme" />
206-->C:\专注力训练\app\src\main\AndroidManifest.xml:160:13-44
207        <activity
207-->C:\专注力训练\app\src\main\AndroidManifest.xml:162:9-166:40
208            android:name="com.example.concentration.MultiplicationNumberActivity"
208-->C:\专注力训练\app\src\main\AndroidManifest.xml:163:13-57
209            android:exported="false"
209-->C:\专注力训练\app\src\main\AndroidManifest.xml:166:13-37
210            android:screenOrientation="portrait"
210-->C:\专注力训练\app\src\main\AndroidManifest.xml:164:13-49
211            android:theme="@style/AppTheme" />
211-->C:\专注力训练\app\src\main\AndroidManifest.xml:165:13-44
212        <activity
212-->C:\专注力训练\app\src\main\AndroidManifest.xml:167:9-171:40
213            android:name="com.example.concentration.NumberLearningActivity"
213-->C:\专注力训练\app\src\main\AndroidManifest.xml:168:13-51
214            android:exported="false"
214-->C:\专注力训练\app\src\main\AndroidManifest.xml:171:13-37
215            android:screenOrientation="portrait"
215-->C:\专注力训练\app\src\main\AndroidManifest.xml:169:13-49
216            android:theme="@style/AppTheme" />
216-->C:\专注力训练\app\src\main\AndroidManifest.xml:170:13-44
217        <activity
217-->C:\专注力训练\app\src\main\AndroidManifest.xml:172:9-176:40
218            android:name="com.example.concentration.MemoryCardGameActivity"
218-->C:\专注力训练\app\src\main\AndroidManifest.xml:173:13-51
219            android:exported="false"
219-->C:\专注力训练\app\src\main\AndroidManifest.xml:176:13-37
220            android:screenOrientation="portrait"
220-->C:\专注力训练\app\src\main\AndroidManifest.xml:174:13-49
221            android:theme="@style/AppTheme" />
221-->C:\专注力训练\app\src\main\AndroidManifest.xml:175:13-44
222        <activity
222-->C:\专注力训练\app\src\main\AndroidManifest.xml:177:9-181:40
223            android:name="com.example.concentration.RhythmLearningActivity"
223-->C:\专注力训练\app\src\main\AndroidManifest.xml:178:13-51
224            android:exported="false"
224-->C:\专注力训练\app\src\main\AndroidManifest.xml:181:13-37
225            android:screenOrientation="portrait"
225-->C:\专注力训练\app\src\main\AndroidManifest.xml:179:13-49
226            android:theme="@style/AppTheme" />
226-->C:\专注力训练\app\src\main\AndroidManifest.xml:180:13-44
227        <activity
227-->C:\专注力训练\app\src\main\AndroidManifest.xml:182:9-186:40
228            android:name="com.example.concentration.PuzzleChallengeActivity"
228-->C:\专注力训练\app\src\main\AndroidManifest.xml:183:13-52
229            android:exported="false"
229-->C:\专注力训练\app\src\main\AndroidManifest.xml:186:13-37
230            android:screenOrientation="portrait"
230-->C:\专注力训练\app\src\main\AndroidManifest.xml:184:13-49
231            android:theme="@style/AppTheme" />
231-->C:\专注力训练\app\src\main\AndroidManifest.xml:185:13-44
232        <activity
232-->C:\专注力训练\app\src\main\AndroidManifest.xml:187:9-191:40
233            android:name="com.example.concentration.MultiplicationChallengeActivity"
233-->C:\专注力训练\app\src\main\AndroidManifest.xml:188:13-60
234            android:exported="false"
234-->C:\专注力训练\app\src\main\AndroidManifest.xml:191:13-37
235            android:screenOrientation="portrait"
235-->C:\专注力训练\app\src\main\AndroidManifest.xml:189:13-49
236            android:theme="@style/AppTheme" />
236-->C:\专注力训练\app\src\main\AndroidManifest.xml:190:13-44
237        <activity
237-->C:\专注力训练\app\src\main\AndroidManifest.xml:192:9-196:40
238            android:name="com.example.concentration.SettingsActivity"
238-->C:\专注力训练\app\src\main\AndroidManifest.xml:193:13-45
239            android:exported="false"
239-->C:\专注力训练\app\src\main\AndroidManifest.xml:196:13-37
240            android:screenOrientation="portrait"
240-->C:\专注力训练\app\src\main\AndroidManifest.xml:194:13-49
241            android:theme="@style/AppTheme" />
241-->C:\专注力训练\app\src\main\AndroidManifest.xml:195:13-44
242        <activity
242-->C:\专注力训练\app\src\main\AndroidManifest.xml:197:9-201:40
243            android:name="com.example.concentration.UsernameSetupActivity"
243-->C:\专注力训练\app\src\main\AndroidManifest.xml:198:13-50
244            android:exported="false"
244-->C:\专注力训练\app\src\main\AndroidManifest.xml:201:13-37
245            android:screenOrientation="portrait"
245-->C:\专注力训练\app\src\main\AndroidManifest.xml:199:13-49
246            android:theme="@style/AppTheme" />
246-->C:\专注力训练\app\src\main\AndroidManifest.xml:200:13-44
247        <activity
247-->C:\专注力训练\app\src\main\AndroidManifest.xml:202:9-206:40
248            android:name="com.example.concentration.TutorialActivity"
248-->C:\专注力训练\app\src\main\AndroidManifest.xml:203:13-45
249            android:exported="false"
249-->C:\专注力训练\app\src\main\AndroidManifest.xml:206:13-37
250            android:screenOrientation="portrait"
250-->C:\专注力训练\app\src\main\AndroidManifest.xml:204:13-49
251            android:theme="@style/AppTheme" />
251-->C:\专注力训练\app\src\main\AndroidManifest.xml:205:13-44
252        <activity
252-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:17:9-20:47
253            android:name="com.alliance.ssp.ad.activity.SAAllianceWebViewActivity"
253-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:18:13-82
254            android:configChanges="orientation|screenSize|keyboardHidden"
254-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:19:13-74
255            android:launchMode="singleTask" />
255-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:20:13-44
256        <activity
256-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:21:9-25:52
257            android:name="com.alliance.ssp.ad.activity.NMRewardVideoActivity"
257-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:22:13-78
258            android:configChanges="orientation|screenSize|keyboardHidden"
258-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:23:13-74
259            android:launchMode="singleTask"
259-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:24:13-44
260            android:screenOrientation="portrait" />
260-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:25:13-49
261        <activity
261-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:26:9-30:64
262            android:name="com.alliance.ssp.ad.activity.AppInfoViewActivity"
262-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:27:13-76
263            android:configChanges="orientation|screenSize|keyboardHidden"
263-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:28:13-74
264            android:launchMode="singleTask"
264-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:29:13-44
265            android:theme="@android:style/Theme.Translucent" />
265-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:30:13-61
266
267        <service
267-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:32:9-34:40
268            android:name="com.alliance.ssp.ad.service.YTSDKDownloadService"
268-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:33:13-76
269            android:exported="false" />
269-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:34:13-37
270
271        <activity
271-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:36:9-40:64
272            android:name="com.alliance.ssp.ad.activity.DeepLinkProxyActivity"
272-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:37:13-78
273            android:configChanges="orientation|screenSize|keyboardHidden"
273-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:38:13-74
274            android:launchMode="singleTask"
274-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:39:13-44
275            android:theme="@android:style/Theme.Translucent" />
275-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:40:13-61
276
277        <provider
277-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:42:9-50:20
278            android:name="com.alliance.ssp.ad.utils.NMSSPFileProvider"
278-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:43:13-71
279            android:authorities="com.example.concentration.NMSSPFileProvider"
279-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:44:13-69
280            android:exported="false"
280-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:45:13-37
281            android:grantUriPermissions="true" >
281-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:46:13-47
282            <meta-data
282-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
283                android:name="android.support.FILE_PROVIDER_PATHS"
283-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
284                android:resource="@xml/nmssp_file_path" />
284-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
285        </provider>
286
287        <activity
287-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:31:9-35:52
288            android:name="com.ads.admob.saas.SaasH5Activity"
288-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:32:13-61
289            android:configChanges="keyboard|orientation|screenSize"
289-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:33:13-68
290            android:exported="false"
290-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:34:13-37
291            android:screenOrientation="portrait" />
291-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:35:13-49
292        <activity
292-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:36:9-40:52
293            android:name="com.ads.admob.saas.H5Activity"
293-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:37:13-57
294            android:configChanges="keyboard|orientation|screenSize"
294-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:38:13-68
295            android:exported="false"
295-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:39:13-37
296            android:screenOrientation="portrait" />
296-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:40:13-49
297        <activity
297-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:41:9-45:52
298            android:name="com.ads.admob.saas.GameH5Activity"
298-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:42:13-61
299            android:configChanges="keyboard|orientation|screenSize"
299-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:43:13-68
300            android:exported="false"
300-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:44:13-37
301            android:screenOrientation="portrait" />
301-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:45:13-49
302        <activity
302-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:46:9-50:52
303            android:name="com.ads.admob.saas.YmActivity"
303-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:47:13-57
304            android:configChanges="keyboard|orientation|screenSize"
304-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:48:13-68
305            android:exported="false"
305-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:49:13-37
306            android:screenOrientation="portrait" />
306-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:50:13-49
307        <activity
307-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:51:9-55:52
308            android:name="com.ads.admob.saas.VideoFragmentActivity"
308-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:52:13-68
309            android:configChanges="keyboard|orientation|screenSize"
309-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:53:13-68
310            android:exported="false"
310-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:54:13-37
311            android:screenOrientation="portrait" />
311-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:55:13-49
312        <activity
312-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:18:9-21:86
313            android:name="com.anythink.china.activity.TransparentActivity"
313-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:19:13-75
314            android:exported="false"
314-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:20:13-37
315            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
315-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:21:13-83
316        <activity
316-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:22:9-25:86
317            android:name="com.anythink.dlopt.activity.ApkConfirmDialogActivity"
317-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:23:13-80
318            android:exported="false"
318-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:24:13-37
319            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
319-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:25:13-83
320
321        <provider
321-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:27:9-35:20
322            android:name="com.anythink.dlopt.common.ApkFileProvider"
322-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:28:13-69
323            android:authorities="com.example.concentration.anythink.fileProvider"
323-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:29:13-73
324            android:exported="false"
324-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:30:13-37
325            android:grantUriPermissions="true" >
325-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:31:13-47
326            <meta-data
326-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
327                android:name="android.support.FILE_PROVIDER_PATHS"
327-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
328                android:resource="@xml/anythink_file_paths" />
328-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
329        </provider>
330
331        <receiver
331-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:37:9-44:20
332            android:name="com.anythink.dlopt.common.NotificationBroadcastReceiver"
332-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:38:13-83
333            android:exported="false" >
333-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:39:13-37
334            <intent-filter>
334-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:40:13-43:29
335                <action android:name="anythink_action_notification_click" />
335-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:41:17-77
335-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:41:25-74
336                <action android:name="anythink_action_notification_cancel" />
336-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:42:17-78
336-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:42:25-75
337            </intent-filter>
338        </receiver>
339
340        <service
340-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:46:9-48:40
341            android:name="com.anythink.dlopt.common.service.ApkDownloadService"
341-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:47:13-80
342            android:exported="false" />
342-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:48:13-37
343
344        <activity
344-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:25:9-30:74
345            android:name="com.anythink.core.activity.AnyThinkGdprAuthActivity"
345-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:26:13-79
346            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
346-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:27:13-87
347            android:exported="false"
347-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:28:13-37
348            android:launchMode="singleTask"
348-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:29:13-44
349            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
349-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:30:13-71
350        <activity
350-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:31:9-36:74
351            android:name="com.anythink.basead.ui.ATLandscapeActivity"
351-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:32:13-70
352            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
352-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:33:13-87
353            android:exported="false"
353-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:34:13-37
354            android:launchMode="standard"
354-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:35:13-42
355            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
355-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:36:13-71
356        <activity
356-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:37:9-42:74
357            android:name="com.anythink.basead.ui.ATPortraitActivity"
357-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:38:13-69
358            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
358-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:39:13-87
359            android:exported="false"
359-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:40:13-37
360            android:launchMode="standard"
360-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:41:13-42
361            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
361-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:42:13-71
362        <activity
362-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:43:9-48:67
363            android:name="com.anythink.basead.ui.ATLandscapeTranslucentActivity"
363-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:44:13-81
364            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
364-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:45:13-87
365            android:exported="false"
365-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:46:13-37
366            android:launchMode="standard"
366-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:47:13-42
367            android:theme="@style/anythink_myoffer_half_screen" />
367-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:48:13-64
368        <activity
368-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:49:9-54:67
369            android:name="com.anythink.basead.ui.ATPortraitTranslucentActivity"
369-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:50:13-80
370            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
370-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:51:13-87
371            android:exported="false"
371-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:52:13-37
372            android:launchMode="standard"
372-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:53:13-42
373            android:theme="@style/anythink_myoffer_half_screen" />
373-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:54:13-64
374        <activity
374-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:55:9-61:58
375            android:name="com.anythink.core.basead.ui.web.WebLandPageActivity"
375-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:56:13-79
376            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
376-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:57:13-87
377            android:exported="false"
377-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:58:13-37
378            android:launchMode="singleTop"
378-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:59:13-43
379            android:theme="@android:style/Theme.Light.NoTitleBar"
379-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:60:13-66
380            android:windowSoftInputMode="adjustResize" />
380-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:61:13-55
381        <activity
381-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:62:9-65:86
382            android:name="com.anythink.basead.ui.RewardExitConfirmDialogActivity"
382-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:63:13-82
383            android:exported="false"
383-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:64:13-37
384            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
384-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:65:13-83
385        <activity
385-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:66:9-71:74
386            android:name="com.anythink.basead.ui.activity.ATMixSplashActivity"
386-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:67:13-79
387            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
387-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:68:13-83
388            android:exported="false"
388-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:69:13-37
389            android:launchMode="standard"
389-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:70:13-42
390            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- 如果targetSdkVersion设置值>=24，则强烈建议添加以下provider，否则会影响app变现 -->
390-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:71:13-71
391        <!-- android:authorities="${applicationId}.bd.provider" authorities中${applicationId}部分必须替换成app自己的包名 -->
392        <!-- 原来的FileProvider在新版本中改为BdFileProvider,继承自v4的FileProvider,需要在应用内引用support-v4包 -->
393        <provider
393-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:14:9-22:20
394            android:name="com.baidu.mobads.sdk.api.BdFileProvider"
394-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:15:13-67
395            android:authorities="com.example.concentration.bd.provider"
395-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:16:13-63
396            android:exported="false"
396-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:17:13-37
397            android:grantUriPermissions="true" >
397-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:18:13-47
398            <meta-data
398-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
399                android:name="android.support.FILE_PROVIDER_PATHS"
399-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
400                android:resource="@xml/bd_file_paths" />
400-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
401        </provider> <!-- 落地页配置 -->
402        <activity
402-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:24:9-27:63
403            android:name="com.baidu.mobads.sdk.api.AppActivity"
403-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:25:13-64
404            android:configChanges="screenSize|keyboard|keyboardHidden|orientation"
404-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:26:13-83
405            android:theme="@android:style/Theme.NoTitleBar" /> <!-- 激励视频、全屏视频配置 -->
405-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:27:13-60
406        <activity
406-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:29:9-32:75
407            android:name="com.baidu.mobads.sdk.api.MobRewardVideoActivity"
407-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:30:13-75
408            android:configChanges="screenSize|orientation|keyboardHidden|uiMode"
408-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:31:13-81
409            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
409-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:32:13-72
410        <activity
410-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:33:9-36:75
411            android:name="com.baidu.mobads.sdk.api.BdShellActivity"
411-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:34:13-68
412            android:configChanges="screenSize|keyboard|keyboardHidden|orientation|smallestScreenSize|screenLayout"
412-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:35:13-115
413            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
413-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:36:13-72
414        <activity
414-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:27:9-31:50
415            android:name="com.beizi.ad.AdActivity"
415-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:28:13-51
416            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
416-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:29:13-122
417            android:exported="false"
417-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:30:13-37
418            android:hardwareAccelerated="true" />
418-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:31:13-47
419        <activity
419-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:32:9-35:61
420            android:name="com.beizi.ad.internal.activity.BeiZiInterstitialActivity"
420-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:33:13-84
421            android:exported="false"
421-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:34:13-37
422            android:theme="@style/BeiZiTheme.Transparent" />
422-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:35:13-58
423        <activity
423-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:36:9-38:40
424            android:name="com.beizi.ad.internal.activity.DownloadAppInfoActivity"
424-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:37:13-82
425            android:exported="false" />
425-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:38:13-37
426        <activity
426-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:39:9-42:61
427            android:name="com.beizi.ad.internal.activity.BeiZiDownloadDialogActivity"
427-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:40:13-86
428            android:exported="false"
428-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:41:13-37
429            android:theme="@style/BeiZiTheme.Transparent" />
429-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:42:13-58
430
431        <service
431-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:44:9-46:40
432            android:name="com.beizi.ad.DownloadService"
432-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:45:13-56
433            android:exported="false" /> <!-- 声明SDK所需要的组件 -->
433-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:46:13-37
434        <service
434-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:32:9-35:43
435            android:name="com.qq.e.comm.DownloadService"
435-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:33:13-57
436            android:exported="false"
436-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:34:13-37
437            android:multiprocess="true" /> <!-- 请开发者注意字母的大小写，ADActivity，而不是AdActivity -->
437-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:35:13-40
438        <activity
438-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:37:9-41:43
439            android:name="com.qq.e.ads.ADActivity"
439-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:38:13-51
440            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
440-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:39:13-83
441            android:exported="false"
441-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:40:13-37
442            android:multiprocess="true" />
442-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:41:13-40
443        <activity
443-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:42:9-48:69
444            android:name="com.qq.e.ads.PortraitADActivity"
444-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:43:13-59
445            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
445-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:44:13-83
446            android:exported="false"
446-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:45:13-37
447            android:multiprocess="true"
447-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:46:13-40
448            android:screenOrientation="portrait"
448-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:47:13-49
449            android:theme="@android:style/Theme.Light.NoTitleBar" />
449-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:48:13-66
450        <activity
450-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:49:9-55:69
451            android:name="com.qq.e.ads.LandscapeADActivity"
451-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:50:13-60
452            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
452-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:51:13-83
453            android:exported="false"
453-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:52:13-37
454            android:multiprocess="true"
454-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:53:13-40
455            android:screenOrientation="sensorLandscape"
455-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:54:13-56
456            android:theme="@android:style/Theme.Light.NoTitleBar" /> <!-- 用于激励视频可选广告的竖屏透明背景activity -->
456-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:55:13-66
457        <activity
457-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:57:9-66:20
458            android:name="com.qq.e.ads.RewardvideoPortraitADActivity"
458-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:58:13-70
459            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
459-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:59:13-83
460            android:exported="false"
460-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:60:13-37
461            android:multiprocess="true"
461-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:61:13-40
462            android:theme="@android:style/Theme.Light.NoTitleBar" >
462-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:62:13-66
463            <meta-data
463-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:63:13-65:40
464                android:name="android.notch_support"
464-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:64:17-53
465                android:value="true" />
465-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:65:17-37
466        </activity> <!-- 用于激励视频可选广告的横屏透明背景activity -->
467        <activity
467-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:68:9-78:20
468            android:name="com.qq.e.ads.RewardvideoLandscapeADActivity"
468-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:69:13-71
469            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
469-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:70:13-83
470            android:exported="false"
470-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:71:13-37
471            android:multiprocess="true"
471-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:72:13-40
472            android:screenOrientation="landscape"
472-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:73:13-50
473            android:theme="@android:style/Theme.Light.NoTitleBar" >
473-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:74:13-66
474            <meta-data
474-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:63:13-65:40
475                android:name="android.notch_support"
475-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:64:17-53
476                android:value="true" />
476-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:65:17-37
477        </activity>
478        <activity
478-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:79:9-83:86
479            android:name="com.qq.e.ads.DialogActivity"
479-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:80:13-55
480            android:exported="false"
480-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:81:13-37
481            android:multiprocess="true"
481-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:82:13-40
482            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
482-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:83:13-83
483        <activity
483-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:28:9-32:69
484            android:name="com.kwad.sdk.api.proxy.app.AdWebViewActivity"
484-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:29:13-72
485            android:configChanges="keyboardHidden|orientation|screenSize"
485-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:30:13-74
486            android:screenOrientation="portrait"
486-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:31:13-49
487            android:theme="@android:style/Theme.Light.NoTitleBar" />
487-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:32:13-66
488        <activity
488-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:33:9-37:80
489            android:name="com.kwad.sdk.api.proxy.app.KsFullScreenVideoActivity"
489-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:34:13-80
490            android:configChanges="keyboardHidden|orientation|screenSize"
490-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:35:13-74
491            android:screenOrientation="behind"
491-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:36:13-47
492            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
492-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:37:13-77
493        <activity
493-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:38:9-42:80
494            android:name="com.kwad.sdk.api.proxy.app.KsFullScreenLandScapeVideoActivity"
494-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:39:13-89
495            android:configChanges="keyboardHidden|orientation|screenSize"
495-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:40:13-74
496            android:screenOrientation="landscape"
496-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:41:13-50
497            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
497-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:42:13-77
498        <activity
498-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:43:9-47:80
499            android:name="com.kwad.sdk.api.proxy.app.KsRewardVideoActivity"
499-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:44:13-76
500            android:configChanges="keyboardHidden|orientation|screenSize"
500-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:45:13-74
501            android:screenOrientation="portrait"
501-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:46:13-49
502            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
502-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:47:13-77
503        <activity
503-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:48:9-52:80
504            android:name="com.kwad.sdk.api.proxy.app.KSRewardLandScapeVideoActivity"
504-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:49:13-85
505            android:configChanges="keyboardHidden|orientation|screenSize"
505-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:50:13-74
506            android:screenOrientation="landscape"
506-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:51:13-50
507            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
507-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:52:13-77
508        <activity
508-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:53:9-57:80
509            android:name="com.kwad.sdk.api.proxy.app.FeedDownloadActivity"
509-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:54:13-75
510            android:configChanges="keyboardHidden|orientation|screenSize"
510-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:55:13-74
511            android:screenOrientation="portrait"
511-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:56:13-49
512            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
512-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:57:13-77
513        <activity
513-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:58:9-62:69
514            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$KsTrendsActivity"
514-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:59:13-92
515            android:configChanges="keyboardHidden|orientation|screenSize"
515-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:60:13-74
516            android:screenOrientation="portrait"
516-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:61:13-49
517            android:theme="@android:style/Theme.Light.NoTitleBar" />
517-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:62:13-66
518        <activity
518-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:63:9-66:52
519            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$ProfileHomeActivity"
519-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:64:13-95
520            android:configChanges="keyboardHidden|orientation|screenSize"
520-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:65:13-74
521            android:screenOrientation="portrait" />
521-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:66:13-49
522        <activity
522-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:67:9-70:52
523            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$GoodsPlayBackActivity"
523-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:68:13-97
524            android:configChanges="keyboardHidden|orientation|screenSize"
524-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:69:13-74
525            android:screenOrientation="portrait" />
525-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:70:13-49
526        <activity
526-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:71:9-74:52
527            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$ProfileVideoDetailActivity"
527-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:72:13-102
528            android:configChanges="keyboardHidden|orientation|screenSize"
528-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:73:13-74
529            android:screenOrientation="portrait" />
529-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:74:13-49
530        <activity
530-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:75:9-78:52
531            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$TubeProfileActivity"
531-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:76:13-95
532            android:configChanges="keyboardHidden|orientation|screenSize"
532-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:77:13-74
533            android:screenOrientation="portrait" />
533-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:78:13-49
534        <activity
534-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:79:9-82:52
535            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$ChannelDetailActivity"
535-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:80:13-97
536            android:configChanges="keyboardHidden|orientation|screenSize"
536-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:81:13-74
537            android:screenOrientation="portrait" />
537-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:82:13-49
538        <activity
538-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:83:9-86:52
539            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$TubeDetailActivity"
539-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:84:13-94
540            android:configChanges="keyboardHidden|orientation|screenSize"
540-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:85:13-74
541            android:screenOrientation="portrait" />
541-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:86:13-49
542        <activity
542-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:87:9-90:52
543            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$EpisodeDetailActivity"
543-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:88:13-97
544            android:configChanges="keyboardHidden|orientation|screenSize"
544-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:89:13-74
545            android:screenOrientation="portrait" />
545-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:90:13-49
546        <activity
546-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:91:9-95:80
547            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity1"
547-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:92:13-93
548            android:configChanges="keyboardHidden|orientation|screenSize"
548-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:93:13-74
549            android:screenOrientation="portrait"
549-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:94:13-49
550            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
550-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:95:13-77
551        <activity
551-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:96:9-100:80
552            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity2"
552-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:97:13-93
553            android:configChanges="keyboardHidden|orientation|screenSize"
553-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:98:13-74
554            android:screenOrientation="portrait"
554-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:99:13-49
555            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
555-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:100:13-77
556        <activity
556-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:101:9-105:80
557            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity3"
557-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:102:13-93
558            android:configChanges="keyboardHidden|orientation|screenSize"
558-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:103:13-74
559            android:screenOrientation="portrait"
559-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:104:13-49
560            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
560-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:105:13-77
561        <activity
561-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:106:9-109:52
562            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity4"
562-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:107:13-93
563            android:configChanges="keyboardHidden|orientation|screenSize"
563-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:108:13-74
564            android:screenOrientation="portrait" />
564-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:109:13-49
565        <activity
565-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:110:9-113:52
566            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity5"
566-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:111:13-93
567            android:configChanges="keyboardHidden|orientation|screenSize"
567-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:112:13-74
568            android:screenOrientation="portrait" />
568-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:113:13-49
569        <activity
569-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:114:9-117:52
570            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity6"
570-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:115:13-93
571            android:configChanges="keyboardHidden|orientation|screenSize"
571-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:116:13-74
572            android:screenOrientation="portrait" />
572-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:117:13-49
573        <activity
573-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:118:9-121:52
574            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity7"
574-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:119:13-93
575            android:configChanges="keyboardHidden|orientation|screenSize"
575-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:120:13-74
576            android:screenOrientation="portrait" />
576-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:121:13-49
577        <activity
577-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:122:9-125:52
578            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity8"
578-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:123:13-93
579            android:configChanges="keyboardHidden|orientation|screenSize"
579-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:124:13-74
580            android:screenOrientation="portrait" />
580-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:125:13-49
581        <activity
581-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:126:9-129:52
582            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity9"
582-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:127:13-93
583            android:configChanges="keyboardHidden|orientation|screenSize"
583-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:128:13-74
584            android:screenOrientation="portrait" />
584-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:129:13-49
585        <activity
585-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:130:9-133:52
586            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity10"
586-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:131:13-94
587            android:configChanges="keyboardHidden|orientation|screenSize"
587-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:132:13-74
588            android:screenOrientation="portrait" />
588-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:133:13-49
589        <activity
589-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:134:9-138:52
590            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleTop1"
590-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:135:13-102
591            android:configChanges="keyboardHidden|orientation|screenSize"
591-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:136:13-74
592            android:launchMode="singleTop"
592-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:137:13-43
593            android:screenOrientation="portrait" />
593-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:138:13-49
594        <activity
594-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:139:9-143:52
595            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleTop2"
595-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:140:13-102
596            android:configChanges="keyboardHidden|orientation|screenSize"
596-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:141:13-74
597            android:launchMode="singleTop"
597-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:142:13-43
598            android:screenOrientation="portrait" />
598-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:143:13-49
599        <activity
599-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:144:9-148:52
600            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleInstance1"
600-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:145:13-107
601            android:configChanges="keyboardHidden|orientation|screenSize"
601-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:146:13-74
602            android:launchMode="singleInstance"
602-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:147:13-48
603            android:screenOrientation="portrait" />
603-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:148:13-49
604        <activity
604-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:149:9-153:52
605            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleInstance2"
605-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:150:13-107
606            android:configChanges="keyboardHidden|orientation|screenSize"
606-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:151:13-74
607            android:launchMode="singleInstance"
607-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:152:13-48
608            android:screenOrientation="portrait" />
608-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:153:13-49
609        <activity
609-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:154:9-159:80
610            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$DeveloperConfigActivity"
610-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:155:13-99
611            android:configChanges="keyboardHidden|orientation|screenSize"
611-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:156:13-74
612            android:launchMode="singleInstance"
612-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:157:13-48
613            android:screenOrientation="portrait"
613-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:158:13-49
614            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
614-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:159:13-77
615
616        <service android:name="com.kwad.sdk.api.proxy.app.FileDownloadService$SharedMainProcessService" />
616-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:161:9-107
616-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:161:18-104
617        <service android:name="com.kwad.sdk.api.proxy.app.FileDownloadService$SeparateProcessService" />
617-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:162:9-105
617-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:162:18-102
618        <service
618-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:163:9-165:40
619            android:name="com.kwad.sdk.api.proxy.app.DownloadService"
619-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:164:13-70
620            android:exported="false" />
620-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:165:13-37
621        <service
621-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:166:9-169:47
622            android:name="com.kwad.sdk.api.proxy.app.ServiceProxyRemote"
622-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:167:13-73
623            android:exported="false"
623-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:168:13-37
624            android:process=":kssdk_remote" />
624-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:169:13-44
625        <service
625-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:170:9-181:19
626            android:name="com.kwad.sdk.api.proxy.VideoWallpaperService"
626-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:171:13-72
627            android:exported="true"
627-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:172:13-36
628            android:permission="android.permission.BIND_WALLPAPER" >
628-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:173:13-67
629            <intent-filter android:priority="1000" >
629-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:174:13-176:29
629-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:174:28-51
630                <action android:name="android.service.wallpaper.WallpaperService" />
630-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:175:17-85
630-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:175:25-82
631            </intent-filter>
632
633            <meta-data
633-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:178:13-180:58
634                android:name="android.service.wallpaper"
634-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:179:17-57
635                android:resource="@xml/ksad_wallpaper" />
635-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:180:17-55
636        </service>
637
638        <provider
638-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:183:9-191:20
639            android:name="com.kwad.sdk.api.proxy.app.AdSdkFileProvider"
639-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:184:13-72
640            android:authorities="com.example.concentration.adFileProvider"
640-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:185:13-66
641            android:exported="false"
641-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:186:13-37
642            android:grantUriPermissions="true" >
642-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:187:13-47
643            <meta-data
643-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
644                android:name="android.support.FILE_PROVIDER_PATHS"
644-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
645                android:resource="@xml/ksad_file_paths" />
645-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
646        </provider>
647
648        <meta-data
648-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:32:9-291
649            android:name="ZEUS_PLUGIN_PANGLE"
649-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:32:20-53
650            android:value="{                 apiVersionCode:6841,                 packageName:com.byted.pangle,                 minPluginVersion:6841,                 internalPath:**********,                 internalVersionCode:6841             }" />
650-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:32:54-289
651        <meta-data
651-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:33:9-248
652            android:name="ZEUS_PLUGIN_com.byted.csj.ext"
652-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:33:20-64
653            android:value="{apiVersionCode:999,packageName:com.byted.csj.ext,minPluginVersion:1000,maxPluginVersion:999999999,internalPath:&apos;&apos;,internalVersionCode:-1, appKey:&apos;&apos;,appSecretKey:&apos;&apos;}" />
653-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:33:65-246
654
655        <activity
655-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:9-331
656            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Activity"
656-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:19-98
657            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
657-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:99-171
658            android:exported="false"
658-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:172-196
659            android:launchMode="standard"
659-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:197-226
660            android:theme="@android:style/Theme.NoTitleBar"
660-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:227-274
661            android:windowSoftInputMode="adjustResize|stateHidden" />
661-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:275-329
662        <activity
662-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:9-384
663            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Portrait_Activity"
663-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:19-107
664            android:configChanges="orientation|keyboardHidden|navigation|screenSize|uiMode"
664-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:108-187
665            android:exported="false"
665-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:188-212
666            android:launchMode="standard"
666-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:213-242
667            android:screenOrientation="portrait"
667-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:243-279
668            android:theme="@android:style/Theme.NoTitleBar"
668-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:280-327
669            android:windowSoftInputMode="adjustResize|stateHidden" />
669-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:328-382
670        <activity
670-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:9-334
671            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Activity_T"
671-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:19-100
672            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
672-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:101-173
673            android:exported="false"
673-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:174-198
674            android:launchMode="standard"
674-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:199-228
675            android:theme="@android:style/Theme.Translucent"
675-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:229-277
676            android:windowSoftInputMode="adjustResize|stateHidden" />
676-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:278-332
677        <activity
677-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:9-386
678            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Landscape_Activity"
678-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:19-108
679            android:configChanges="orientation|keyboardHidden|navigation|screenSize|uiMode"
679-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:109-188
680            android:exported="false"
680-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:189-213
681            android:launchMode="standard"
681-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:214-243
682            android:screenOrientation="landscape"
682-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:244-281
683            android:theme="@android:style/Theme.NoTitleBar"
683-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:282-329
684            android:windowSoftInputMode="adjustResize|stateHidden" />
684-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:330-384
685        <activity
685-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:9-292
686            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_Activity"
686-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:19-89
687            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
687-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:90-162
688            android:exported="false"
688-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:163-187
689            android:theme="@android:style/Theme.NoTitleBar"
689-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:188-235
690            android:windowSoftInputMode="adjustResize|stateHidden" />
690-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:236-290
691        <activity
691-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:9-338
692            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_SingleTask_Activity_T"
692-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:19-102
693            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
693-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:103-175
694            android:exported="false"
694-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:176-200
695            android:launchMode="singleTask"
695-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:201-232
696            android:theme="@android:style/Theme.Translucent"
696-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:233-281
697            android:windowSoftInputMode="adjustResize|stateHidden" />
697-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:282-336
698        <activity
698-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:9-287
699            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_SingleTask_Activity"
699-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:19-100
700            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
700-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:101-173
701            android:exported="false"
701-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:174-198
702            android:launchMode="singleTask"
702-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:199-230
703            android:windowSoftInputMode="adjustResize|stateHidden" />
703-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:231-285
704
705        <provider
705-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:43:9-260
706            android:name="com.bytedance.sdk.openadsdk.stub.server.DownloaderServerManager"
706-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:43:19-97
707            android:authorities="com.example.concentration.pangle.servermanager.downloader.com.bytedance.sdk.openadsdk.adhost"
707-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:43:98-203
708            android:exported="false"
708-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:43:204-228
709            android:process=":downloader" />
709-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:43:229-258
710        <provider
710-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:44:9-183
711            android:name="com.bytedance.sdk.openadsdk.stub.server.MainServerManager"
711-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:44:19-91
712            android:authorities="com.example.concentration.pangle.servermanager.main"
712-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:44:92-156
713            android:exported="false" />
713-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:44:157-181
714        <provider
714-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:47:9-181
715            android:name="com.bytedance.pangle.provider.MainProcessProviderProxy"
715-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:47:19-88
716            android:authorities="com.example.concentration.pangle.provider.proxy.main"
716-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:47:89-154
717            android:exported="false" />
717-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:47:155-179
718        <provider
718-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:48:9-188
719            android:name="com.bytedance.pangle.FileProvider"
719-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:48:19-67
720            android:authorities="com.example.concentration.pangle.fileprovider"
720-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:48:68-126
721            android:exported="false"
721-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:48:162-186
722            android:grantUriPermissions="true" />
722-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:48:127-161
723
724        <activity
724-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:50:9-134
725            android:name="com.ss.android.downloadlib.addownload.compliance.AppPrivacyPolicyActivity"
725-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:50:19-107
726            android:exported="false" />
726-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:50:108-132
727        <activity
727-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:51:9-131
728            android:name="com.ss.android.downloadlib.addownload.compliance.AppDetailInfoActivity"
728-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:51:19-104
729            android:exported="false" />
729-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:51:105-129
730        <activity
730-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:52:9-207
731            android:name="com.ss.android.downloadlib.activity.TTDelegateActivity"
731-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:52:19-88
732            android:exported="false"
732-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:52:181-205
733            android:launchMode="singleTask"
733-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:52:89-120
734            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
734-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:52:121-180
735        <activity
735-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:53:9-205
736            android:name="com.ss.android.downloadlib.activity.JumpKllkActivity"
736-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:53:19-86
737            android:exported="false"
737-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:53:179-203
738            android:launchMode="singleTask"
738-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:53:87-118
739            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
739-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:53:119-178
740
741        <receiver
741-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:55:9-118
742            android:name="com.ss.android.downloadlib.core.download.DownloadReceiver"
742-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:55:19-91
743            android:exported="false" />
743-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:55:92-116
744
745        <service
745-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:56:9-150
746            android:name="com.ss.android.socialbase.appdownloader.DownloadHandlerService"
746-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:56:18-95
747            android:exported="false"
747-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:56:124-148
748            android:stopWithTask="true" />
748-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:56:96-123
749
750        <activity
750-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:58:9-208
751            android:name="com.ss.android.socialbase.appdownloader.view.DownloadTaskDeleteActivity"
751-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:58:19-105
752            android:exported="false"
752-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:58:182-206
753            android:launchMode="singleTask"
753-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:58:106-137
754            android:theme="@android:style/Theme.Dialog" />
754-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:58:138-181
755        <activity
755-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:59:9-207
756            android:name="com.ss.android.socialbase.appdownloader.view.JumpUnknownSourceActivity"
756-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:59:19-104
757            android:exported="false"
757-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:59:181-205
758            android:launchMode="singleTask"
758-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:59:105-136
759            android:theme="@android:style/Theme.Dialog" />
759-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:59:137-180
760
761        <service
761-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:9-232
762            android:name="com.ss.android.socialbase.appdownloader.RetryJobSchedulerService"
762-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:18-97
763            android:enabled="true"
763-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:98-120
764            android:exported="false"
764-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:121-145
765            android:permission="android.permission.BIND_JOB_SERVICE"
765-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:146-202
766            android:stopWithTask="true" />
766-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:203-230
767        <service
767-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:62:9-66:19
768            android:name="com.ss.android.socialbase.downloader.downloader.IndependentProcessDownloadService"
768-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:62:18-114
769            android:exported="false"
769-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:62:115-139
770            android:process=":downloader" >
770-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:62:140-169
771            <intent-filter>
771-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:63:13-65:29
772                <action android:name="com.ss.android.socialbase.downloader.remote" />
772-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:64:17-85
772-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:64:25-83
773            </intent-filter>
774        </service>
775        <service
775-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:67:9-165
776            android:name="com.ss.android.socialbase.downloader.notification.DownloadNotificationService"
776-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:67:18-110
777            android:exported="false"
777-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:67:139-163
778            android:stopWithTask="true" />
778-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:67:111-138
779        <service
779-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:68:9-151
780            android:name="com.ss.android.socialbase.downloader.downloader.DownloadService"
780-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:68:18-96
781            android:exported="false"
781-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:68:125-149
782            android:stopWithTask="true" />
782-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:68:97-124
783        <service
783-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:69:9-152
784            android:name="com.ss.android.socialbase.downloader.impls.DownloadHandleService"
784-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:69:18-97
785            android:exported="false"
785-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:69:126-150
786            android:stopWithTask="true" />
786-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:69:98-125
787        <service
787-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:75:9-159
788            android:name="com.ss.android.socialbase.downloader.downloader.SqlDownloadCacheService"
788-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:75:18-104
789            android:exported="false"
789-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:75:133-157
790            android:stopWithTask="true" />
790-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:75:105-132
791
792        <meta-data
792-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:77:9-294
793            android:name="ZEUS_PLUGIN_LIVE"
793-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:77:20-51
794            android:value="{                 apiVersionCode:2114,                 packageName:com.byted.live.lite,                 minPluginVersion:211400,                 maxPluginVersion:999999,                 isSupportLibIsolate:true             }" />
794-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:77:52-292
795        <meta-data
795-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:78:9-79
796            android:name="LIVE_API_VERSION_CODE"
796-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:78:20-56
797            android:value="2114" />
797-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:78:57-77
798
799        <activity
799-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:80:9-139
800            android:name="com.bytedance.android.openliveplugin.stub.activity.DouyinAuthorizeActivityProxy"
800-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:80:19-113
801            android:exported="true" />
801-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:80:114-137
802        <activity
802-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:81:9-178
803            android:name="com.bytedance.android.openliveplugin.stub.activity.DouyinAuthorizeActivityLiveProcessProxy"
803-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:81:19-124
804            android:exported="true"
804-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:81:125-148
805            android:process=":bytelive" />
805-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:81:149-176
806
807        <provider
807-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:84:9-263
808            android:name="com.bytedance.android.openliveplugin.process.server.LiveServerManager"
808-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:84:19-103
809            android:authorities="com.example.concentration.bytedance.android.openliveplugin.process.server.LiveServerManager"
809-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:84:104-208
810            android:exported="false"
810-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:84:209-233
811            android:process=":bytelive" />
811-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:84:234-261
812
813        <activity
813-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:9-301
814            android:name="com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait"
814-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:19-115
815            android:configChanges="orientation|keyboardHidden|colorMode|uiMode"
815-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:116-183
816            android:exported="false"
816-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:275-299
817            android:screenOrientation="portrait"
817-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:184-220
818            android:theme="@android:style/Theme.Light.NoTitleBar" />
818-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:221-274
819        <activity
819-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:9-302
820            android:name="com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait2"
820-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:19-116
821            android:configChanges="orientation|keyboardHidden|colorMode|uiMode"
821-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:117-184
822            android:exported="false"
822-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:276-300
823            android:screenOrientation="portrait"
823-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:185-221
824            android:theme="@android:style/Theme.Light.NoTitleBar" />
824-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:222-275
825        <activity
825-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:9-302
826            android:name="com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait3"
826-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:19-116
827            android:configChanges="orientation|keyboardHidden|colorMode|uiMode"
827-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:117-184
828            android:exported="false"
828-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:276-300
829            android:screenOrientation="portrait"
829-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:185-221
830            android:theme="@android:style/Theme.Light.NoTitleBar" />
830-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:222-275
831        <activity
831-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:9-302
832            android:name="com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait4"
832-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:19-116
833            android:configChanges="orientation|keyboardHidden|colorMode|uiMode"
833-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:117-184
834            android:exported="false"
834-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:276-300
835            android:screenOrientation="portrait"
835-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:185-221
836            android:theme="@android:style/Theme.Light.NoTitleBar" />
836-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:222-275
837        <activity
837-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:9-302
838            android:name="com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait5"
838-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:19-116
839            android:configChanges="orientation|keyboardHidden|colorMode|uiMode"
839-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:117-184
840            android:exported="false"
840-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:276-300
841            android:screenOrientation="portrait"
841-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:185-221
842            android:theme="@android:style/Theme.Light.NoTitleBar" />
842-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:222-275
843        <activity
843-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:9-298
844            android:name="com.byted.live.lite.Activity_bytelive_singleTask4"
844-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:19-83
845            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
845-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:84-156
846            android:exported="false"
846-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:157-181
847            android:launchMode="singleTask"
847-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:182-213
848            android:process=":bytelive"
848-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:214-241
849            android:windowSoftInputMode="adjustResize|stateHidden" />
849-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:242-296
850        <activity
850-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:9-296
851            android:name="com.byted.live.lite.Activity_bytelive_singleTop3"
851-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:19-82
852            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
852-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:83-155
853            android:exported="false"
853-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:156-180
854            android:launchMode="singleTop"
854-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:181-211
855            android:process=":bytelive"
855-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:212-239
856            android:windowSoftInputMode="adjustResize|stateHidden" />
856-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:240-294
857        <activity
857-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:9-296
858            android:name="com.byted.live.lite.Activity_bytelive_singleTop2"
858-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:19-82
859            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
859-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:83-155
860            android:exported="false"
860-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:156-180
861            android:launchMode="singleTop"
861-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:181-211
862            android:process=":bytelive"
862-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:212-239
863            android:windowSoftInputMode="adjustResize|stateHidden" />
863-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:240-294
864        <activity
864-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:9-296
865            android:name="com.byted.live.lite.Activity_bytelive_singleTop5"
865-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:19-82
866            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
866-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:83-155
867            android:exported="false"
867-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:156-180
868            android:launchMode="singleTop"
868-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:181-211
869            android:process=":bytelive"
869-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:212-239
870            android:windowSoftInputMode="adjustResize|stateHidden" />
870-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:240-294
871        <activity
871-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:9-296
872            android:name="com.byted.live.lite.Activity_bytelive_singleTop4"
872-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:19-82
873            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
873-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:83-155
874            android:exported="false"
874-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:156-180
875            android:launchMode="singleTop"
875-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:181-211
876            android:process=":bytelive"
876-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:212-239
877            android:windowSoftInputMode="adjustResize|stateHidden" />
877-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:240-294
878        <activity
878-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:9-293
879            android:name="com.byted.live.lite.Activity_bytelive_standard"
879-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:19-80
880            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
880-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:81-153
881            android:exported="false"
881-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:154-178
882            android:launchMode="standard"
882-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:179-208
883            android:process=":bytelive"
883-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:209-236
884            android:windowSoftInputMode="adjustResize|stateHidden" />
884-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:237-291
885        <activity
885-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:9-296
886            android:name="com.byted.live.lite.Activity_bytelive_singleTop1"
886-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:19-82
887            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
887-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:83-155
888            android:exported="false"
888-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:156-180
889            android:launchMode="singleTop"
889-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:181-211
890            android:process=":bytelive"
890-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:212-239
891            android:windowSoftInputMode="adjustResize|stateHidden" />
891-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:240-294
892        <activity
892-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:9-298
893            android:name="com.byted.live.lite.Activity_bytelive_singleTask1"
893-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:19-83
894            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
894-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:84-156
895            android:exported="false"
895-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:157-181
896            android:launchMode="singleTask"
896-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:182-213
897            android:process=":bytelive"
897-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:214-241
898            android:windowSoftInputMode="adjustResize|stateHidden" />
898-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:242-296
899        <activity
899-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:9-296
900            android:name="com.byted.live.lite.Activity_bytelive_singleTop6"
900-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:19-82
901            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
901-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:83-155
902            android:exported="false"
902-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:156-180
903            android:launchMode="singleTop"
903-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:181-211
904            android:process=":bytelive"
904-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:212-239
905            android:windowSoftInputMode="adjustResize|stateHidden" />
905-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:240-294
906        <activity
906-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:9-298
907            android:name="com.byted.live.lite.Activity_bytelive_singleTask2"
907-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:19-83
908            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
908-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:84-156
909            android:exported="false"
909-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:157-181
910            android:launchMode="singleTask"
910-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:182-213
911            android:process=":bytelive"
911-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:214-241
912            android:windowSoftInputMode="adjustResize|stateHidden" />
912-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:242-296
913        <activity
913-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:9-298
914            android:name="com.byted.live.lite.Activity_bytelive_singleTask3"
914-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:19-83
915            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
915-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:84-156
916            android:exported="false"
916-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:157-181
917            android:launchMode="singleTask"
917-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:182-213
918            android:process=":bytelive"
918-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:214-241
919            android:windowSoftInputMode="adjustResize|stateHidden" />
919-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:242-296
920        <activity
920-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:9-266
921            android:name="com.byted.live.lite.Activity_main_singleTask1"
921-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:19-79
922            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
922-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:80-152
923            android:exported="false"
923-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:153-177
924            android:launchMode="singleTask"
924-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:178-209
925            android:windowSoftInputMode="adjustResize|stateHidden" />
925-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:210-264
926        <activity
926-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:9-266
927            android:name="com.byted.live.lite.Activity_main_singleTask2"
927-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:19-79
928            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
928-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:80-152
929            android:exported="false"
929-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:153-177
930            android:launchMode="singleTask"
930-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:178-209
931            android:windowSoftInputMode="adjustResize|stateHidden" />
931-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:210-264
932        <activity
932-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:9-266
933            android:name="com.byted.live.lite.Activity_main_singleTask3"
933-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:19-79
934            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
934-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:80-152
935            android:exported="false"
935-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:153-177
936            android:launchMode="singleTask"
936-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:178-209
937            android:windowSoftInputMode="adjustResize|stateHidden" />
937-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:210-264
938        <activity
938-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:9-266
939            android:name="com.byted.live.lite.Activity_main_singleTask4"
939-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:19-79
940            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
940-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:80-152
941            android:exported="false"
941-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:153-177
942            android:launchMode="singleTask"
942-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:178-209
943            android:windowSoftInputMode="adjustResize|stateHidden" />
943-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:210-264
944        <activity
944-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:9-261
945            android:name="com.byted.live.lite.Activity_main_standard"
945-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:19-76
946            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
946-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:77-149
947            android:exported="false"
947-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:150-174
948            android:launchMode="standard"
948-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:175-204
949            android:windowSoftInputMode="adjustResize|stateHidden" />
949-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:205-259
950        <activity
950-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:9-264
951            android:name="com.byted.live.lite.Activity_main_singleTop1"
951-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:19-78
952            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
952-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:79-151
953            android:exported="false"
953-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:152-176
954            android:launchMode="singleTop"
954-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:177-207
955            android:windowSoftInputMode="adjustResize|stateHidden" />
955-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:208-262
956        <activity
956-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:9-264
957            android:name="com.byted.live.lite.Activity_main_singleTop2"
957-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:19-78
958            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
958-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:79-151
959            android:exported="false"
959-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:152-176
960            android:launchMode="singleTop"
960-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:177-207
961            android:windowSoftInputMode="adjustResize|stateHidden" />
961-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:208-262
962        <activity
962-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:9-274
963            android:name="com.byted.live.lite.Activity_main_singleInstance1"
963-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:19-83
964            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
964-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:84-156
965            android:exported="false"
965-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:157-181
966            android:launchMode="singleInstance"
966-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:182-217
967            android:windowSoftInputMode="adjustResize|stateHidden" />
967-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:218-272
968        <activity
968-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:9-264
969            android:name="com.byted.live.lite.Activity_main_singleTop5"
969-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:19-78
970            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
970-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:79-151
971            android:exported="false"
971-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:152-176
972            android:launchMode="singleTop"
972-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:177-207
973            android:windowSoftInputMode="adjustResize|stateHidden" />
973-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:208-262
974        <activity
974-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:9-264
975            android:name="com.byted.live.lite.Activity_main_singleTop6"
975-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:19-78
976            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
976-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:79-151
977            android:exported="false"
977-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:152-176
978            android:launchMode="singleTop"
978-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:177-207
979            android:windowSoftInputMode="adjustResize|stateHidden" />
979-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:208-262
980        <activity
980-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:9-264
981            android:name="com.byted.live.lite.Activity_main_singleTop3"
981-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:19-78
982            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
982-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:79-151
983            android:exported="false"
983-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:152-176
984            android:launchMode="singleTop"
984-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:177-207
985            android:windowSoftInputMode="adjustResize|stateHidden" />
985-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:208-262
986        <activity
986-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:9-264
987            android:name="com.byted.live.lite.Activity_main_singleTop4"
987-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:19-78
988            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
988-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:79-151
989            android:exported="false"
989-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:152-176
990            android:launchMode="singleTop"
990-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:177-207
991            android:windowSoftInputMode="adjustResize|stateHidden" />
991-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:208-262
992        <activity
992-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:9-306
993            android:name="com.byted.live.lite.Activity_bytelive_singleInstance1"
993-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:19-87
994            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
994-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:88-160
995            android:exported="false"
995-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:161-185
996            android:launchMode="singleInstance"
996-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:186-221
997            android:process=":bytelive"
997-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:222-249
998            android:windowSoftInputMode="adjustResize|stateHidden" />
998-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:250-************        <provider
1000-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:116:9-220
1001            android:name="com.byted.live.lite.ServerManager_bytelive"
1001-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:116:19-76
1002            android:authorities="com.example.concentration.pangle.servermanager.bytelive.com.byted.live.lite"
1002-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:116:77-165
1003            android:exported="false"
1003-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:116:************            android:process=":bytelive" />
1004-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:116:************        <provider
1005-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:117:9-208
1006            android:name="com.byted.live.lite.ServerManager_push"
1006-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:117:19-72
1007            android:authorities="com.example.concentration.pangle.servermanager.push.com.byted.live.lite"
1007-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:117:73-157
1008            android:exported="false"
1008-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:117:158-182
1009            android:process=":push" />
1009-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:117:183-206
1010        <provider
1010-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:118:9-226
1011            android:name="com.byted.live.lite.ServerManager_downloader"
1011-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:118:19-78
1012            android:authorities="com.example.concentration.pangle.servermanager.downloader.com.byted.live.lite"
1012-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:118:79-169
1013            android:exported="false"
1013-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:118:170-194
1014            android:process=":downloader" />
1014-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:118:195-224
1015        <provider
1015-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:36:9-45:20
1016            android:name="com.sigmob.sdk.SigmobFileProvider"
1016-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:37:13-61
1017            android:authorities="com.example.concentration.sigprovider"
1017-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:38:13-63
1018            android:exported="false"
1018-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:39:13-37
1019            android:grantUriPermissions="true"
1019-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:40:13-47
1020            android:initOrder="200" >
1020-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:41:13-36
1021            <meta-data
1021-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
1022                android:name="android.support.FILE_PROVIDER_PATHS"
1022-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
1023                android:resource="@xml/sigmob_provider_paths" />
1023-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
1024        </provider> <!-- TT 国内 -->
1025        <activity
1025-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:47:9-53:60
1026            android:name="com.sigmob.sdk.base.common.TransparentAdActivity"
1026-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:48:13-76
1027            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
1027-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:49:13-83
1028            android:hardwareAccelerated="true"
1028-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:50:13-47
1029            android:multiprocess="true"
1029-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:51:13-40
1030            android:screenOrientation="behind"
1030-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:52:13-47
1031            android:theme="@style/sig_transparent_style" />
1031-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:53:13-57
1032        <activity
1032-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:54:9-60:69
1033            android:name="com.sigmob.sdk.base.common.AdActivity"
1033-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:55:13-65
1034            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
1034-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:56:13-83
1035            android:hardwareAccelerated="true"
1035-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:57:13-47
1036            android:multiprocess="true"
1036-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:58:13-40
1037            android:screenOrientation="behind"
1037-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:59:13-47
1038            android:theme="@android:style/Theme.Light.NoTitleBar" />
1038-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:60:13-66
1039        <activity
1039-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:61:9-67:69
1040            android:name="com.sigmob.sdk.base.common.PortraitAdActivity"
1040-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:62:13-73
1041            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
1041-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:63:13-83
1042            android:hardwareAccelerated="true"
1042-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:64:13-47
1043            android:multiprocess="true"
1043-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:65:13-40
1044            android:screenOrientation="sensorPortrait"
1044-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:66:13-55
1045            android:theme="@android:style/Theme.Light.NoTitleBar" />
1045-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:67:13-66
1046        <activity
1046-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:68:9-74:69
1047            android:name="com.sigmob.sdk.base.common.LandscapeAdActivity"
1047-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:69:13-74
1048            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
1048-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:70:13-83
1049            android:hardwareAccelerated="true"
1049-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:71:13-47
1050            android:multiprocess="true"
1050-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:72:13-40
1051            android:screenOrientation="sensorLandscape"
1051-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:73:13-56
1052            android:theme="@android:style/Theme.Light.NoTitleBar" />
1052-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:74:13-66
1053        <activity
1053-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:75:9-81:60
1054            android:name="com.sigmob.sdk.base.common.PortraitTransparentAdActivity"
1054-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:76:13-84
1055            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
1055-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:77:13-83
1056            android:hardwareAccelerated="true"
1056-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:78:13-47
1057            android:multiprocess="true"
1057-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:79:13-40
1058            android:screenOrientation="sensorPortrait"
1058-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:80:13-55
1059            android:theme="@style/sig_transparent_style" />
1059-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:81:13-57
1060        <activity
1060-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:82:9-88:60
1061            android:name="com.sigmob.sdk.base.common.LandscapeTransparentAdActivity"
1061-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:83:13-85
1062            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
1062-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:84:13-83
1063            android:hardwareAccelerated="true"
1063-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:85:13-47
1064            android:multiprocess="true"
1064-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:86:13-40
1065            android:screenOrientation="sensorLandscape"
1065-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:87:13-56
1066            android:theme="@style/sig_transparent_style" />
1066-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:88:13-57
1067
1068        <provider
1068-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
1069            android:name="androidx.startup.InitializationProvider"
1069-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
1070            android:authorities="com.example.concentration.androidx-startup"
1070-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
1071            android:exported="false" >
1071-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
1072            <meta-data
1072-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
1073                android:name="androidx.emoji2.text.EmojiCompatInitializer"
1073-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
1074                android:value="androidx.startup" />
1074-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
1075            <meta-data
1075-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
1076                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
1076-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
1077                android:value="androidx.startup" />
1077-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
1078            <meta-data
1078-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
1079                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
1079-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
1080                android:value="androidx.startup" />
1080-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
1081        </provider>
1082
1083        <receiver
1083-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
1084            android:name="androidx.profileinstaller.ProfileInstallReceiver"
1084-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
1085            android:directBootAware="false"
1085-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
1086            android:enabled="true"
1086-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
1087            android:exported="true"
1087-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
1088            android:permission="android.permission.DUMP" >
1088-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
1089            <intent-filter>
1089-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
1090                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
1090-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
1090-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
1091            </intent-filter>
1092            <intent-filter>
1092-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
1093                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
1093-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
1093-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
1094            </intent-filter>
1095            <intent-filter>
1095-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
1096                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
1096-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
1096-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
1097            </intent-filter>
1098            <intent-filter>
1098-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
1099                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
1099-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
1099-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
1100            </intent-filter>
1101        </receiver>
1102    </application>
1103
1104</manifest>
