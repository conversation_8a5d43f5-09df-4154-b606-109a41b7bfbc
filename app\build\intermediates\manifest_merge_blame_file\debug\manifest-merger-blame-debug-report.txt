1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.concentration"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\专注力训练\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\专注力训练\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\专注力训练\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\专注力训练\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\专注力训练\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\专注力训练\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- 可选权限 动态权限 -->
17    <!-- 获取设备标识IMEI。用于标识用户 -->
18    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
18-->C:\专注力训练\app\src\main\AndroidManifest.xml:12:5-75
18-->C:\专注力训练\app\src\main\AndroidManifest.xml:12:22-72
19    <!-- 读写存储权限 -->
20    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
20-->C:\专注力训练\app\src\main\AndroidManifest.xml:14:5-80
20-->C:\专注力训练\app\src\main\AndroidManifest.xml:14:22-77
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->C:\专注力训练\app\src\main\AndroidManifest.xml:15:5-81
21-->C:\专注力训练\app\src\main\AndroidManifest.xml:15:22-78
22    <!-- 定位权限 -->
23    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
23-->C:\专注力训练\app\src\main\AndroidManifest.xml:17:5-81
23-->C:\专注力训练\app\src\main\AndroidManifest.xml:17:22-78
24    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
24-->C:\专注力训练\app\src\main\AndroidManifest.xml:18:5-79
24-->C:\专注力训练\app\src\main\AndroidManifest.xml:18:22-76
25    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> <!-- 可选权限 -->
25-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:13:5-83
25-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:13:22-80
26    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- suppress DeprecatedClassUsageInspection -->
26-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:15:5-76
26-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:15:22-73
27    <uses-permission android:name="android.permission.GET_TASKS" /> <!-- 如果有视频相关的广告且使用textureView播放，请务必添加，否则黑屏 -->
27-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:17:5-68
27-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:17:22-65
28    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- MTG将通过此权限在Android R系统上判定广告对应的应用是否在用户的app上安装，避免投放错误的广告，以此提高用户的广告体验。 -->
28-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:25:5-68
28-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:25:22-65
29    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
29-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:28:5-77
29-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:28:22-74
30    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
30-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:14:5-79
30-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:14:22-76
31
32    <queries>
32-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:16:5-22:15
33        <intent>
33-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:17:9-21:18
34            <action android:name="android.intent.action.MAIN" />
34-->C:\专注力训练\app\src\main\AndroidManifest.xml:76:17-69
34-->C:\专注力训练\app\src\main\AndroidManifest.xml:76:25-66
35
36            <category android:name="android.intent.category.LAUNCHER" />
36-->C:\专注力训练\app\src\main\AndroidManifest.xml:77:17-77
36-->C:\专注力训练\app\src\main\AndroidManifest.xml:77:27-74
37        </intent>
38        <intent>
38-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:13:9-19:18
39            <action android:name="android.intent.action.VIEW" />
39-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:14:13-64
39-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:14:21-62
40
41            <category android:name="android.intent.category.BROWSABLE" />
41-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:16:13-73
41-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:16:23-71
42
43            <data android:scheme="https" />
43-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:18:13-43
43-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:18:19-41
44        </intent>
45    </queries>
46
47    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
47-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:8:5-79
47-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:8:22-76
48    <uses-permission android:name="android.permission.REORDER_TASKS" />
48-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:24:5-72
48-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:24:22-69
49    <uses-permission android:name="android.permission.VIBRATE" /> <!-- 动态壁纸权限 -->
49-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:25:5-66
49-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:25:22-63
50    <uses-permission android:name="android.permission.SET_WALLPAPER" />
50-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:20:5-72
50-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:20:22-69
51    <uses-permission android:name="android.permission.BLUETOOTH" />
51-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:24:5-68
51-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:24:22-65
52    <uses-permission android:name="com.asus.msa.SupplementaryDID.ACCESS" />
52-->[oaid_sdk_1.0.25.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01bc7fca446bdde30d72de23c77cdbfa\transformed\jetified-oaid_sdk_1.0.25\AndroidManifest.xml:13:5-76
52-->[oaid_sdk_1.0.25.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01bc7fca446bdde30d72de23c77cdbfa\transformed\jetified-oaid_sdk_1.0.25\AndroidManifest.xml:13:22-73
53    <uses-permission android:name="freemme.permission.msa" />
53-->[oaid_sdk_1.0.25.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01bc7fca446bdde30d72de23c77cdbfa\transformed\jetified-oaid_sdk_1.0.25\AndroidManifest.xml:15:5-62
53-->[oaid_sdk_1.0.25.aar] C:\Users\<USER>\.gradle\caches\transforms-3\01bc7fca446bdde30d72de23c77cdbfa\transformed\jetified-oaid_sdk_1.0.25\AndroidManifest.xml:15:22-59
54    <uses-permission android:name="com.example.concentration.openadsdk.permission.TT_PANGOLIN" />
54-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:7:5-88
54-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:7:22-86
55
56    <permission
56-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:10:5-119
57        android:name="com.example.concentration.openadsdk.permission.TT_PANGOLIN"
57-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:10:17-81
58        android:protectionLevel="signature" />
58-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:10:82-117
59    <permission
59-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
60        android:name="com.example.concentration.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
60-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
61        android:protectionLevel="signature" />
61-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
62
63    <uses-permission android:name="com.example.concentration.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
63-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
63-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
64
65    <application
65-->C:\专注力训练\app\src\main\AndroidManifest.xml:20:5-202:19
66        android:name="com.example.concentration.AdApplication"
66-->C:\专注力训练\app\src\main\AndroidManifest.xml:21:9-38
67        android:allowBackup="false"
67-->C:\专注力训练\app\src\main\AndroidManifest.xml:22:9-36
68        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
68-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
69        android:debuggable="true"
70        android:extractNativeLibs="false"
71        android:icon="@mipmap/ic_launcher"
71-->C:\专注力训练\app\src\main\AndroidManifest.xml:23:9-43
72        android:label="@string/app_name"
72-->C:\专注力训练\app\src\main\AndroidManifest.xml:24:9-41
73        android:requestLegacyExternalStorage="false"
73-->C:\专注力训练\app\src\main\AndroidManifest.xml:28:9-53
74        android:roundIcon="@mipmap/ic_launcher"
74-->C:\专注力训练\app\src\main\AndroidManifest.xml:25:9-48
75        android:supportsRtl="true"
75-->C:\专注力训练\app\src\main\AndroidManifest.xml:26:9-35
76        android:theme="@style/AppTheme"
76-->C:\专注力训练\app\src\main\AndroidManifest.xml:27:9-40
77        android:usesCleartextTraffic="true" >
77-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:16:18-53
78        <uses-library
78-->C:\专注力训练\app\src\main\AndroidManifest.xml:30:9-32:40
79            android:name="org.apache.http.legacy"
79-->C:\专注力训练\app\src\main\AndroidManifest.xml:31:13-50
80            android:required="false" />
80-->C:\专注力训练\app\src\main\AndroidManifest.xml:32:13-37
81
82        <provider
83            android:name="com.bytedance.sdk.openadsdk.multipro.TTMultiProvider"
83-->C:\专注力训练\app\src\main\AndroidManifest.xml:35:13-80
84            android:authorities="com.example.concentration.TTMultiProvider"
84-->C:\专注力训练\app\src\main\AndroidManifest.xml:36:13-67
85            android:exported="false" />
85-->C:\专注力训练\app\src\main\AndroidManifest.xml:37:13-37
86        <provider
87            android:name="com.bytedance.sdk.openadsdk.TTFileProvider"
87-->C:\专注力训练\app\src\main\AndroidManifest.xml:39:13-70
88            android:authorities="com.example.concentration.TTFileProvider"
88-->C:\专注力训练\app\src\main\AndroidManifest.xml:40:13-66
89            android:exported="false"
89-->C:\专注力训练\app\src\main\AndroidManifest.xml:41:13-37
90            android:grantUriPermissions="true" >
90-->C:\专注力训练\app\src\main\AndroidManifest.xml:42:13-47
91            <meta-data
91-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
92                android:name="android.support.FILE_PROVIDER_PATHS"
92-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
93                android:resource="@xml/anythink_bk_tt_file_path" />
93-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
94        </provider>
95        <provider
96            android:name="com.qq.e.comm.GDTFileProvider"
96-->C:\专注力训练\app\src\main\AndroidManifest.xml:49:13-57
97            android:authorities="com.example.concentration.gdt.fileprovider"
97-->C:\专注力训练\app\src\main\AndroidManifest.xml:50:13-68
98            android:exported="false"
98-->C:\专注力训练\app\src\main\AndroidManifest.xml:51:13-37
99            android:grantUriPermissions="true" >
99-->C:\专注力训练\app\src\main\AndroidManifest.xml:52:13-47
100            <meta-data
100-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
101                android:name="android.support.FILE_PROVIDER_PATHS"
101-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
102                android:resource="@xml/anythink_bk_gdt_file_path" />
102-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
103        </provider>
104        <provider
105            android:name="com.sigmob.sdk.SigmobFileV4Provider"
105-->C:\专注力训练\app\src\main\AndroidManifest.xml:59:13-63
106            android:authorities="com.example.concentration.sigprovider"
106-->C:\专注力训练\app\src\main\AndroidManifest.xml:60:13-63
107            android:exported="false"
107-->C:\专注力训练\app\src\main\AndroidManifest.xml:61:13-37
108            android:grantUriPermissions="true"
108-->C:\专注力训练\app\src\main\AndroidManifest.xml:63:13-47
109            android:initOrder="200" >
109-->C:\专注力训练\app\src\main\AndroidManifest.xml:62:13-36
110            <meta-data
110-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
111                android:name="android.support.FILE_PROVIDER_PATHS"
111-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
112                android:resource="@xml/anythink_bk_sigmob_file_path" />
112-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
113        </provider>
114
115        <!-- 开屏广告Activity -->
116        <activity
116-->C:\专注力训练\app\src\main\AndroidManifest.xml:70:9-79:20
117            android:name="com.example.concentration.SplashActivity"
117-->C:\专注力训练\app\src\main\AndroidManifest.xml:71:13-43
118            android:exported="true"
118-->C:\专注力训练\app\src\main\AndroidManifest.xml:74:13-36
119            android:screenOrientation="portrait"
119-->C:\专注力训练\app\src\main\AndroidManifest.xml:72:13-49
120            android:theme="@style/AppTheme" >
120-->C:\专注力训练\app\src\main\AndroidManifest.xml:73:13-44
121            <intent-filter>
121-->C:\专注力训练\app\src\main\AndroidManifest.xml:75:13-78:29
122                <action android:name="android.intent.action.MAIN" />
122-->C:\专注力训练\app\src\main\AndroidManifest.xml:76:17-69
122-->C:\专注力训练\app\src\main\AndroidManifest.xml:76:25-66
123
124                <category android:name="android.intent.category.LAUNCHER" />
124-->C:\专注力训练\app\src\main\AndroidManifest.xml:77:17-77
124-->C:\专注力训练\app\src\main\AndroidManifest.xml:77:27-74
125            </intent-filter>
126        </activity>
127        <activity
127-->C:\专注力训练\app\src\main\AndroidManifest.xml:81:9-84:40
128            android:name="com.example.concentration.MainActivity"
128-->C:\专注力训练\app\src\main\AndroidManifest.xml:81:19-47
129            android:exported="false"
129-->C:\专注力训练\app\src\main\AndroidManifest.xml:84:13-37
130            android:screenOrientation="portrait"
130-->C:\专注力训练\app\src\main\AndroidManifest.xml:82:13-49
131            android:theme="@style/AppTheme" />
131-->C:\专注力训练\app\src\main\AndroidManifest.xml:83:13-44
132        <activity
132-->C:\专注力训练\app\src\main\AndroidManifest.xml:86:9-90:40
133            android:name="com.example.concentration.SchulteGridActivity"
133-->C:\专注力训练\app\src\main\AndroidManifest.xml:87:13-48
134            android:exported="false"
134-->C:\专注力训练\app\src\main\AndroidManifest.xml:90:13-37
135            android:screenOrientation="portrait"
135-->C:\专注力训练\app\src\main\AndroidManifest.xml:88:13-49
136            android:theme="@style/AppTheme" />
136-->C:\专注力训练\app\src\main\AndroidManifest.xml:89:13-44
137        <activity
137-->C:\专注力训练\app\src\main\AndroidManifest.xml:91:9-95:40
138            android:name="com.example.concentration.ThemeSelectionActivity"
138-->C:\专注力训练\app\src\main\AndroidManifest.xml:92:13-51
139            android:exported="false"
139-->C:\专注力训练\app\src\main\AndroidManifest.xml:95:13-37
140            android:screenOrientation="portrait"
140-->C:\专注力训练\app\src\main\AndroidManifest.xml:93:13-49
141            android:theme="@style/AppTheme" />
141-->C:\专注力训练\app\src\main\AndroidManifest.xml:94:13-44
142        <activity
142-->C:\专注力训练\app\src\main\AndroidManifest.xml:96:9-100:40
143            android:name="com.example.concentration.CountdownActivity"
143-->C:\专注力训练\app\src\main\AndroidManifest.xml:97:13-46
144            android:exported="false"
144-->C:\专注力训练\app\src\main\AndroidManifest.xml:100:13-37
145            android:screenOrientation="portrait"
145-->C:\专注力训练\app\src\main\AndroidManifest.xml:98:13-49
146            android:theme="@style/AppTheme" />
146-->C:\专注力训练\app\src\main\AndroidManifest.xml:99:13-44
147        <activity
147-->C:\专注力训练\app\src\main\AndroidManifest.xml:101:9-105:40
148            android:name="com.example.concentration.GameActivity"
148-->C:\专注力训练\app\src\main\AndroidManifest.xml:102:13-41
149            android:exported="false"
149-->C:\专注力训练\app\src\main\AndroidManifest.xml:105:13-37
150            android:screenOrientation="portrait"
150-->C:\专注力训练\app\src\main\AndroidManifest.xml:103:13-49
151            android:theme="@style/AppTheme" />
151-->C:\专注力训练\app\src\main\AndroidManifest.xml:104:13-44
152        <activity
152-->C:\专注力训练\app\src\main\AndroidManifest.xml:107:9-111:40
153            android:name="com.example.concentration.ColorTrainingActivity"
153-->C:\专注力训练\app\src\main\AndroidManifest.xml:108:13-50
154            android:exported="false"
154-->C:\专注力训练\app\src\main\AndroidManifest.xml:111:13-37
155            android:screenOrientation="portrait"
155-->C:\专注力训练\app\src\main\AndroidManifest.xml:109:13-49
156            android:theme="@style/AppTheme" />
156-->C:\专注力训练\app\src\main\AndroidManifest.xml:110:13-44
157        <activity
157-->C:\专注力训练\app\src\main\AndroidManifest.xml:112:9-116:40
158            android:name="com.example.concentration.ColorGameActivity"
158-->C:\专注力训练\app\src\main\AndroidManifest.xml:113:13-46
159            android:exported="false"
159-->C:\专注力训练\app\src\main\AndroidManifest.xml:116:13-37
160            android:screenOrientation="portrait"
160-->C:\专注力训练\app\src\main\AndroidManifest.xml:114:13-49
161            android:theme="@style/AppTheme" />
161-->C:\专注力训练\app\src\main\AndroidManifest.xml:115:13-44
162        <activity
162-->C:\专注力训练\app\src\main\AndroidManifest.xml:117:9-121:40
163            android:name="com.example.concentration.MemoryGameMainActivity"
163-->C:\专注力训练\app\src\main\AndroidManifest.xml:118:13-51
164            android:exported="false"
164-->C:\专注力训练\app\src\main\AndroidManifest.xml:121:13-37
165            android:screenOrientation="portrait"
165-->C:\专注力训练\app\src\main\AndroidManifest.xml:119:13-49
166            android:theme="@style/AppTheme" />
166-->C:\专注力训练\app\src\main\AndroidManifest.xml:120:13-44
167        <activity
167-->C:\专注力训练\app\src\main\AndroidManifest.xml:122:9-126:40
168            android:name="com.example.concentration.MemoryGameActivity"
168-->C:\专注力训练\app\src\main\AndroidManifest.xml:123:13-47
169            android:exported="false"
169-->C:\专注力训练\app\src\main\AndroidManifest.xml:126:13-37
170            android:screenOrientation="portrait"
170-->C:\专注力训练\app\src\main\AndroidManifest.xml:124:13-49
171            android:theme="@style/AppTheme" />
171-->C:\专注力训练\app\src\main\AndroidManifest.xml:125:13-44
172        <activity
172-->C:\专注力训练\app\src\main\AndroidManifest.xml:127:9-131:40
173            android:name="com.example.concentration.CelebrationActivity"
173-->C:\专注力训练\app\src\main\AndroidManifest.xml:128:13-48
174            android:exported="false"
174-->C:\专注力训练\app\src\main\AndroidManifest.xml:131:13-37
175            android:screenOrientation="portrait"
175-->C:\专注力训练\app\src\main\AndroidManifest.xml:129:13-49
176            android:theme="@style/AppTheme" />
176-->C:\专注力训练\app\src\main\AndroidManifest.xml:130:13-44
177        <activity
177-->C:\专注力训练\app\src\main\AndroidManifest.xml:132:9-136:40
178            android:name="com.example.concentration.MultiplicationTableActivity"
178-->C:\专注力训练\app\src\main\AndroidManifest.xml:133:13-56
179            android:exported="false"
179-->C:\专注力训练\app\src\main\AndroidManifest.xml:136:13-37
180            android:screenOrientation="portrait"
180-->C:\专注力训练\app\src\main\AndroidManifest.xml:134:13-49
181            android:theme="@style/AppTheme" />
181-->C:\专注力训练\app\src\main\AndroidManifest.xml:135:13-44
182        <activity
182-->C:\专注力训练\app\src\main\AndroidManifest.xml:137:9-141:40
183            android:name="com.example.concentration.MultiplicationConceptActivity"
183-->C:\专注力训练\app\src\main\AndroidManifest.xml:138:13-58
184            android:exported="false"
184-->C:\专注力训练\app\src\main\AndroidManifest.xml:141:13-37
185            android:screenOrientation="portrait"
185-->C:\专注力训练\app\src\main\AndroidManifest.xml:139:13-49
186            android:theme="@style/AppTheme" />
186-->C:\专注力训练\app\src\main\AndroidManifest.xml:140:13-44
187        <activity
187-->C:\专注力训练\app\src\main\AndroidManifest.xml:142:9-146:40
188            android:name="com.example.concentration.ConceptVisualDemoActivity"
188-->C:\专注力训练\app\src\main\AndroidManifest.xml:143:13-54
189            android:exported="false"
189-->C:\专注力训练\app\src\main\AndroidManifest.xml:146:13-37
190            android:screenOrientation="portrait"
190-->C:\专注力训练\app\src\main\AndroidManifest.xml:144:13-49
191            android:theme="@style/AppTheme" />
191-->C:\专注力训练\app\src\main\AndroidManifest.xml:145:13-44
192        <activity
192-->C:\专注力训练\app\src\main\AndroidManifest.xml:147:9-151:40
193            android:name="com.example.concentration.ConceptInteractiveCountActivity"
193-->C:\专注力训练\app\src\main\AndroidManifest.xml:148:13-60
194            android:exported="false"
194-->C:\专注力训练\app\src\main\AndroidManifest.xml:151:13-37
195            android:screenOrientation="portrait"
195-->C:\专注力训练\app\src\main\AndroidManifest.xml:149:13-49
196            android:theme="@style/AppTheme" />
196-->C:\专注力训练\app\src\main\AndroidManifest.xml:150:13-44
197        <activity
197-->C:\专注力训练\app\src\main\AndroidManifest.xml:152:9-156:40
198            android:name="com.example.concentration.ConceptAnimationDemoActivity"
198-->C:\专注力训练\app\src\main\AndroidManifest.xml:153:13-57
199            android:exported="false"
199-->C:\专注力训练\app\src\main\AndroidManifest.xml:156:13-37
200            android:screenOrientation="portrait"
200-->C:\专注力训练\app\src\main\AndroidManifest.xml:154:13-49
201            android:theme="@style/AppTheme" />
201-->C:\专注力训练\app\src\main\AndroidManifest.xml:155:13-44
202        <activity
202-->C:\专注力训练\app\src\main\AndroidManifest.xml:157:9-161:40
203            android:name="com.example.concentration.ConceptTestGameActivity"
203-->C:\专注力训练\app\src\main\AndroidManifest.xml:158:13-52
204            android:exported="false"
204-->C:\专注力训练\app\src\main\AndroidManifest.xml:161:13-37
205            android:screenOrientation="portrait"
205-->C:\专注力训练\app\src\main\AndroidManifest.xml:159:13-49
206            android:theme="@style/AppTheme" />
206-->C:\专注力训练\app\src\main\AndroidManifest.xml:160:13-44
207        <activity
207-->C:\专注力训练\app\src\main\AndroidManifest.xml:162:9-166:40
208            android:name="com.example.concentration.MultiplicationNumberActivity"
208-->C:\专注力训练\app\src\main\AndroidManifest.xml:163:13-57
209            android:exported="false"
209-->C:\专注力训练\app\src\main\AndroidManifest.xml:166:13-37
210            android:screenOrientation="portrait"
210-->C:\专注力训练\app\src\main\AndroidManifest.xml:164:13-49
211            android:theme="@style/AppTheme" />
211-->C:\专注力训练\app\src\main\AndroidManifest.xml:165:13-44
212        <activity
212-->C:\专注力训练\app\src\main\AndroidManifest.xml:167:9-171:40
213            android:name="com.example.concentration.NumberLearningActivity"
213-->C:\专注力训练\app\src\main\AndroidManifest.xml:168:13-51
214            android:exported="false"
214-->C:\专注力训练\app\src\main\AndroidManifest.xml:171:13-37
215            android:screenOrientation="portrait"
215-->C:\专注力训练\app\src\main\AndroidManifest.xml:169:13-49
216            android:theme="@style/AppTheme" />
216-->C:\专注力训练\app\src\main\AndroidManifest.xml:170:13-44
217        <activity
217-->C:\专注力训练\app\src\main\AndroidManifest.xml:172:9-176:40
218            android:name="com.example.concentration.MemoryCardGameActivity"
218-->C:\专注力训练\app\src\main\AndroidManifest.xml:173:13-51
219            android:exported="false"
219-->C:\专注力训练\app\src\main\AndroidManifest.xml:176:13-37
220            android:screenOrientation="portrait"
220-->C:\专注力训练\app\src\main\AndroidManifest.xml:174:13-49
221            android:theme="@style/AppTheme" />
221-->C:\专注力训练\app\src\main\AndroidManifest.xml:175:13-44
222        <activity
222-->C:\专注力训练\app\src\main\AndroidManifest.xml:177:9-181:40
223            android:name="com.example.concentration.RhythmLearningActivity"
223-->C:\专注力训练\app\src\main\AndroidManifest.xml:178:13-51
224            android:exported="false"
224-->C:\专注力训练\app\src\main\AndroidManifest.xml:181:13-37
225            android:screenOrientation="portrait"
225-->C:\专注力训练\app\src\main\AndroidManifest.xml:179:13-49
226            android:theme="@style/AppTheme" />
226-->C:\专注力训练\app\src\main\AndroidManifest.xml:180:13-44
227        <activity
227-->C:\专注力训练\app\src\main\AndroidManifest.xml:182:9-186:40
228            android:name="com.example.concentration.PuzzleChallengeActivity"
228-->C:\专注力训练\app\src\main\AndroidManifest.xml:183:13-52
229            android:exported="false"
229-->C:\专注力训练\app\src\main\AndroidManifest.xml:186:13-37
230            android:screenOrientation="portrait"
230-->C:\专注力训练\app\src\main\AndroidManifest.xml:184:13-49
231            android:theme="@style/AppTheme" />
231-->C:\专注力训练\app\src\main\AndroidManifest.xml:185:13-44
232        <activity
232-->C:\专注力训练\app\src\main\AndroidManifest.xml:187:9-191:40
233            android:name="com.example.concentration.MultiplicationChallengeActivity"
233-->C:\专注力训练\app\src\main\AndroidManifest.xml:188:13-60
234            android:exported="false"
234-->C:\专注力训练\app\src\main\AndroidManifest.xml:191:13-37
235            android:screenOrientation="portrait"
235-->C:\专注力训练\app\src\main\AndroidManifest.xml:189:13-49
236            android:theme="@style/AppTheme" />
236-->C:\专注力训练\app\src\main\AndroidManifest.xml:190:13-44
237        <activity
237-->C:\专注力训练\app\src\main\AndroidManifest.xml:192:9-196:40
238            android:name="com.example.concentration.SettingsActivity"
238-->C:\专注力训练\app\src\main\AndroidManifest.xml:193:13-45
239            android:exported="false"
239-->C:\专注力训练\app\src\main\AndroidManifest.xml:196:13-37
240            android:screenOrientation="portrait"
240-->C:\专注力训练\app\src\main\AndroidManifest.xml:194:13-49
241            android:theme="@style/AppTheme" />
241-->C:\专注力训练\app\src\main\AndroidManifest.xml:195:13-44
242        <activity
242-->C:\专注力训练\app\src\main\AndroidManifest.xml:197:9-201:40
243            android:name="com.example.concentration.TutorialActivity"
243-->C:\专注力训练\app\src\main\AndroidManifest.xml:198:13-45
244            android:exported="false"
244-->C:\专注力训练\app\src\main\AndroidManifest.xml:201:13-37
245            android:screenOrientation="portrait"
245-->C:\专注力训练\app\src\main\AndroidManifest.xml:199:13-49
246            android:theme="@style/AppTheme" />
246-->C:\专注力训练\app\src\main\AndroidManifest.xml:200:13-44
247        <activity
247-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:17:9-20:47
248            android:name="com.alliance.ssp.ad.activity.SAAllianceWebViewActivity"
248-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:18:13-82
249            android:configChanges="orientation|screenSize|keyboardHidden"
249-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:19:13-74
250            android:launchMode="singleTask" />
250-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:20:13-44
251        <activity
251-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:21:9-25:52
252            android:name="com.alliance.ssp.ad.activity.NMRewardVideoActivity"
252-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:22:13-78
253            android:configChanges="orientation|screenSize|keyboardHidden"
253-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:23:13-74
254            android:launchMode="singleTask"
254-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:24:13-44
255            android:screenOrientation="portrait" />
255-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:25:13-49
256        <activity
256-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:26:9-30:64
257            android:name="com.alliance.ssp.ad.activity.AppInfoViewActivity"
257-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:27:13-76
258            android:configChanges="orientation|screenSize|keyboardHidden"
258-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:28:13-74
259            android:launchMode="singleTask"
259-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:29:13-44
260            android:theme="@android:style/Theme.Translucent" />
260-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:30:13-61
261
262        <service
262-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:32:9-34:40
263            android:name="com.alliance.ssp.ad.service.YTSDKDownloadService"
263-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:33:13-76
264            android:exported="false" />
264-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:34:13-37
265
266        <activity
266-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:36:9-40:64
267            android:name="com.alliance.ssp.ad.activity.DeepLinkProxyActivity"
267-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:37:13-78
268            android:configChanges="orientation|screenSize|keyboardHidden"
268-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:38:13-74
269            android:launchMode="singleTask"
269-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:39:13-44
270            android:theme="@android:style/Theme.Translucent" />
270-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:40:13-61
271
272        <provider
272-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:42:9-50:20
273            android:name="com.alliance.ssp.ad.utils.NMSSPFileProvider"
273-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:43:13-71
274            android:authorities="com.example.concentration.NMSSPFileProvider"
274-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:44:13-69
275            android:exported="false"
275-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:45:13-37
276            android:grantUriPermissions="true" >
276-->[adalliance_adn_sdk.3.11.3.aar] C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\AndroidManifest.xml:46:13-47
277            <meta-data
277-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
278                android:name="android.support.FILE_PROVIDER_PATHS"
278-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
279                android:resource="@xml/nmssp_file_path" />
279-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
280        </provider>
281
282        <activity
282-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:31:9-35:52
283            android:name="com.ads.admob.saas.SaasH5Activity"
283-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:32:13-61
284            android:configChanges="keyboard|orientation|screenSize"
284-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:33:13-68
285            android:exported="false"
285-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:34:13-37
286            android:screenOrientation="portrait" />
286-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:35:13-49
287        <activity
287-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:36:9-40:52
288            android:name="com.ads.admob.saas.H5Activity"
288-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:37:13-57
289            android:configChanges="keyboard|orientation|screenSize"
289-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:38:13-68
290            android:exported="false"
290-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:39:13-37
291            android:screenOrientation="portrait" />
291-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:40:13-49
292        <activity
292-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:41:9-45:52
293            android:name="com.ads.admob.saas.GameH5Activity"
293-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:42:13-61
294            android:configChanges="keyboard|orientation|screenSize"
294-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:43:13-68
295            android:exported="false"
295-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:44:13-37
296            android:screenOrientation="portrait" />
296-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:45:13-49
297        <activity
297-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:46:9-50:52
298            android:name="com.ads.admob.saas.YmActivity"
298-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:47:13-57
299            android:configChanges="keyboard|orientation|screenSize"
299-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:48:13-68
300            android:exported="false"
300-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:49:13-37
301            android:screenOrientation="portrait" />
301-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:50:13-49
302        <activity
302-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:51:9-55:52
303            android:name="com.ads.admob.saas.VideoFragmentActivity"
303-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:52:13-68
304            android:configChanges="keyboard|orientation|screenSize"
304-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:53:13-68
305            android:exported="false"
305-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:54:13-37
306            android:screenOrientation="portrait" />
306-->[admob-20250529.aar] C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\AndroidManifest.xml:55:13-49
307        <activity
307-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:18:9-21:86
308            android:name="com.anythink.china.activity.TransparentActivity"
308-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:19:13-75
309            android:exported="false"
309-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:20:13-37
310            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
310-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:21:13-83
311        <activity
311-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:22:9-25:86
312            android:name="com.anythink.dlopt.activity.ApkConfirmDialogActivity"
312-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:23:13-80
313            android:exported="false"
313-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:24:13-37
314            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
314-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:25:13-83
315
316        <provider
316-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:27:9-35:20
317            android:name="com.anythink.dlopt.common.ApkFileProvider"
317-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:28:13-69
318            android:authorities="com.example.concentration.anythink.fileProvider"
318-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:29:13-73
319            android:exported="false"
319-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:30:13-37
320            android:grantUriPermissions="true" >
320-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:31:13-47
321            <meta-data
321-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
322                android:name="android.support.FILE_PROVIDER_PATHS"
322-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
323                android:resource="@xml/anythink_file_paths" />
323-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
324        </provider>
325
326        <receiver
326-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:37:9-44:20
327            android:name="com.anythink.dlopt.common.NotificationBroadcastReceiver"
327-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:38:13-83
328            android:exported="false" >
328-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:39:13-37
329            <intent-filter>
329-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:40:13-43:29
330                <action android:name="anythink_action_notification_click" />
330-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:41:17-77
330-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:41:25-74
331                <action android:name="anythink_action_notification_cancel" />
331-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:42:17-78
331-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:42:25-75
332            </intent-filter>
333        </receiver>
334
335        <service
335-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:46:9-48:40
336            android:name="com.anythink.dlopt.common.service.ApkDownloadService"
336-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:47:13-80
337            android:exported="false" />
337-->[anythink_china_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\33a65179fb977c89f954a242644f692a\transformed\jetified-anythink_china_core\AndroidManifest.xml:48:13-37
338
339        <activity
339-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:25:9-30:74
340            android:name="com.anythink.core.activity.AnyThinkGdprAuthActivity"
340-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:26:13-79
341            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
341-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:27:13-87
342            android:exported="false"
342-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:28:13-37
343            android:launchMode="singleTask"
343-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:29:13-44
344            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
344-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:30:13-71
345        <activity
345-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:31:9-36:74
346            android:name="com.anythink.basead.ui.ATLandscapeActivity"
346-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:32:13-70
347            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
347-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:33:13-87
348            android:exported="false"
348-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:34:13-37
349            android:launchMode="standard"
349-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:35:13-42
350            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
350-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:36:13-71
351        <activity
351-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:37:9-42:74
352            android:name="com.anythink.basead.ui.ATPortraitActivity"
352-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:38:13-69
353            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
353-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:39:13-87
354            android:exported="false"
354-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:40:13-37
355            android:launchMode="standard"
355-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:41:13-42
356            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
356-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:42:13-71
357        <activity
357-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:43:9-48:67
358            android:name="com.anythink.basead.ui.ATLandscapeTranslucentActivity"
358-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:44:13-81
359            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
359-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:45:13-87
360            android:exported="false"
360-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:46:13-37
361            android:launchMode="standard"
361-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:47:13-42
362            android:theme="@style/anythink_myoffer_half_screen" />
362-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:48:13-64
363        <activity
363-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:49:9-54:67
364            android:name="com.anythink.basead.ui.ATPortraitTranslucentActivity"
364-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:50:13-80
365            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
365-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:51:13-87
366            android:exported="false"
366-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:52:13-37
367            android:launchMode="standard"
367-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:53:13-42
368            android:theme="@style/anythink_myoffer_half_screen" />
368-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:54:13-64
369        <activity
369-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:55:9-61:58
370            android:name="com.anythink.core.basead.ui.web.WebLandPageActivity"
370-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:56:13-79
371            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
371-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:57:13-87
372            android:exported="false"
372-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:58:13-37
373            android:launchMode="singleTop"
373-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:59:13-43
374            android:theme="@android:style/Theme.Light.NoTitleBar"
374-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:60:13-66
375            android:windowSoftInputMode="adjustResize" />
375-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:61:13-55
376        <activity
376-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:62:9-65:86
377            android:name="com.anythink.basead.ui.RewardExitConfirmDialogActivity"
377-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:63:13-82
378            android:exported="false"
378-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:64:13-37
379            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
379-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:65:13-83
380        <activity
380-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:66:9-71:74
381            android:name="com.anythink.basead.ui.activity.ATMixSplashActivity"
381-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:67:13-79
382            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
382-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:68:13-83
383            android:exported="false"
383-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:69:13-37
384            android:launchMode="standard"
384-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:70:13-42
385            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- 如果targetSdkVersion设置值>=24，则强烈建议添加以下provider，否则会影响app变现 -->
385-->[anythink_core.aar] C:\Users\<USER>\.gradle\caches\transforms-3\369091b8ae125b1df315664f32e06581\transformed\jetified-anythink_core\AndroidManifest.xml:71:13-71
386        <!-- android:authorities="${applicationId}.bd.provider" authorities中${applicationId}部分必须替换成app自己的包名 -->
387        <!-- 原来的FileProvider在新版本中改为BdFileProvider,继承自v4的FileProvider,需要在应用内引用support-v4包 -->
388        <provider
388-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:14:9-22:20
389            android:name="com.baidu.mobads.sdk.api.BdFileProvider"
389-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:15:13-67
390            android:authorities="com.example.concentration.bd.provider"
390-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:16:13-63
391            android:exported="false"
391-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:17:13-37
392            android:grantUriPermissions="true" >
392-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:18:13-47
393            <meta-data
393-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
394                android:name="android.support.FILE_PROVIDER_PATHS"
394-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
395                android:resource="@xml/bd_file_paths" />
395-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
396        </provider> <!-- 落地页配置 -->
397        <activity
397-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:24:9-27:63
398            android:name="com.baidu.mobads.sdk.api.AppActivity"
398-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:25:13-64
399            android:configChanges="screenSize|keyboard|keyboardHidden|orientation"
399-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:26:13-83
400            android:theme="@android:style/Theme.NoTitleBar" /> <!-- 激励视频、全屏视频配置 -->
400-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:27:13-60
401        <activity
401-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:29:9-32:75
402            android:name="com.baidu.mobads.sdk.api.MobRewardVideoActivity"
402-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:30:13-75
403            android:configChanges="screenSize|orientation|keyboardHidden|uiMode"
403-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:31:13-81
404            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
404-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:32:13-72
405        <activity
405-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:33:9-36:75
406            android:name="com.baidu.mobads.sdk.api.BdShellActivity"
406-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:34:13-68
407            android:configChanges="screenSize|keyboard|keyboardHidden|orientation|smallestScreenSize|screenLayout"
407-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:35:13-115
408            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
408-->[Baidu_MobAds_SDK-release_v9.3912.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\AndroidManifest.xml:36:13-72
409        <activity
409-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:27:9-31:50
410            android:name="com.beizi.ad.AdActivity"
410-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:28:13-51
411            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
411-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:29:13-122
412            android:exported="false"
412-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:30:13-37
413            android:hardwareAccelerated="true" />
413-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:31:13-47
414        <activity
414-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:32:9-35:61
415            android:name="com.beizi.ad.internal.activity.BeiZiInterstitialActivity"
415-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:33:13-84
416            android:exported="false"
416-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:34:13-37
417            android:theme="@style/BeiZiTheme.Transparent" />
417-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:35:13-58
418        <activity
418-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:36:9-38:40
419            android:name="com.beizi.ad.internal.activity.DownloadAppInfoActivity"
419-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:37:13-82
420            android:exported="false" />
420-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:38:13-37
421        <activity
421-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:39:9-42:61
422            android:name="com.beizi.ad.internal.activity.BeiZiDownloadDialogActivity"
422-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:40:13-86
423            android:exported="false"
423-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:41:13-37
424            android:theme="@style/BeiZiTheme.Transparent" />
424-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:42:13-58
425
426        <service
426-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:44:9-46:40
427            android:name="com.beizi.ad.DownloadService"
427-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:45:13-56
428            android:exported="false" /> <!-- 声明SDK所需要的组件 -->
428-->[beizi_ad_sdk_3.5.0.33.aar] C:\Users\<USER>\.gradle\caches\transforms-3\36a3fbf177a6c7f0be6fedd6278b83fd\transformed\jetified-beizi_ad_sdk_3.5.0.33\AndroidManifest.xml:46:13-37
429        <service
429-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:32:9-35:43
430            android:name="com.qq.e.comm.DownloadService"
430-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:33:13-57
431            android:exported="false"
431-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:34:13-37
432            android:multiprocess="true" /> <!-- 请开发者注意字母的大小写，ADActivity，而不是AdActivity -->
432-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:35:13-40
433        <activity
433-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:37:9-41:43
434            android:name="com.qq.e.ads.ADActivity"
434-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:38:13-51
435            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
435-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:39:13-83
436            android:exported="false"
436-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:40:13-37
437            android:multiprocess="true" />
437-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:41:13-40
438        <activity
438-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:42:9-48:69
439            android:name="com.qq.e.ads.PortraitADActivity"
439-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:43:13-59
440            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
440-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:44:13-83
441            android:exported="false"
441-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:45:13-37
442            android:multiprocess="true"
442-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:46:13-40
443            android:screenOrientation="portrait"
443-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:47:13-49
444            android:theme="@android:style/Theme.Light.NoTitleBar" />
444-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:48:13-66
445        <activity
445-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:49:9-55:69
446            android:name="com.qq.e.ads.LandscapeADActivity"
446-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:50:13-60
447            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
447-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:51:13-83
448            android:exported="false"
448-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:52:13-37
449            android:multiprocess="true"
449-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:53:13-40
450            android:screenOrientation="sensorLandscape"
450-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:54:13-56
451            android:theme="@android:style/Theme.Light.NoTitleBar" /> <!-- 用于激励视频可选广告的竖屏透明背景activity -->
451-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:55:13-66
452        <activity
452-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:57:9-66:20
453            android:name="com.qq.e.ads.RewardvideoPortraitADActivity"
453-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:58:13-70
454            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
454-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:59:13-83
455            android:exported="false"
455-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:60:13-37
456            android:multiprocess="true"
456-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:61:13-40
457            android:theme="@android:style/Theme.Light.NoTitleBar" >
457-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:62:13-66
458            <meta-data
458-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:63:13-65:40
459                android:name="android.notch_support"
459-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:64:17-53
460                android:value="true" />
460-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:65:17-37
461        </activity> <!-- 用于激励视频可选广告的横屏透明背景activity -->
462        <activity
462-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:68:9-78:20
463            android:name="com.qq.e.ads.RewardvideoLandscapeADActivity"
463-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:69:13-71
464            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
464-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:70:13-83
465            android:exported="false"
465-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:71:13-37
466            android:multiprocess="true"
466-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:72:13-40
467            android:screenOrientation="landscape"
467-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:73:13-50
468            android:theme="@android:style/Theme.Light.NoTitleBar" >
468-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:74:13-66
469            <meta-data
469-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:63:13-65:40
470                android:name="android.notch_support"
470-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:64:17-53
471                android:value="true" />
471-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:65:17-37
472        </activity>
473        <activity
473-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:79:9-83:86
474            android:name="com.qq.e.ads.DialogActivity"
474-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:80:13-55
475            android:exported="false"
475-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:81:13-37
476            android:multiprocess="true"
476-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:82:13-40
477            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
477-->[GDTSDK.unionNormal.4.640.1510.aar] C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\AndroidManifest.xml:83:13-83
478        <activity
478-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:28:9-32:69
479            android:name="com.kwad.sdk.api.proxy.app.AdWebViewActivity"
479-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:29:13-72
480            android:configChanges="keyboardHidden|orientation|screenSize"
480-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:30:13-74
481            android:screenOrientation="portrait"
481-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:31:13-49
482            android:theme="@android:style/Theme.Light.NoTitleBar" />
482-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:32:13-66
483        <activity
483-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:33:9-37:80
484            android:name="com.kwad.sdk.api.proxy.app.KsFullScreenVideoActivity"
484-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:34:13-80
485            android:configChanges="keyboardHidden|orientation|screenSize"
485-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:35:13-74
486            android:screenOrientation="behind"
486-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:36:13-47
487            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
487-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:37:13-77
488        <activity
488-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:38:9-42:80
489            android:name="com.kwad.sdk.api.proxy.app.KsFullScreenLandScapeVideoActivity"
489-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:39:13-89
490            android:configChanges="keyboardHidden|orientation|screenSize"
490-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:40:13-74
491            android:screenOrientation="landscape"
491-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:41:13-50
492            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
492-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:42:13-77
493        <activity
493-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:43:9-47:80
494            android:name="com.kwad.sdk.api.proxy.app.KsRewardVideoActivity"
494-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:44:13-76
495            android:configChanges="keyboardHidden|orientation|screenSize"
495-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:45:13-74
496            android:screenOrientation="portrait"
496-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:46:13-49
497            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
497-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:47:13-77
498        <activity
498-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:48:9-52:80
499            android:name="com.kwad.sdk.api.proxy.app.KSRewardLandScapeVideoActivity"
499-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:49:13-85
500            android:configChanges="keyboardHidden|orientation|screenSize"
500-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:50:13-74
501            android:screenOrientation="landscape"
501-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:51:13-50
502            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
502-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:52:13-77
503        <activity
503-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:53:9-57:80
504            android:name="com.kwad.sdk.api.proxy.app.FeedDownloadActivity"
504-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:54:13-75
505            android:configChanges="keyboardHidden|orientation|screenSize"
505-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:55:13-74
506            android:screenOrientation="portrait"
506-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:56:13-49
507            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
507-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:57:13-77
508        <activity
508-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:58:9-62:69
509            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$KsTrendsActivity"
509-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:59:13-92
510            android:configChanges="keyboardHidden|orientation|screenSize"
510-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:60:13-74
511            android:screenOrientation="portrait"
511-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:61:13-49
512            android:theme="@android:style/Theme.Light.NoTitleBar" />
512-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:62:13-66
513        <activity
513-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:63:9-66:52
514            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$ProfileHomeActivity"
514-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:64:13-95
515            android:configChanges="keyboardHidden|orientation|screenSize"
515-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:65:13-74
516            android:screenOrientation="portrait" />
516-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:66:13-49
517        <activity
517-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:67:9-70:52
518            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$GoodsPlayBackActivity"
518-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:68:13-97
519            android:configChanges="keyboardHidden|orientation|screenSize"
519-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:69:13-74
520            android:screenOrientation="portrait" />
520-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:70:13-49
521        <activity
521-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:71:9-74:52
522            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$ProfileVideoDetailActivity"
522-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:72:13-102
523            android:configChanges="keyboardHidden|orientation|screenSize"
523-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:73:13-74
524            android:screenOrientation="portrait" />
524-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:74:13-49
525        <activity
525-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:75:9-78:52
526            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$TubeProfileActivity"
526-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:76:13-95
527            android:configChanges="keyboardHidden|orientation|screenSize"
527-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:77:13-74
528            android:screenOrientation="portrait" />
528-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:78:13-49
529        <activity
529-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:79:9-82:52
530            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$ChannelDetailActivity"
530-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:80:13-97
531            android:configChanges="keyboardHidden|orientation|screenSize"
531-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:81:13-74
532            android:screenOrientation="portrait" />
532-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:82:13-49
533        <activity
533-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:83:9-86:52
534            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$TubeDetailActivity"
534-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:84:13-94
535            android:configChanges="keyboardHidden|orientation|screenSize"
535-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:85:13-74
536            android:screenOrientation="portrait" />
536-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:86:13-49
537        <activity
537-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:87:9-90:52
538            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$EpisodeDetailActivity"
538-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:88:13-97
539            android:configChanges="keyboardHidden|orientation|screenSize"
539-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:89:13-74
540            android:screenOrientation="portrait" />
540-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:90:13-49
541        <activity
541-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:91:9-95:80
542            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity1"
542-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:92:13-93
543            android:configChanges="keyboardHidden|orientation|screenSize"
543-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:93:13-74
544            android:screenOrientation="portrait"
544-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:94:13-49
545            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
545-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:95:13-77
546        <activity
546-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:96:9-100:80
547            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity2"
547-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:97:13-93
548            android:configChanges="keyboardHidden|orientation|screenSize"
548-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:98:13-74
549            android:screenOrientation="portrait"
549-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:99:13-49
550            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
550-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:100:13-77
551        <activity
551-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:101:9-105:80
552            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity3"
552-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:102:13-93
553            android:configChanges="keyboardHidden|orientation|screenSize"
553-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:103:13-74
554            android:screenOrientation="portrait"
554-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:104:13-49
555            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
555-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:105:13-77
556        <activity
556-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:106:9-109:52
557            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity4"
557-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:107:13-93
558            android:configChanges="keyboardHidden|orientation|screenSize"
558-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:108:13-74
559            android:screenOrientation="portrait" />
559-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:109:13-49
560        <activity
560-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:110:9-113:52
561            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity5"
561-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:111:13-93
562            android:configChanges="keyboardHidden|orientation|screenSize"
562-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:112:13-74
563            android:screenOrientation="portrait" />
563-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:113:13-49
564        <activity
564-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:114:9-117:52
565            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity6"
565-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:115:13-93
566            android:configChanges="keyboardHidden|orientation|screenSize"
566-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:116:13-74
567            android:screenOrientation="portrait" />
567-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:117:13-49
568        <activity
568-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:118:9-121:52
569            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity7"
569-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:119:13-93
570            android:configChanges="keyboardHidden|orientation|screenSize"
570-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:120:13-74
571            android:screenOrientation="portrait" />
571-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:121:13-49
572        <activity
572-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:122:9-125:52
573            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity8"
573-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:123:13-93
574            android:configChanges="keyboardHidden|orientation|screenSize"
574-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:124:13-74
575            android:screenOrientation="portrait" />
575-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:125:13-49
576        <activity
576-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:126:9-129:52
577            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity9"
577-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:127:13-93
578            android:configChanges="keyboardHidden|orientation|screenSize"
578-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:128:13-74
579            android:screenOrientation="portrait" />
579-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:129:13-49
580        <activity
580-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:130:9-133:52
581            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity10"
581-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:131:13-94
582            android:configChanges="keyboardHidden|orientation|screenSize"
582-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:132:13-74
583            android:screenOrientation="portrait" />
583-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:133:13-49
584        <activity
584-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:134:9-138:52
585            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleTop1"
585-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:135:13-102
586            android:configChanges="keyboardHidden|orientation|screenSize"
586-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:136:13-74
587            android:launchMode="singleTop"
587-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:137:13-43
588            android:screenOrientation="portrait" />
588-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:138:13-49
589        <activity
589-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:139:9-143:52
590            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleTop2"
590-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:140:13-102
591            android:configChanges="keyboardHidden|orientation|screenSize"
591-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:141:13-74
592            android:launchMode="singleTop"
592-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:142:13-43
593            android:screenOrientation="portrait" />
593-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:143:13-49
594        <activity
594-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:144:9-148:52
595            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleInstance1"
595-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:145:13-107
596            android:configChanges="keyboardHidden|orientation|screenSize"
596-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:146:13-74
597            android:launchMode="singleInstance"
597-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:147:13-48
598            android:screenOrientation="portrait" />
598-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:148:13-49
599        <activity
599-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:149:9-153:52
600            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleInstance2"
600-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:150:13-107
601            android:configChanges="keyboardHidden|orientation|screenSize"
601-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:151:13-74
602            android:launchMode="singleInstance"
602-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:152:13-48
603            android:screenOrientation="portrait" />
603-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:153:13-49
604        <activity
604-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:154:9-159:80
605            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$DeveloperConfigActivity"
605-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:155:13-99
606            android:configChanges="keyboardHidden|orientation|screenSize"
606-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:156:13-74
607            android:launchMode="singleInstance"
607-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:157:13-48
608            android:screenOrientation="portrait"
608-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:158:13-49
609            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
609-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:159:13-77
610
611        <service android:name="com.kwad.sdk.api.proxy.app.FileDownloadService$SharedMainProcessService" />
611-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:161:9-107
611-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:161:18-104
612        <service android:name="com.kwad.sdk.api.proxy.app.FileDownloadService$SeparateProcessService" />
612-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:162:9-105
612-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:162:18-102
613        <service
613-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:163:9-165:40
614            android:name="com.kwad.sdk.api.proxy.app.DownloadService"
614-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:164:13-70
615            android:exported="false" />
615-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:165:13-37
616        <service
616-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:166:9-169:47
617            android:name="com.kwad.sdk.api.proxy.app.ServiceProxyRemote"
617-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:167:13-73
618            android:exported="false"
618-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:168:13-37
619            android:process=":kssdk_remote" />
619-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:169:13-44
620        <service
620-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:170:9-181:19
621            android:name="com.kwad.sdk.api.proxy.VideoWallpaperService"
621-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:171:13-72
622            android:exported="true"
622-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:172:13-36
623            android:permission="android.permission.BIND_WALLPAPER" >
623-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:173:13-67
624            <intent-filter android:priority="1000" >
624-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:174:13-176:29
624-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:174:28-51
625                <action android:name="android.service.wallpaper.WallpaperService" />
625-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:175:17-85
625-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:175:25-82
626            </intent-filter>
627
628            <meta-data
628-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:178:13-180:58
629                android:name="android.service.wallpaper"
629-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:179:17-57
630                android:resource="@xml/ksad_wallpaper" />
630-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:180:17-55
631        </service>
632
633        <provider
633-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:183:9-191:20
634            android:name="com.kwad.sdk.api.proxy.app.AdSdkFileProvider"
634-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:184:13-72
635            android:authorities="com.example.concentration.adFileProvider"
635-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:185:13-66
636            android:exported="false"
636-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:186:13-37
637            android:grantUriPermissions="true" >
637-->[kssdk-ct-********.aar] C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-********\AndroidManifest.xml:187:13-47
638            <meta-data
638-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
639                android:name="android.support.FILE_PROVIDER_PATHS"
639-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
640                android:resource="@xml/ksad_file_paths" />
640-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
641        </provider>
642
643        <meta-data
643-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:32:9-291
644            android:name="ZEUS_PLUGIN_PANGLE"
644-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:32:20-53
645            android:value="{                 apiVersionCode:6841,                 packageName:com.byted.pangle,                 minPluginVersion:6841,                 internalPath:**********,                 internalVersionCode:6841             }" />
645-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:32:54-289
646        <meta-data
646-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:33:9-248
647            android:name="ZEUS_PLUGIN_com.byted.csj.ext"
647-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:33:20-64
648            android:value="{apiVersionCode:999,packageName:com.byted.csj.ext,minPluginVersion:1000,maxPluginVersion:999999999,internalPath:&apos;&apos;,internalVersionCode:-1, appKey:&apos;&apos;,appSecretKey:&apos;&apos;}" />
648-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:33:65-246
649
650        <activity
650-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:9-331
651            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Activity"
651-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:19-98
652            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
652-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:99-171
653            android:exported="false"
653-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:172-196
654            android:launchMode="standard"
654-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:197-226
655            android:theme="@android:style/Theme.NoTitleBar"
655-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:227-274
656            android:windowSoftInputMode="adjustResize|stateHidden" />
656-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:35:275-329
657        <activity
657-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:9-384
658            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Portrait_Activity"
658-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:19-107
659            android:configChanges="orientation|keyboardHidden|navigation|screenSize|uiMode"
659-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:108-187
660            android:exported="false"
660-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:188-212
661            android:launchMode="standard"
661-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:213-242
662            android:screenOrientation="portrait"
662-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:243-279
663            android:theme="@android:style/Theme.NoTitleBar"
663-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:280-327
664            android:windowSoftInputMode="adjustResize|stateHidden" />
664-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:36:328-382
665        <activity
665-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:9-334
666            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Activity_T"
666-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:19-100
667            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
667-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:101-173
668            android:exported="false"
668-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:174-198
669            android:launchMode="standard"
669-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:199-228
670            android:theme="@android:style/Theme.Translucent"
670-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:229-277
671            android:windowSoftInputMode="adjustResize|stateHidden" />
671-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:37:278-332
672        <activity
672-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:9-386
673            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Landscape_Activity"
673-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:19-108
674            android:configChanges="orientation|keyboardHidden|navigation|screenSize|uiMode"
674-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:109-188
675            android:exported="false"
675-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:189-213
676            android:launchMode="standard"
676-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:214-243
677            android:screenOrientation="landscape"
677-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:244-281
678            android:theme="@android:style/Theme.NoTitleBar"
678-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:282-329
679            android:windowSoftInputMode="adjustResize|stateHidden" />
679-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:38:330-384
680        <activity
680-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:9-292
681            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_Activity"
681-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:19-89
682            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
682-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:90-162
683            android:exported="false"
683-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:163-187
684            android:theme="@android:style/Theme.NoTitleBar"
684-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:188-235
685            android:windowSoftInputMode="adjustResize|stateHidden" />
685-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:39:236-290
686        <activity
686-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:9-338
687            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_SingleTask_Activity_T"
687-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:19-102
688            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
688-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:103-175
689            android:exported="false"
689-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:176-200
690            android:launchMode="singleTask"
690-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:201-232
691            android:theme="@android:style/Theme.Translucent"
691-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:233-281
692            android:windowSoftInputMode="adjustResize|stateHidden" />
692-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:40:282-336
693        <activity
693-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:9-287
694            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_SingleTask_Activity"
694-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:19-100
695            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
695-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:101-173
696            android:exported="false"
696-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:174-198
697            android:launchMode="singleTask"
697-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:199-230
698            android:windowSoftInputMode="adjustResize|stateHidden" />
698-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:41:231-285
699
700        <provider
700-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:43:9-260
701            android:name="com.bytedance.sdk.openadsdk.stub.server.DownloaderServerManager"
701-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:43:19-97
702            android:authorities="com.example.concentration.pangle.servermanager.downloader.com.bytedance.sdk.openadsdk.adhost"
702-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:43:98-203
703            android:exported="false"
703-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:43:204-228
704            android:process=":downloader" />
704-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:43:229-258
705        <provider
705-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:44:9-183
706            android:name="com.bytedance.sdk.openadsdk.stub.server.MainServerManager"
706-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:44:19-91
707            android:authorities="com.example.concentration.pangle.servermanager.main"
707-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:44:92-156
708            android:exported="false" />
708-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:44:157-181
709        <provider
709-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:47:9-181
710            android:name="com.bytedance.pangle.provider.MainProcessProviderProxy"
710-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:47:19-88
711            android:authorities="com.example.concentration.pangle.provider.proxy.main"
711-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:47:89-154
712            android:exported="false" />
712-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:47:155-179
713        <provider
713-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:48:9-188
714            android:name="com.bytedance.pangle.FileProvider"
714-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:48:19-67
715            android:authorities="com.example.concentration.pangle.fileprovider"
715-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:48:68-126
716            android:exported="false"
716-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:48:162-186
717            android:grantUriPermissions="true" />
717-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:48:127-161
718
719        <activity
719-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:50:9-134
720            android:name="com.ss.android.downloadlib.addownload.compliance.AppPrivacyPolicyActivity"
720-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:50:19-107
721            android:exported="false" />
721-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:50:108-132
722        <activity
722-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:51:9-131
723            android:name="com.ss.android.downloadlib.addownload.compliance.AppDetailInfoActivity"
723-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:51:19-104
724            android:exported="false" />
724-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:51:105-129
725        <activity
725-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:52:9-207
726            android:name="com.ss.android.downloadlib.activity.TTDelegateActivity"
726-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:52:19-88
727            android:exported="false"
727-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:52:181-205
728            android:launchMode="singleTask"
728-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:52:89-120
729            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
729-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:52:121-180
730        <activity
730-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:53:9-205
731            android:name="com.ss.android.downloadlib.activity.JumpKllkActivity"
731-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:53:19-86
732            android:exported="false"
732-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:53:179-203
733            android:launchMode="singleTask"
733-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:53:87-118
734            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
734-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:53:119-178
735
736        <receiver
736-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:55:9-118
737            android:name="com.ss.android.downloadlib.core.download.DownloadReceiver"
737-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:55:19-91
738            android:exported="false" />
738-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:55:92-116
739
740        <service
740-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:56:9-150
741            android:name="com.ss.android.socialbase.appdownloader.DownloadHandlerService"
741-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:56:18-95
742            android:exported="false"
742-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:56:124-148
743            android:stopWithTask="true" />
743-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:56:96-123
744
745        <activity
745-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:58:9-208
746            android:name="com.ss.android.socialbase.appdownloader.view.DownloadTaskDeleteActivity"
746-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:58:19-105
747            android:exported="false"
747-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:58:182-206
748            android:launchMode="singleTask"
748-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:58:106-137
749            android:theme="@android:style/Theme.Dialog" />
749-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:58:138-181
750        <activity
750-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:59:9-207
751            android:name="com.ss.android.socialbase.appdownloader.view.JumpUnknownSourceActivity"
751-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:59:19-104
752            android:exported="false"
752-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:59:181-205
753            android:launchMode="singleTask"
753-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:59:105-136
754            android:theme="@android:style/Theme.Dialog" />
754-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:59:137-180
755
756        <service
756-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:9-232
757            android:name="com.ss.android.socialbase.appdownloader.RetryJobSchedulerService"
757-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:18-97
758            android:enabled="true"
758-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:98-120
759            android:exported="false"
759-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:121-145
760            android:permission="android.permission.BIND_JOB_SERVICE"
760-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:146-202
761            android:stopWithTask="true" />
761-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:61:203-230
762        <service
762-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:62:9-66:19
763            android:name="com.ss.android.socialbase.downloader.downloader.IndependentProcessDownloadService"
763-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:62:18-114
764            android:exported="false"
764-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:62:115-139
765            android:process=":downloader" >
765-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:62:140-169
766            <intent-filter>
766-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:63:13-65:29
767                <action android:name="com.ss.android.socialbase.downloader.remote" />
767-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:64:17-85
767-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:64:25-83
768            </intent-filter>
769        </service>
770        <service
770-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:67:9-165
771            android:name="com.ss.android.socialbase.downloader.notification.DownloadNotificationService"
771-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:67:18-110
772            android:exported="false"
772-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:67:139-163
773            android:stopWithTask="true" />
773-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:67:111-138
774        <service
774-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:68:9-151
775            android:name="com.ss.android.socialbase.downloader.downloader.DownloadService"
775-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:68:18-96
776            android:exported="false"
776-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:68:125-149
777            android:stopWithTask="true" />
777-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:68:97-124
778        <service
778-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:69:9-152
779            android:name="com.ss.android.socialbase.downloader.impls.DownloadHandleService"
779-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:69:18-97
780            android:exported="false"
780-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:69:126-150
781            android:stopWithTask="true" />
781-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:69:98-125
782        <service
782-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:75:9-159
783            android:name="com.ss.android.socialbase.downloader.downloader.SqlDownloadCacheService"
783-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:75:18-104
784            android:exported="false"
784-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:75:133-157
785            android:stopWithTask="true" />
785-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:75:105-132
786
787        <meta-data
787-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:77:9-294
788            android:name="ZEUS_PLUGIN_LIVE"
788-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:77:20-51
789            android:value="{                 apiVersionCode:2114,                 packageName:com.byted.live.lite,                 minPluginVersion:211400,                 maxPluginVersion:999999,                 isSupportLibIsolate:true             }" />
789-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:77:52-292
790        <meta-data
790-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:78:9-79
791            android:name="LIVE_API_VERSION_CODE"
791-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:78:20-56
792            android:value="2114" />
792-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:78:57-77
793
794        <activity
794-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:80:9-139
795            android:name="com.bytedance.android.openliveplugin.stub.activity.DouyinAuthorizeActivityProxy"
795-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:80:19-113
796            android:exported="true" />
796-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:80:114-137
797        <activity
797-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:81:9-178
798            android:name="com.bytedance.android.openliveplugin.stub.activity.DouyinAuthorizeActivityLiveProcessProxy"
798-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:81:19-124
799            android:exported="true"
799-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:81:125-148
800            android:process=":bytelive" />
800-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:81:149-176
801
802        <provider
802-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:84:9-263
803            android:name="com.bytedance.android.openliveplugin.process.server.LiveServerManager"
803-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:84:19-103
804            android:authorities="com.example.concentration.bytedance.android.openliveplugin.process.server.LiveServerManager"
804-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:84:104-208
805            android:exported="false"
805-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:84:209-233
806            android:process=":bytelive" />
806-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:84:234-261
807
808        <activity
808-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:9-301
809            android:name="com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait"
809-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:19-115
810            android:configChanges="orientation|keyboardHidden|colorMode|uiMode"
810-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:116-183
811            android:exported="false"
811-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:275-299
812            android:screenOrientation="portrait"
812-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:184-220
813            android:theme="@android:style/Theme.Light.NoTitleBar" />
813-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:86:221-274
814        <activity
814-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:9-302
815            android:name="com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait2"
815-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:19-116
816            android:configChanges="orientation|keyboardHidden|colorMode|uiMode"
816-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:117-184
817            android:exported="false"
817-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:276-300
818            android:screenOrientation="portrait"
818-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:185-221
819            android:theme="@android:style/Theme.Light.NoTitleBar" />
819-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:87:222-275
820        <activity
820-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:9-302
821            android:name="com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait3"
821-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:19-116
822            android:configChanges="orientation|keyboardHidden|colorMode|uiMode"
822-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:117-184
823            android:exported="false"
823-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:276-300
824            android:screenOrientation="portrait"
824-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:185-221
825            android:theme="@android:style/Theme.Light.NoTitleBar" />
825-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:88:222-275
826        <activity
826-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:9-302
827            android:name="com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait4"
827-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:19-116
828            android:configChanges="orientation|keyboardHidden|colorMode|uiMode"
828-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:117-184
829            android:exported="false"
829-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:276-300
830            android:screenOrientation="portrait"
830-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:185-221
831            android:theme="@android:style/Theme.Light.NoTitleBar" />
831-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:89:222-275
832        <activity
832-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:9-302
833            android:name="com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait5"
833-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:19-116
834            android:configChanges="orientation|keyboardHidden|colorMode|uiMode"
834-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:117-184
835            android:exported="false"
835-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:276-300
836            android:screenOrientation="portrait"
836-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:185-221
837            android:theme="@android:style/Theme.Light.NoTitleBar" />
837-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:90:222-275
838        <activity
838-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:9-298
839            android:name="com.byted.live.lite.Activity_bytelive_singleTask4"
839-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:19-83
840            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
840-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:84-156
841            android:exported="false"
841-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:157-181
842            android:launchMode="singleTask"
842-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:182-213
843            android:process=":bytelive"
843-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:214-241
844            android:windowSoftInputMode="adjustResize|stateHidden" />
844-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:91:242-296
845        <activity
845-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:9-296
846            android:name="com.byted.live.lite.Activity_bytelive_singleTop3"
846-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:19-82
847            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
847-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:83-155
848            android:exported="false"
848-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:156-180
849            android:launchMode="singleTop"
849-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:181-211
850            android:process=":bytelive"
850-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:212-239
851            android:windowSoftInputMode="adjustResize|stateHidden" />
851-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:92:240-294
852        <activity
852-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:9-296
853            android:name="com.byted.live.lite.Activity_bytelive_singleTop2"
853-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:19-82
854            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
854-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:83-155
855            android:exported="false"
855-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:156-180
856            android:launchMode="singleTop"
856-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:181-211
857            android:process=":bytelive"
857-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:212-239
858            android:windowSoftInputMode="adjustResize|stateHidden" />
858-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:93:240-294
859        <activity
859-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:9-296
860            android:name="com.byted.live.lite.Activity_bytelive_singleTop5"
860-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:19-82
861            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
861-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:83-155
862            android:exported="false"
862-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:156-180
863            android:launchMode="singleTop"
863-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:181-211
864            android:process=":bytelive"
864-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:212-239
865            android:windowSoftInputMode="adjustResize|stateHidden" />
865-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:94:240-294
866        <activity
866-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:9-296
867            android:name="com.byted.live.lite.Activity_bytelive_singleTop4"
867-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:19-82
868            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
868-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:83-155
869            android:exported="false"
869-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:156-180
870            android:launchMode="singleTop"
870-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:181-211
871            android:process=":bytelive"
871-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:212-239
872            android:windowSoftInputMode="adjustResize|stateHidden" />
872-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:95:240-294
873        <activity
873-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:9-293
874            android:name="com.byted.live.lite.Activity_bytelive_standard"
874-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:19-80
875            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
875-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:81-153
876            android:exported="false"
876-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:154-178
877            android:launchMode="standard"
877-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:179-208
878            android:process=":bytelive"
878-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:209-236
879            android:windowSoftInputMode="adjustResize|stateHidden" />
879-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:96:237-291
880        <activity
880-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:9-296
881            android:name="com.byted.live.lite.Activity_bytelive_singleTop1"
881-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:19-82
882            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
882-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:83-155
883            android:exported="false"
883-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:156-180
884            android:launchMode="singleTop"
884-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:181-211
885            android:process=":bytelive"
885-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:212-239
886            android:windowSoftInputMode="adjustResize|stateHidden" />
886-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:97:240-294
887        <activity
887-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:9-298
888            android:name="com.byted.live.lite.Activity_bytelive_singleTask1"
888-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:19-83
889            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
889-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:84-156
890            android:exported="false"
890-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:157-181
891            android:launchMode="singleTask"
891-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:182-213
892            android:process=":bytelive"
892-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:214-241
893            android:windowSoftInputMode="adjustResize|stateHidden" />
893-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:98:242-296
894        <activity
894-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:9-296
895            android:name="com.byted.live.lite.Activity_bytelive_singleTop6"
895-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:19-82
896            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
896-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:83-155
897            android:exported="false"
897-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:156-180
898            android:launchMode="singleTop"
898-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:181-211
899            android:process=":bytelive"
899-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:212-239
900            android:windowSoftInputMode="adjustResize|stateHidden" />
900-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:99:240-294
901        <activity
901-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:9-298
902            android:name="com.byted.live.lite.Activity_bytelive_singleTask2"
902-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:19-83
903            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
903-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:84-156
904            android:exported="false"
904-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:157-181
905            android:launchMode="singleTask"
905-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:182-213
906            android:process=":bytelive"
906-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:214-241
907            android:windowSoftInputMode="adjustResize|stateHidden" />
907-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:100:242-296
908        <activity
908-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:9-298
909            android:name="com.byted.live.lite.Activity_bytelive_singleTask3"
909-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:19-83
910            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
910-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:84-156
911            android:exported="false"
911-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:157-181
912            android:launchMode="singleTask"
912-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:182-213
913            android:process=":bytelive"
913-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:214-241
914            android:windowSoftInputMode="adjustResize|stateHidden" />
914-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:101:242-296
915        <activity
915-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:9-266
916            android:name="com.byted.live.lite.Activity_main_singleTask1"
916-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:19-79
917            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
917-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:80-152
918            android:exported="false"
918-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:153-177
919            android:launchMode="singleTask"
919-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:178-209
920            android:windowSoftInputMode="adjustResize|stateHidden" />
920-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:102:210-264
921        <activity
921-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:9-266
922            android:name="com.byted.live.lite.Activity_main_singleTask2"
922-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:19-79
923            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
923-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:80-152
924            android:exported="false"
924-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:153-177
925            android:launchMode="singleTask"
925-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:178-209
926            android:windowSoftInputMode="adjustResize|stateHidden" />
926-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:103:210-264
927        <activity
927-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:9-266
928            android:name="com.byted.live.lite.Activity_main_singleTask3"
928-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:19-79
929            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
929-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:80-152
930            android:exported="false"
930-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:153-177
931            android:launchMode="singleTask"
931-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:178-209
932            android:windowSoftInputMode="adjustResize|stateHidden" />
932-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:104:210-264
933        <activity
933-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:9-266
934            android:name="com.byted.live.lite.Activity_main_singleTask4"
934-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:19-79
935            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
935-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:80-152
936            android:exported="false"
936-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:153-177
937            android:launchMode="singleTask"
937-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:178-209
938            android:windowSoftInputMode="adjustResize|stateHidden" />
938-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:105:210-264
939        <activity
939-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:9-261
940            android:name="com.byted.live.lite.Activity_main_standard"
940-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:19-76
941            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
941-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:77-149
942            android:exported="false"
942-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:150-174
943            android:launchMode="standard"
943-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:175-204
944            android:windowSoftInputMode="adjustResize|stateHidden" />
944-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:106:205-259
945        <activity
945-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:9-264
946            android:name="com.byted.live.lite.Activity_main_singleTop1"
946-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:19-78
947            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
947-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:79-151
948            android:exported="false"
948-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:152-176
949            android:launchMode="singleTop"
949-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:177-207
950            android:windowSoftInputMode="adjustResize|stateHidden" />
950-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:107:208-262
951        <activity
951-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:9-264
952            android:name="com.byted.live.lite.Activity_main_singleTop2"
952-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:19-78
953            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
953-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:79-151
954            android:exported="false"
954-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:152-176
955            android:launchMode="singleTop"
955-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:177-207
956            android:windowSoftInputMode="adjustResize|stateHidden" />
956-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:108:208-262
957        <activity
957-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:9-274
958            android:name="com.byted.live.lite.Activity_main_singleInstance1"
958-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:19-83
959            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
959-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:84-156
960            android:exported="false"
960-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:157-181
961            android:launchMode="singleInstance"
961-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:182-217
962            android:windowSoftInputMode="adjustResize|stateHidden" />
962-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:109:218-272
963        <activity
963-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:9-264
964            android:name="com.byted.live.lite.Activity_main_singleTop5"
964-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:19-78
965            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
965-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:79-151
966            android:exported="false"
966-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:152-176
967            android:launchMode="singleTop"
967-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:177-207
968            android:windowSoftInputMode="adjustResize|stateHidden" />
968-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:110:208-262
969        <activity
969-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:9-264
970            android:name="com.byted.live.lite.Activity_main_singleTop6"
970-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:19-78
971            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
971-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:79-151
972            android:exported="false"
972-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:152-176
973            android:launchMode="singleTop"
973-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:177-207
974            android:windowSoftInputMode="adjustResize|stateHidden" />
974-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:111:208-262
975        <activity
975-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:9-264
976            android:name="com.byted.live.lite.Activity_main_singleTop3"
976-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:19-78
977            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
977-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:79-151
978            android:exported="false"
978-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:152-176
979            android:launchMode="singleTop"
979-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:177-207
980            android:windowSoftInputMode="adjustResize|stateHidden" />
980-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:112:208-262
981        <activity
981-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:9-264
982            android:name="com.byted.live.lite.Activity_main_singleTop4"
982-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:19-78
983            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
983-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:79-151
984            android:exported="false"
984-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:152-176
985            android:launchMode="singleTop"
985-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:177-207
986            android:windowSoftInputMode="adjustResize|stateHidden" />
986-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:113:208-262
987        <activity
987-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:9-306
988            android:name="com.byted.live.lite.Activity_bytelive_singleInstance1"
988-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:19-87
989            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
989-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:88-160
990            android:exported="false"
990-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:161-185
991            android:launchMode="singleInstance"
991-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:186-221
992            android:process=":bytelive"
992-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:222-249
993            android:windowSoftInputMode="adjustResize|stateHidden" />
993-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:114:250-304
994
995        <provider
995-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:116:9-220
996            android:name="com.byted.live.lite.ServerManager_bytelive"
996-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:116:19-76
997            android:authorities="com.example.concentration.pangle.servermanager.bytelive.com.byted.live.lite"
997-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:116:77-165
998            android:exported="false"
998-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:116:166-190
999            android:process=":bytelive" />
999-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:116:************        <provider
1000-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:117:9-208
1001            android:name="com.byted.live.lite.ServerManager_push"
1001-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:117:19-72
1002            android:authorities="com.example.concentration.pangle.servermanager.push.com.byted.live.lite"
1002-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:117:73-157
1003            android:exported="false"
1003-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:117:158-182
1004            android:process=":push" />
1004-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:117:183-206
1005        <provider
1005-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:118:9-226
1006            android:name="com.byted.live.lite.ServerManager_downloader"
1006-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:118:19-78
1007            android:authorities="com.example.concentration.pangle.servermanager.downloader.com.byted.live.lite"
1007-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:118:79-169
1008            android:exported="false"
1008-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:118:170-194
1009            android:process=":downloader" />
1009-->[open_ad_sdk_6.8.4.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\AndroidManifest.xml:118:195-224
1010        <provider
1010-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:36:9-45:20
1011            android:name="com.sigmob.sdk.SigmobFileProvider"
1011-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:37:13-61
1012            android:authorities="com.example.concentration.sigprovider"
1012-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:38:13-63
1013            android:exported="false"
1013-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:39:13-37
1014            android:grantUriPermissions="true"
1014-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:40:13-47
1015            android:initOrder="200" >
1015-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:41:13-36
1016            <meta-data
1016-->C:\专注力训练\app\src\main\AndroidManifest.xml:43:13-45:68
1017                android:name="android.support.FILE_PROVIDER_PATHS"
1017-->C:\专注力训练\app\src\main\AndroidManifest.xml:44:17-67
1018                android:resource="@xml/sigmob_provider_paths" />
1018-->C:\专注力训练\app\src\main\AndroidManifest.xml:45:17-65
1019        </provider> <!-- TT 国内 -->
1020        <activity
1020-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:47:9-53:60
1021            android:name="com.sigmob.sdk.base.common.TransparentAdActivity"
1021-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:48:13-76
1022            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
1022-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:49:13-83
1023            android:hardwareAccelerated="true"
1023-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:50:13-47
1024            android:multiprocess="true"
1024-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:51:13-40
1025            android:screenOrientation="behind"
1025-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:52:13-47
1026            android:theme="@style/sig_transparent_style" />
1026-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:53:13-57
1027        <activity
1027-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:54:9-60:69
1028            android:name="com.sigmob.sdk.base.common.AdActivity"
1028-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:55:13-65
1029            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
1029-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:56:13-83
1030            android:hardwareAccelerated="true"
1030-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:57:13-47
1031            android:multiprocess="true"
1031-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:58:13-40
1032            android:screenOrientation="behind"
1032-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:59:13-47
1033            android:theme="@android:style/Theme.Light.NoTitleBar" />
1033-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:60:13-66
1034        <activity
1034-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:61:9-67:69
1035            android:name="com.sigmob.sdk.base.common.PortraitAdActivity"
1035-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:62:13-73
1036            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
1036-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:63:13-83
1037            android:hardwareAccelerated="true"
1037-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:64:13-47
1038            android:multiprocess="true"
1038-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:65:13-40
1039            android:screenOrientation="sensorPortrait"
1039-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:66:13-55
1040            android:theme="@android:style/Theme.Light.NoTitleBar" />
1040-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:67:13-66
1041        <activity
1041-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:68:9-74:69
1042            android:name="com.sigmob.sdk.base.common.LandscapeAdActivity"
1042-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:69:13-74
1043            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
1043-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:70:13-83
1044            android:hardwareAccelerated="true"
1044-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:71:13-47
1045            android:multiprocess="true"
1045-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:72:13-40
1046            android:screenOrientation="sensorLandscape"
1046-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:73:13-56
1047            android:theme="@android:style/Theme.Light.NoTitleBar" />
1047-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:74:13-66
1048        <activity
1048-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:75:9-81:60
1049            android:name="com.sigmob.sdk.base.common.PortraitTransparentAdActivity"
1049-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:76:13-84
1050            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
1050-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:77:13-83
1051            android:hardwareAccelerated="true"
1051-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:78:13-47
1052            android:multiprocess="true"
1052-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:79:13-40
1053            android:screenOrientation="sensorPortrait"
1053-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:80:13-55
1054            android:theme="@style/sig_transparent_style" />
1054-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:81:13-57
1055        <activity
1055-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:82:9-88:60
1056            android:name="com.sigmob.sdk.base.common.LandscapeTransparentAdActivity"
1056-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:83:13-85
1057            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
1057-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:84:13-83
1058            android:hardwareAccelerated="true"
1058-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:85:13-47
1059            android:multiprocess="true"
1059-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:86:13-40
1060            android:screenOrientation="sensorLandscape"
1060-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:87:13-56
1061            android:theme="@style/sig_transparent_style" />
1061-->[wind-sdk-4.21.1.aar] C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\AndroidManifest.xml:88:13-57
1062
1063        <provider
1063-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
1064            android:name="androidx.startup.InitializationProvider"
1064-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
1065            android:authorities="com.example.concentration.androidx-startup"
1065-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
1066            android:exported="false" >
1066-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
1067            <meta-data
1067-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
1068                android:name="androidx.emoji2.text.EmojiCompatInitializer"
1068-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
1069                android:value="androidx.startup" />
1069-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\acd7b7be712c1ada299bcfc81b726076\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
1070            <meta-data
1070-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
1071                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
1071-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
1072                android:value="androidx.startup" />
1072-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\64a43ba5e267ff1bec7f2dbd5a6fc86a\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
1073            <meta-data
1073-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
1074                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
1074-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
1075                android:value="androidx.startup" />
1075-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
1076        </provider>
1077
1078        <receiver
1078-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
1079            android:name="androidx.profileinstaller.ProfileInstallReceiver"
1079-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
1080            android:directBootAware="false"
1080-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
1081            android:enabled="true"
1081-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
1082            android:exported="true"
1082-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
1083            android:permission="android.permission.DUMP" >
1083-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
1084            <intent-filter>
1084-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
1085                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
1085-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
1085-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
1086            </intent-filter>
1087            <intent-filter>
1087-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
1088                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
1088-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
1088-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
1089            </intent-filter>
1090            <intent-filter>
1090-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
1091                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
1091-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
1091-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
1092            </intent-filter>
1093            <intent-filter>
1093-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
1094                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
1094-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
1094-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d68980383f0015e28b8eec4415ed3978\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
1095            </intent-filter>
1096        </receiver>
1097    </application>
1098
1099</manifest>
