<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/light_gradient_background"
    android:padding="16dp">

    <!-- 标题区域 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🧠 数字内容记忆"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/md_theme_light_onBackground"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/tv_instruction"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="✨ 请记住数字序列的内容"
        android:textSize="16sp"
        android:textColor="@color/md_theme_light_onBackground"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tv_progress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="准备显示数字..."
        android:textSize="14sp"
        android:textColor="@color/md_theme_light_onBackground"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- 数字显示区域 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_number_display"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:cardBackgroundColor="@android:color/white"
        android:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_current_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:textSize="72sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_primary"
                android:gravity="center" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 位置网格区域 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:cardBackgroundColor="@android:color/white">

        <GridLayout
            android:id="@+id/grid_positions"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:padding="16dp"
            android:visibility="gone" />

    </androidx.cardview.widget.CardView>

    <!-- 输入区域 -->
    <LinearLayout
        android:id="@+id/layout_input_area"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_input_prompt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="请输入数字："
            android:textSize="16sp"
            android:textColor="@color/md_theme_light_onBackground"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <!-- 数字按钮区域 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:id="@+id/layout_number_buttons"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                android:gravity="center" />

        </androidx.cardview.widget.CardView>

        <!-- 确认按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_confirm"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="✅ 确认答案"
            android:textSize="16sp"
            android:textStyle="bold"
            app:cornerRadius="28dp"
            app:backgroundTint="@color/md_theme_light_primary"
            android:textColor="@android:color/white" />

    </LinearLayout>

</LinearLayout>
