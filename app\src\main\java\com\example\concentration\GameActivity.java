package com.example.concentration;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.GridLayout;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class GameActivity extends AppCompatActivity {
    
    private GridLayout gridLayout;
    private TextView tvTimer;
    private TextView tvCurrentNumber;
    private View backgroundView;
    private ViewGroup rootView;
    
    private int gridSize;
    private String themeId;
    private boolean numbersDisappear;
    private int currentNumber = 1;
    private long startTime;
    private Handler timerHandler;
    private Runnable timerRunnable;
    private boolean isGameRunning = true;
    
    // 管理器
    private SoundManager soundManager;
    private ThemeManager themeManager;
    private GameDataManager gameDataManager;
    private CelebrationManager celebrationManager;
    private StoryManager storyManager;
    
    // 按钮列表
    private List<Button> gameButtons;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_game);
        
        // 获取参数
        gridSize = getIntent().getIntExtra("gridSize", 3);
        themeId = getIntent().getStringExtra("themeId");
        numbersDisappear = getIntent().getBooleanExtra("numbersDisappear", true);
        if (themeId == null) themeId = "default";
        
        initializeManagers();
        initializeViews();
        setupGame();
        startTimer();
    }
    
    private void initializeManagers() {
        soundManager = SoundManager.getInstance(this);
        themeManager = ThemeManager.getInstance(this);
        gameDataManager = new GameDataManager(this);
        celebrationManager = CelebrationManager.getInstance(this);
        storyManager = StoryManager.getInstance(this);
    }
    
    private void initializeViews() {
        gridLayout = findViewById(R.id.grid_layout);
        tvTimer = findViewById(R.id.tv_timer);
        tvCurrentNumber = findViewById(R.id.tv_current_number);
        backgroundView = findViewById(R.id.background_view);
        rootView = findViewById(R.id.root_view);
        
        timerHandler = new Handler();
        gameButtons = new ArrayList<>();
        
        // 应用主题样式
        applyThemeStyles();
    }
    
    private void applyThemeStyles() {
        ThemeManager.ThemeType currentTheme = themeManager.getCurrentTheme();
        
        switch (currentTheme) {
            case SPACE:
                backgroundView.setBackgroundResource(R.drawable.space_game_background);
                break;
            case CARTOON:
                backgroundView.setBackgroundResource(R.drawable.cartoon_game_background);
                break;
            case ANIMAL:
                backgroundView.setBackgroundResource(R.drawable.animal_game_background);
                break;
            default:
                backgroundView.setBackgroundResource(R.drawable.default_game_background);
                break;
        }
    }
    
    private void setupGame() {
        // 设置网格
        gridLayout.setColumnCount(gridSize);
        gridLayout.setRowCount(gridSize);
        
        // 创建数字列表并打乱
        List<Integer> numbers = new ArrayList<>();
        for (int i = 1; i <= gridSize * gridSize; i++) {
            numbers.add(i);
        }
        Collections.shuffle(numbers);
        
        // 计算按钮大小（自适应屏幕，更美观的比例）
        int screenWidth = getResources().getDisplayMetrics().widthPixels;
        int screenMargin = (int) (24 * getResources().getDisplayMetrics().density); // 屏幕边距
        int gridPadding = (int) (20 * getResources().getDisplayMetrics().density); // 网格容器内边距
        int buttonMargin = (int) (4 * getResources().getDisplayMetrics().density); // 按钮间距

        int totalMargin = screenMargin * 2 + gridPadding * 2;
        int totalSpacing = (gridSize - 1) * buttonMargin; // 按钮之间的间距
        int availableWidth = screenWidth - totalMargin - totalSpacing;
        int buttonSize = availableWidth / gridSize;

        // 确保按钮大小合理 - 最大化屏幕利用率
        int minButtonSize = (int) (45 * getResources().getDisplayMetrics().density);

        if (buttonSize < minButtonSize) {
            buttonSize = minButtonSize;
        }
        // 移除最大尺寸限制，让方格充分利用屏幕空间
        
        // 随机选择本次游戏的格子效果类型
        int gameEffectType = (int) (Math.random() * 4); // 4种不同的游戏效果

        // 创建按钮
        gameButtons.clear();
        for (int i = 0; i < gridSize * gridSize; i++) {
            Button button = createGameButton(numbers.get(i), buttonSize, gameEffectType);
            gameButtons.add(button);
            gridLayout.addView(button);
        }
        
        // 更新当前目标数字显示
        updateCurrentNumberDisplay();
        
        // 记录开始时间
        startTime = System.currentTimeMillis();
    }
    
    private Button createGameButton(int number, int size, int effectType) {
        Button button = new Button(this);
        button.setText(String.valueOf(number));
        button.setTextSize(24);
        button.setTextColor(Color.WHITE);
        button.setTag(number);

        // 设置按钮大小和边距
        GridLayout.LayoutParams params = new GridLayout.LayoutParams();
        params.width = size;
        params.height = size;
        int margin = (int) (2 * getResources().getDisplayMetrics().density);
        params.setMargins(margin, margin, margin, margin);
        button.setLayoutParams(params);

        // 设置按钮样式
        setButtonStyle(button, false);

        // 应用动态格子效果
        applyDynamicGridEffect(button, effectType);

        // 设置点击监听
        button.setOnClickListener(v -> onNumberClick(button, number));

        return button;
    }
    
    private void setButtonStyle(Button button, boolean isCorrect) {
        GradientDrawable drawable = new GradientDrawable();
        drawable.setShape(GradientDrawable.RECTANGLE);
        drawable.setCornerRadius(12);

        if (isCorrect) {
            // 正确点击后的金色样式
            drawable.setColors(new int[]{0xFFFFD700, 0xFFFFA500});
            button.setTextColor(Color.WHITE);
            playGoldenFlameEffect(button);
        } else {
            // 恢复原来简洁美观的样式
            drawable.setColor(Color.WHITE);
            drawable.setStroke(3, 0xFFE0E0E0);
            button.setTextColor(0xFF333333);
        }

        button.setBackground(drawable);
        button.setElevation(4);
    }

    private void applyDynamicGridEffect(Button button, int effectType) {
        switch (effectType) {
            case 0: // 彩虹渐变效果
                applyRainbowEffect(button);
                break;
            case 1: // 3D立体效果
                apply3DEffect(button);
                break;
            case 2: // 霓虹发光效果
                applyNeonEffect(button);
                break;
            case 3: // 材质纹理效果
                applyMaterialEffect(button);
                break;
        }
    }

    private void applyRainbowEffect(Button button) {
        // 彩虹渐变背景 - 使用浅色调确保数字可见
        GradientDrawable drawable = (GradientDrawable) button.getBackground();
        int[] rainbowColors = {
            0xFFFFB3BA, 0xFFFFDFBA, 0xFFFFFFBA, 0xFFBAFFC9, 0xFFBAE1FF, 0xFFE1BAFF
        };
        int colorIndex = (int) (Math.random() * rainbowColors.length);
        int nextColorIndex = (colorIndex + 1) % rainbowColors.length;

        drawable.setColors(new int[]{rainbowColors[colorIndex], rainbowColors[nextColorIndex]});
        drawable.setOrientation(GradientDrawable.Orientation.TL_BR);
        button.setTextColor(0xFF333333); // 确保文字颜色深色可见
    }

    private void apply3DEffect(Button button) {
        // 3D立体效果
        button.setElevation(16);
        button.setTranslationZ(8);

        // 添加阴影效果
        GradientDrawable drawable = (GradientDrawable) button.getBackground();
        drawable.setColors(new int[]{0xFFE8E8E8, 0xFFD0D0D0});
        drawable.setStroke(3, 0xFFBBBBBB);
    }

    private void applyNeonEffect(Button button) {
        // 霓虹发光效果 - 使用浅色背景确保数字可见
        GradientDrawable drawable = (GradientDrawable) button.getBackground();
        int[] neonColors = {
            0xFF87CEEB, 0xFFDDA0DD, 0xFFFFFACD, 0xFF98FB98, 0xFFFFB6C1
        };
        int colorIndex = (int) (Math.random() * neonColors.length);

        drawable.setColors(new int[]{neonColors[colorIndex], 0xFFFFFFFF});
        drawable.setStroke(4, neonColors[colorIndex]);
        button.setElevation(12);
        button.setTextColor(0xFF333333); // 确保文字颜色深色可见
    }

    private void applyMaterialEffect(Button button) {
        // 材质纹理效果
        GradientDrawable drawable = (GradientDrawable) button.getBackground();
        drawable.setColors(new int[]{0xFFF8F9FA, 0xFFE9ECEF});
        drawable.setStroke(1, 0xFFDEE2E6);
        drawable.setCornerRadius(12);

        // 添加轻微阴影
        button.setElevation(6);
    }
    
    private void onNumberClick(Button button, int number) {
        if (!isGameRunning) return;
        
        if (number == currentNumber) {
            // 正确点击
            soundManager.playCorrectSound();
            setButtonStyle(button, true);
            
            // 根据设置决定是否隐藏按钮
            if (numbersDisappear) {
                // 按钮消失动画
                ObjectAnimator fadeOut = ObjectAnimator.ofFloat(button, "alpha", 1.0f, 0.0f);
                fadeOut.setDuration(500);
                fadeOut.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        button.setVisibility(View.INVISIBLE);
                    }
                });
                fadeOut.start();
            } else {
                // 保持显示但改变样式表示已点击
                button.setEnabled(false);
                button.setAlpha(0.5f);
            }
            
            currentNumber++;
            updateCurrentNumberDisplay();
            
            // 检查游戏是否完成
            if (currentNumber > gridSize * gridSize) {
                completeGame();
            }
        } else {
            // 错误点击
            soundManager.playWrongSound();
            shakeButton(button);
        }
    }
    
    private void playGoldenFlameEffect(Button button) {
        // 创建金色光环效果
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(button, "scaleX", 1.0f, 1.2f, 1.0f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(button, "scaleY", 1.0f, 1.2f, 1.0f);
        
        scaleX.setDuration(300);
        scaleY.setDuration(300);
        
        scaleX.start();
        scaleY.start();
        
        // TODO: 添加更炫酷的粒子效果
    }
    
    private void shakeButton(Button button) {
        ObjectAnimator shake = ObjectAnimator.ofFloat(button, "translationX", 0, -25, 25, -25, 25, 0);
        shake.setDuration(500);
        shake.start();
    }
    
    private void updateCurrentNumberDisplay() {
        if (currentNumber <= gridSize * gridSize) {
            tvCurrentNumber.setText("寻找: " + currentNumber);
        } else {
            tvCurrentNumber.setText("完成！");
        }
    }
    
    private void startTimer() {
        timerRunnable = new Runnable() {
            @Override
            public void run() {
                if (isGameRunning) {
                    long elapsedTime = System.currentTimeMillis() - startTime;
                    tvTimer.setText(formatTime(elapsedTime));
                    timerHandler.postDelayed(this, 10); // 10ms更新一次，显示毫秒
                }
            }
        };
        timerHandler.post(timerRunnable);
    }
    
    private String formatTime(long timeInMillis) {
        int totalSeconds = (int) (timeInMillis / 1000);
        int minutes = totalSeconds / 60;
        int seconds = totalSeconds % 60;
        int millis = (int) (timeInMillis % 1000) / 10; // 显示两位毫秒
        
        return String.format("%02d:%02d.%02d", minutes, seconds, millis);
    }
    
    private void completeGame() {
        isGameRunning = false;
        long completionTime = System.currentTimeMillis() - startTime;

        // 保存游戏数据
        gameDataManager.saveBestTime(gridSize, completionTime);
        gameDataManager.recordGameCompletion();

        // 添加水晶奖励
        CrystalManager crystalManager = CrystalManager.getInstance(this);
        CrystalManager.CrystalReward reward = CrystalManager.getSchulteGridReward(gridSize);
        boolean newCrystalGenerated = crystalManager.addCrystalFragments(reward.type, reward.fragments);

        // 跳转到庆祝页面
        Intent intent = new Intent(this, CelebrationActivity.class);
        intent.putExtra("game_type", "schulte");
        intent.putExtra("time", completionTime);
        intent.putExtra("grid_size", gridSize);
        intent.putExtra("success", true);
        intent.putExtra("crystal_reward", reward.fragments);
        intent.putExtra("crystal_description", reward.description);
        intent.putExtra("new_crystal_generated", newCrystalGenerated);
        startActivity(intent);
        finish();
    }

    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (timerHandler != null) {
            timerHandler.removeCallbacksAndMessages(null);
        }
        if (soundManager != null) {
            soundManager.stopBackgroundMusic();
        }
    }
}
