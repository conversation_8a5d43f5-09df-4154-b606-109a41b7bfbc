package com.example.concentration;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.widget.FrameLayout;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;

//开屏广告Activity
public class SplashActivity extends AppCompatActivity {

    // 设置一个变量来控制当前开屏页面是否可以跳转，
    // 当开屏为普链类时，点击会打开一个落地页，此时开发者还不能打开自己的App主页。
    // 当从落地页返回以后，才可以跳转到开发者自己的App主页；
    // 当开屏是App类时只会下载App。
    private boolean canJump;

    private FrameLayout mSplashContainer;
    private final String TAG = "SplashActivity";
    private Handler handler = new Handler();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 隐藏标题栏
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        setContentView(R.layout.activity_splash);
        mSplashContainer = findViewById(R.id.splash_container);

        // 直接初始化广告SDK并加载开屏广告
        ((AdApplication) getApplication()).initAdSdk();
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                loadSplash();
            }
        }, 1000);
    }



    @Override
    protected void onResume() {
        super.onResume();
        canJump = true;
        // 如果没有广告在显示，则跳转到主页面
        if (mSplashContainer.getChildCount() == 0) {
            handler.postDelayed(() -> jumpToMain(), 3000); // 增加等待时间让广告有机会加载
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        canJump = false;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
    }

    private void jumpToMain() {
        if (canJump && !isFinishing()) {
            //跳转到主页面
            Intent intent = new Intent(this, MainActivity.class);
            startActivity(intent);
            finish();
        }
    }

    //加载开屏广告
    private void loadSplash() {
        try {
            com.by.mob.config.BySplashConfig config = new com.by.mob.config.BySplashConfig.Builder()
                    .codeId(AdConfig.splashCodeId)//平台申请的代码位id
                    .container(mSplashContainer)//承载视图的容器。可自定义高度比例,但不能低于0.75
                    .build();
                    
            com.by.mob.ByManager.loadSplash(config, this, new com.by.mob.ByManager.SplashLoadListener() {
                @Override
                public void onFail(String s) {
                    Log.d(TAG, "开屏广告加载失败=" + s);
                    //加载失败，直接跳转到主页面
                    jumpToMain();
                }

                @Override
                public void onTick(long l) {
                    Log.d(TAG, "开屏广告倒计时: " + l);
                }

                @Override
                public void onClicked() {
                    Log.d(TAG, "开屏广告被点击");
                }

                @Override
                public void onDismiss() {
                    Log.d(TAG, "开屏广告被关闭");
                    //广告被关闭，跳转到主页面
                    jumpToMain();
                }

                @Override
                public void onExposure(com.by.mob.bean.Position position) {
                    Log.d(TAG, "开屏广告曝光");
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "加载开屏广告异常", e);
            // 异常情况下直接跳转到主页面
            jumpToMain();
        }
    }
}
