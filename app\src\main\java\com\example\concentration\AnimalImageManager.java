package com.example.concentration;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;

public class AnimalImageManager {
    
    // 可爱动物emoji列表
    private static final String[] ANIMAL_EMOJIS = {
        "🐶", "🐱", "🐭", "🐹", "🐰", "🦊", "🐻", "🐼",
        "🐨", "🐯", "🦁", "🐮", "🐷", "🐸", "🐵", "🐔",
        "🐧", "🐦", "🐤", "🐣", "🐺", "🐗", "🐴", "🦄",
        "🐝", "🐛", "🦋", "🐌", "🐞", "🐜", "🦗", "🕷️",
        "🐢", "🐍", "🦎", "🐙", "🦑", "🦐", "🦀", "🐡",
        "🐠", "🐟", "🐬", "🐳", "🐋", "🦈", "🐊", "🐅"
    };
    
    // 颜色背景列表
    private static final int[] BACKGROUND_COLORS = {
        0xFFFFE0E6, // 浅粉色
        0xFFE0F2FF, // 浅蓝色
        0xFFE0FFE0, // 浅绿色
        0xFFFFF0E0, // 浅橙色
        0xFFF0E0FF, // 浅紫色
        0xFFFFFFE0, // 浅黄色
        0xFFE0FFFF, // 浅青色
        0xFFFFE0F0  // 浅玫瑰色
    };
    
    private Random random;
    private List<String> shuffledAnimals;
    private List<Integer> shuffledColors;
    private int currentIndex = 0;
    
    public AnimalImageManager() {
        random = new Random();
        refreshAnimals();
    }
    
    /**
     * 刷新动物列表，每次游戏开始时调用
     */
    public void refreshAnimals() {
        shuffledAnimals = new ArrayList<>();
        shuffledColors = new ArrayList<>();
        
        // 复制动物emoji
        for (String animal : ANIMAL_EMOJIS) {
            shuffledAnimals.add(animal);
        }
        
        // 复制颜色
        for (int color : BACKGROUND_COLORS) {
            shuffledColors.add(color);
        }
        
        // 打乱顺序
        Collections.shuffle(shuffledAnimals, random);
        Collections.shuffle(shuffledColors, random);
        
        currentIndex = 0;
    }
    
    /**
     * 获取下一个动物emoji
     */
    public String getNextAnimal() {
        if (currentIndex >= shuffledAnimals.size()) {
            refreshAnimals(); // 如果用完了就重新刷新
        }
        return shuffledAnimals.get(currentIndex++);
    }
    
    /**
     * 获取随机动物emoji
     */
    public String getRandomAnimal() {
        return ANIMAL_EMOJIS[random.nextInt(ANIMAL_EMOJIS.length)];
    }
    
    /**
     * 获取随机背景颜色
     */
    public int getRandomBackgroundColor() {
        return BACKGROUND_COLORS[random.nextInt(BACKGROUND_COLORS.length)];
    }
    
    /**
     * 为指定数量的卡片生成动物图案
     */
    public List<CardAnimal> generateCardAnimals(int count) {
        List<CardAnimal> cardAnimals = new ArrayList<>();

        // 为这局游戏选择一个固定的动物和背景色
        String gameAnimal = getRandomAnimal();
        int gameBackgroundColor = getRandomBackgroundColor();

        for (int i = 0; i < count; i++) {
            CardAnimal cardAnimal = new CardAnimal();
            cardAnimal.emoji = gameAnimal; // 所有卡片使用同一个动物
            cardAnimal.backgroundColor = gameBackgroundColor; // 所有卡片使用同一个背景色
            cardAnimals.add(cardAnimal);
        }

        return cardAnimals;
    }


    
    /**
     * 卡片动物数据类
     */
    public static class CardAnimal {
        public String emoji;
        public int backgroundColor;
        
        public CardAnimal() {
        }
        
        public CardAnimal(String emoji, int backgroundColor) {
            this.emoji = emoji;
            this.backgroundColor = backgroundColor;
        }
    }
}
