<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <Button
            android:id="@+id/btn_reward_load"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_gravity="center"
            android:layout_marginStart="8dp"
            android:layout_marginLeft="8dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="8dp"
            android:layout_marginRight="8dp"
            android:gravity="center"
            android:text="加载横版激励视频"
            android:textColor="#3399cc"
            android:textSize="14sp" />

        <Button
            android:id="@+id/btn_reward_load_vertical"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_gravity="center"
            android:layout_marginStart="8dp"
            android:layout_marginLeft="8dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="8dp"
            android:layout_marginRight="8dp"
            android:gravity="center"
            android:text="加载竖版激励视频"
            android:textColor="#3399cc"
            android:textSize="14sp" />


        <Button
            android:id="@+id/btn_reward_load_vertical_now"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_gravity="center"
            android:layout_marginStart="8dp"
            android:layout_marginLeft="8dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="8dp"
            android:layout_marginRight="8dp"
            android:gravity="center"
            android:text="加载竖版激励视频(在线播放)"
            android:textColor="#3399cc"
            android:textSize="14sp" />
    </LinearLayout>

    <ProgressBar
        android:id="@+id/a_RewardVideo_ProgressBar"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true" />
</RelativeLayout>