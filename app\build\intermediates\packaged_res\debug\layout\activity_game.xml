<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 背景 -->
    <View
        android:id="@+id/background_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/clean_game_background" />

    <!-- 顶部状态栏 -->
    <LinearLayout
        android:id="@+id/top_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_marginTop="40dp"
        android:layout_marginHorizontal="20dp"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- 计时器 -->
        <TextView
            android:id="@+id/tv_timer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="00:00.00"
            android:textSize="20sp"
            android:textColor="#333333"
            android:textStyle="bold"
            android:background="@drawable/timer_background"
            android:padding="12dp"
            android:gravity="center" />

        <!-- 当前目标 -->
        <TextView
            android:id="@+id/tv_current_number"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:text="寻找: 1"
            android:textSize="18sp"
            android:textColor="#666666"
            android:textStyle="bold"
            android:background="@drawable/target_background"
            android:padding="12dp"
            android:gravity="center" />

    </LinearLayout>

    <!-- 游戏网格容器 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_below="@id/top_status"
        android:layout_above="@id/bottom_space"
        android:layout_marginTop="40dp">

        <!-- 网格布局 -->
        <GridLayout
            android:id="@+id/grid_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/grid_container_background"
            android:padding="20dp"
            android:elevation="8dp" />

    </FrameLayout>

    <!-- 底部留白（为未来剧情内容预留空间） -->
    <View
        android:id="@+id/bottom_space"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_alignParentBottom="true" />

</RelativeLayout>
