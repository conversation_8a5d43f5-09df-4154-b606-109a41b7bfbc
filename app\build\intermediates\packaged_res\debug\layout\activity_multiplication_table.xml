<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_light_bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 顶部标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="24dp">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="✖️ 九九乘法表学习"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onBackground"
                android:gravity="center"
                android:layout_marginHorizontal="16dp" />

            <!-- 教程按钮 - 灯泡图标 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_tutorial"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:text="💡"
                android:textSize="20sp"
                style="@style/Widget.Material3.Button.IconButton"
                android:background="@android:color/transparent" />

        </LinearLayout>

        <!-- 阶段1：基础概念认知 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_stage1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/gradient_stage1_bg"
                android:padding="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🎯"
                        android:textSize="28sp"
                        android:layout_marginEnd="12dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="阶段1：基础概念认知"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onPrimary" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="理解乘法的基本含义"
                            android:textSize="14sp"
                            android:textColor="@color/md_theme_light_onPrimary"
                            android:alpha="0.9"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/tv_stage1_progress"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="进度: 0%"
                        android:textSize="14sp"
                        android:textColor="@color/md_theme_light_onPrimary" />

                    <TextView
                        android:id="@+id/tv_stage1_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🌟 开始学习"
                        android:textSize="14sp"
                        android:textColor="@color/md_theme_light_onPrimary"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 阶段2：分数字渐进学习 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_stage2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/gradient_stage2_bg"
                android:padding="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📚"
                        android:textSize="28sp"
                        android:layout_marginEnd="12dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="阶段2：分数字渐进学习"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onPrimary" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="逐个学习1-9的乘法表"
                            android:textSize="14sp"
                            android:textColor="@color/md_theme_light_onPrimary"
                            android:alpha="0.9"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/tv_stage2_progress"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="进度: 0%"
                        android:textSize="14sp"
                        android:textColor="@color/md_theme_light_onPrimary" />

                    <TextView
                        android:id="@+id/tv_stage2_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🔒 需要完成阶段1"
                        android:textSize="14sp"
                        android:textColor="@color/md_theme_light_onPrimary"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 阶段3：综合巩固应用 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_stage3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/gradient_stage3_bg"
                android:padding="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🏆"
                        android:textSize="28sp"
                        android:layout_marginEnd="12dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="阶段3：综合巩固应用"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onPrimary" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="混合练习和挑战关卡"
                            android:textSize="14sp"
                            android:textColor="@color/md_theme_light_onPrimary"
                            android:alpha="0.9"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/tv_stage3_progress"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="进度: 0%"
                        android:textSize="14sp"
                        android:textColor="@color/md_theme_light_onPrimary" />

                    <TextView
                        android:id="@+id/tv_stage3_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🔒 需要完成阶段2"
                        android:textSize="14sp"
                        android:textColor="@color/md_theme_light_onPrimary"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 最近学习记录卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📊 学习进度记录"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onBackground"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:id="@+id/layout_recent_records"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
