<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 正确点击状态 -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/md_theme_light_primaryContainer" />
            <corners android:radius="16dp" />
            <stroke
                android:width="3dp"
                android:color="@color/md_theme_light_primary" />
        </shape>
    </item>
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/md_theme_light_surfaceVariant" />
            <corners android:radius="16dp" />
            <stroke
                android:width="2dp"
                android:color="@color/md_theme_light_outline" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/md_theme_light_surface"
                android:endColor="@color/md_theme_light_surfaceVariant"
                android:angle="135" />
            <corners android:radius="16dp" />
            <stroke
                android:width="1dp"
                android:color="@color/md_theme_light_outline" />
        </shape>
    </item>
    
</selector>
