package com.example.concentration;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.core.content.ContextCompat;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class EffectManager {
    private static EffectManager instance;
    private Context context;
    private Random random;
    
    private EffectManager(Context context) {
        this.context = context.getApplicationContext();
        this.random = new Random();
    }
    
    public static EffectManager getInstance(Context context) {
        if (instance == null) {
            instance = new EffectManager(context);
        }
        return instance;
    }
    
    // 数字消失动画
    public void playNumberDisappearEffect(TextView cell) {
        // 创建缩放和淡出动画
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(cell, "scaleX", 1f, 1.3f, 0f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(cell, "scaleY", 1f, 1.3f, 0f);
        ObjectAnimator alpha = ObjectAnimator.ofFloat(cell, "alpha", 1f, 0.8f, 0f);
        ObjectAnimator rotation = ObjectAnimator.ofFloat(cell, "rotation", 0f, 360f);
        
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(scaleX, scaleY, alpha, rotation);
        animatorSet.setDuration(400);
        
        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                // 动画结束后恢复初始状态
                cell.setScaleX(1f);
                cell.setScaleY(1f);
                cell.setAlpha(1f);
                cell.setRotation(0f);
            }
        });
        
        animatorSet.start();
    }
    
    // 星星闪烁效果
    public void playStarEffect(ViewGroup container, float x, float y) {
        ImageView star = new ImageView(context);
        
        // 创建简单的星星drawable（使用系统图标作为占位符）
        Drawable starDrawable = ContextCompat.getDrawable(context, android.R.drawable.btn_star_big_on);
        star.setImageDrawable(starDrawable);
        
        // 设置星星位置和大小
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(60, 60);
        star.setLayoutParams(params);
        star.setX(x - 30);
        star.setY(y - 30);
        star.setAlpha(0f);
        star.setScaleX(0f);
        star.setScaleY(0f);
        
        container.addView(star);
        
        // 创建星星动画
        ObjectAnimator alphaIn = ObjectAnimator.ofFloat(star, "alpha", 0f, 1f);
        ObjectAnimator scaleXIn = ObjectAnimator.ofFloat(star, "scaleX", 0f, 1.2f);
        ObjectAnimator scaleYIn = ObjectAnimator.ofFloat(star, "scaleY", 0f, 1.2f);
        
        AnimatorSet inSet = new AnimatorSet();
        inSet.playTogether(alphaIn, scaleXIn, scaleYIn);
        inSet.setDuration(200);
        
        ObjectAnimator alphaOut = ObjectAnimator.ofFloat(star, "alpha", 1f, 0f);
        ObjectAnimator scaleXOut = ObjectAnimator.ofFloat(star, "scaleX", 1.2f, 0f);
        ObjectAnimator scaleYOut = ObjectAnimator.ofFloat(star, "scaleY", 1.2f, 0f);
        ObjectAnimator translateY = ObjectAnimator.ofFloat(star, "translationY", 0f, -100f);
        
        AnimatorSet outSet = new AnimatorSet();
        outSet.playTogether(alphaOut, scaleXOut, scaleYOut, translateY);
        outSet.setDuration(600);
        outSet.setStartDelay(200);
        
        AnimatorSet fullSet = new AnimatorSet();
        fullSet.playSequentially(inSet, outSet);
        
        fullSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                container.removeView(star);
            }
        });
        
        fullSet.start();
    }
    
    // 多个星星爆炸效果
    public void playStarBurstEffect(ViewGroup container, float centerX, float centerY) {
        int starCount = 5 + random.nextInt(3); // 5-7个星星
        
        for (int i = 0; i < starCount; i++) {
            float angle = (360f / starCount) * i;
            float radius = 50 + random.nextFloat() * 50;
            
            float x = centerX + (float) Math.cos(Math.toRadians(angle)) * radius;
            float y = centerY + (float) Math.sin(Math.toRadians(angle)) * radius;
            
            // 延迟启动每个星星，创建连锁效果
            container.postDelayed(() -> playStarEffect(container, x, y), i * 100);
        }
    }
    
    // 脉冲效果
    public void playPulseEffect(View view) {
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(view, "scaleX", 1f, 1.1f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(view, "scaleY", 1f, 1.1f, 1f);
        
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(scaleX, scaleY);
        animatorSet.setDuration(300);
        animatorSet.start();
    }
    
    // 错误震动效果
    public void playShakeEffect(View view) {
        ObjectAnimator shake = ObjectAnimator.ofFloat(view, "translationX", 0f, -15f, 15f, -10f, 10f, -5f, 5f, 0f);
        shake.setDuration(500);
        shake.start();
    }
    
    // 彩虹色彩变化效果
    public void playColorChangeEffect(TextView cell) {
        int[] colors = {
            0xFF2196F3, // 蓝色
            0xFF4CAF50, // 绿色
            0xFFFF9800, // 橙色
            0xFF9C27B0, // 紫色
            0xFFF44336  // 红色
        };
        
        ValueAnimator colorAnimator = ValueAnimator.ofArgb(colors);
        colorAnimator.setDuration(1000);
        colorAnimator.addUpdateListener(animation -> {
            int color = (int) animation.getAnimatedValue();
            cell.setTextColor(color);
        });
        
        colorAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                // 恢复原始颜色
                cell.setTextColor(ContextCompat.getColor(context, R.color.text_primary));
            }
        });
        
        colorAnimator.start();
    }
}
