<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape>
            <gradient
                android:startColor="@color/gradient_start_tertiary"
                android:endColor="@color/gradient_end_tertiary"
                android:angle="45" />
            <corners android:radius="12dp" />
            <stroke
                android:width="2dp"
                android:color="@color/md_theme_light_primary" />
        </shape>
    </item>
    <item android:state_selected="true">
        <shape>
            <solid android:color="@color/grid_selected" />
            <corners android:radius="12dp" />
            <stroke
                android:width="2dp"
                android:color="@color/md_theme_light_primary" />
        </shape>
    </item>
    <item>
        <shape>
            <solid android:color="@color/background_card" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="@color/md_theme_light_outline" />
        </shape>
    </item>
</selector>
