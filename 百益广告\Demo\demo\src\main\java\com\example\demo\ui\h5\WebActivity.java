package com.example.demo.ui.h5;

import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v7.app.AppCompatActivity;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.Toast;

import com.example.demo.R;

public class WebActivity extends AppCompatActivity implements View.OnClickListener {

    private EditText et_userId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_web);

        et_userId = findViewById(R.id.a_web_et_userId);

        findViewById(R.id.a_main_web01_btn).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        String userId = et_userId.getText().toString().trim();
        if (TextUtils.isEmpty(userId)) {
            Toast.makeText(this, "userId不能为空，请输入userId", Toast.LENGTH_SHORT).show();
            return;
        }
        boolean b = userId.matches("[1]\\d{10}");
        if (!b) {
            Toast.makeText(this, "userId输入格式错误", Toast.LENGTH_SHORT).show();
            return;
        }
        if (id == R.id.a_main_web01_btn) {//夺宝消消消
            Intent intent = new Intent(this, GameH5Activity.class);
            intent.putExtra("userId", userId);
            startActivity(intent);
        }
    }
}
