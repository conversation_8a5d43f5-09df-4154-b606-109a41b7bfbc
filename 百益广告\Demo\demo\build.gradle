apply plugin: 'com.android.application'

android {
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    compileSdkVersion 28

    defaultConfig {
        applicationId "com.soyea.ryc.bydemo"
        minSdkVersion 24
        targetSdkVersion 28
        versionCode 99999
        versionName "20.0.0"
        multiDexEnabled true
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    signingConfigs {
        releaseConfig {
            storeFile file("ryc.jks")
            storePassword '123456'
            keyAlias = 'ryc'
            keyPassword '123456'
        }
        debug {
            storeFile file("ryc.jks")
            storePassword '123456'
            keyAlias = 'ryc'
            keyPassword '123456'
        }
    }
}

//添加依赖库⽬录
repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar", "*.aar"])

    implementation 'com.android.support:support-v4:28.0.0'
    implementation 'com.android.support:recyclerview-v7:28.0.0'
    implementation 'com.android.support:appcompat-v7:28.0.0'

    implementation  'io.github.scwang90:refresh-layout-kernel:2.0.6'      //核心必须依赖
    implementation  'io.github.scwang90:refresh-header-classics:2.0.6'    //经典刷新头
    implementation  'io.github.scwang90:refresh-footer-classics:2.0.6'    //经典加载

    //广告添加相关依赖包
    implementation "com.android.support:design:28.0.0"
    implementation 'com.android.support:multidex:1.0.3'
    implementation 'com.github.bumptech.glide:glide:4.9.0'
    implementation 'com.google.code.gson:gson:2.8.5'
    //implementation "androidx.legacy:legacy-support-v4:1.0.0"//加入 AndroidX V4 依赖支持库

    //adalliance_adn_sdk需要引入的第三方库支持
    implementation 'com.squareup.okhttp3:okhttp:3.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:3.12.0'
}
