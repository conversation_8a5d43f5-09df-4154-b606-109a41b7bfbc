@echo off
echo 正在构建专注力训练APP...

REM 设置Android环境变量
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%ANDROID_HOME%\platform-tools;%ANDROID_HOME%\build-tools\34.0.0

echo 检查连接的设备...
C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe devices

echo 尝试使用gradle构建...
if exist "gradle-8.5\bin\gradle.bat" (
    gradle-8.5\bin\gradle.bat assembleDebug
) else if exist "gradlew.bat" (
    gradlew.bat assembleDebug
) else (
    echo 未找到gradle，请手动构建项目
    echo 可以在Android Studio中打开项目并构建
)

echo 如果构建成功，尝试安装到设备...
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo 安装APK到设备...
    adb install -r app\build\outputs\apk\debug\app-debug.apk
    echo 启动应用...
    adb shell am start -n com.example.concentration/.MainActivity
) else (
    echo APK文件未找到，请检查构建是否成功
)

pause
