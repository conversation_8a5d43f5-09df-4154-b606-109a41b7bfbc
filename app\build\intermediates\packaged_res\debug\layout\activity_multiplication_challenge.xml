<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_number_bg"
    android:padding="16dp">

    <!-- 顶部栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="九九乘法表挑战"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:gravity="center" />

        <View
            android:layout_width="48dp"
            android:layout_height="48dp" />

    </LinearLayout>

    <!-- 游戏信息栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp">

        <TextView
            android:id="@+id/tv_level"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="关卡: 1"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:gravity="center" />

        <TextView
            android:id="@+id/tv_score"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="得分: 0"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:gravity="center" />

        <TextView
            android:id="@+id/tv_timer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="时间: 00:00"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:gravity="center" />

    </LinearLayout>

    <!-- 题目显示 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp">

        <TextView
            android:id="@+id/tv_question"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="2 × 3 = ?"
            android:textSize="32sp"
            android:textStyle="bold"
            android:textColor="@color/primary_color"
            android:gravity="center"
            android:padding="32dp" />

    </com.google.android.material.card.MaterialCardView>

    <!-- 答案选项 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_answers"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginBottom="24dp" />

    <!-- 底部按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_restart"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="重新开始"
            android:textSize="16sp"
            android:backgroundTint="@color/secondary_color"
            app:cornerRadius="28dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_next"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="下一题"
            android:textSize="16sp"
            android:backgroundTint="@color/primary_color"
            app:cornerRadius="28dp"
            android:enabled="false" />

    </LinearLayout>

</LinearLayout>
