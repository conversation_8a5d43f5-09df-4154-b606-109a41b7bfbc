package com.example.concentration;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.core.content.ContextCompat;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class CelebrationManager {
    private static CelebrationManager instance;
    private Context context;
    private Random random;
    
    private CelebrationManager(Context context) {
        this.context = context.getApplicationContext();
        this.random = new Random();
    }
    
    public static CelebrationManager getInstance(Context context) {
        if (instance == null) {
            instance = new CelebrationManager(context);
        }
        return instance;
    }
    
    // 烟花效果
    public void playFireworksEffect(ViewGroup container) {
        int fireworksCount = 3 + random.nextInt(3); // 3-5个烟花

        for (int i = 0; i < fireworksCount; i++) {
            float x = random.nextFloat() * container.getWidth();
            float y = random.nextFloat() * container.getHeight() * 0.6f; // 上半部分

            // 延迟启动每个烟花
            container.postDelayed(() -> createFirework(container, x, y), i * 500);
        }
    }

    // 大型烟花特效（用于新纪录庆祝）
    public void showFireworks(ViewGroup container, int durationMs) {
        int totalFireworks = 8 + random.nextInt(5); // 8-12个烟花
        int interval = durationMs / totalFireworks;

        for (int i = 0; i < totalFireworks; i++) {
            container.postDelayed(() -> {
                // 随机位置创建烟花
                float x = random.nextFloat() * container.getWidth();
                float y = random.nextFloat() * container.getHeight() * 0.7f;
                createFirework(container, x, y);
            }, i * interval);
        }
    }
    
    private void createFirework(ViewGroup container, float x, float y) {
        // 创建更大的烟花中心点 - 2/3屏幕大小
        ImageView center = new ImageView(context);
        Drawable starDrawable = ContextCompat.getDrawable(context, android.R.drawable.btn_star_big_on);
        center.setImageDrawable(starDrawable);

        // 大幅增大中心点尺寸 - 2/3屏幕宽度
        int screenWidth = container.getWidth();
        int centerSize = (int)(screenWidth * 0.4f); // 40%屏幕宽度作为中心点
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(centerSize, centerSize);
        center.setLayoutParams(params);
        center.setX(x - centerSize/2);
        center.setY(y - centerSize/2);
        center.setAlpha(0f);
        center.setElevation(1000f); // 设置极高的层级确保显示在最前面

        container.addView(center);

        // 中心点闪烁动画 - 更持久更明显
        ObjectAnimator centerAlpha = ObjectAnimator.ofFloat(center, "alpha", 0f, 1f, 0.8f, 1f, 0f);
        ObjectAnimator centerScale = ObjectAnimator.ofFloat(center, "scaleX", 0.5f, 1.5f, 1f);
        ObjectAnimator centerScaleY = ObjectAnimator.ofFloat(center, "scaleY", 0.5f, 1.5f, 1f);

        AnimatorSet centerSet = new AnimatorSet();
        centerSet.playTogether(centerAlpha, centerScale, centerScaleY);
        centerSet.setDuration(600);
        centerSet.start();

        // 创建更多更大的爆炸粒子
        int particleCount = 16 + random.nextInt(12); // 16-28个粒子
        List<ImageView> particles = new ArrayList<>();

        for (int i = 0; i < particleCount; i++) {
            ImageView particle = new ImageView(context);
            particle.setImageDrawable(starDrawable);

            // 大幅增大粒子尺寸 - 与屏幕大小成比例
            int particleSize = (int)(screenWidth * 0.08f); // 8%屏幕宽度
            ViewGroup.LayoutParams particleParams = new ViewGroup.LayoutParams(particleSize, particleSize);
            particle.setLayoutParams(particleParams);
            particle.setX(x - particleSize/2);
            particle.setY(y - particleSize/2);
            particle.setAlpha(0f);
            particle.setElevation(1000f); // 设置极高的层级

            container.addView(particle);
            particles.add(particle);

            // 计算粒子飞行方向 - 大幅增大飞行距离
            float angle = (360f / particleCount) * i + random.nextFloat() * 40 - 20; // 增加随机偏移
            float distance = screenWidth * 0.4f + random.nextFloat() * screenWidth * 0.3f; // 40%-70%屏幕宽度的飞行距离

            float endX = x + (float) Math.cos(Math.toRadians(angle)) * distance;
            float endY = y + (float) Math.sin(Math.toRadians(angle)) * distance;

            // 增强的粒子动画
            AnimatorSet particleSet = new AnimatorSet();

            // 粒子出现动画 - 更明显的缩放效果
            ObjectAnimator alphaIn = ObjectAnimator.ofFloat(particle, "alpha", 0f, 1f);
            ObjectAnimator scaleInX = ObjectAnimator.ofFloat(particle, "scaleX", 0f, 1.5f);
            ObjectAnimator scaleInY = ObjectAnimator.ofFloat(particle, "scaleY", 0f, 1.5f);

            AnimatorSet appearSet = new AnimatorSet();
            appearSet.playTogether(alphaIn, scaleInX, scaleInY);
            appearSet.setDuration(400);
            appearSet.setStartDelay(600); // 等待中心点闪烁完成

            // 粒子移动动画 - 更长的持续时间，使用正确的坐标
            ObjectAnimator moveX = ObjectAnimator.ofFloat(particle, "x", x - particleSize/2, endX - particleSize/2);
            ObjectAnimator moveY = ObjectAnimator.ofFloat(particle, "y", y - particleSize/2, endY - particleSize/2);
            ObjectAnimator scaleOutX = ObjectAnimator.ofFloat(particle, "scaleX", 1.5f, 0.5f);
            ObjectAnimator scaleOutY = ObjectAnimator.ofFloat(particle, "scaleY", 1.5f, 0.5f);
            ObjectAnimator alphaOut = ObjectAnimator.ofFloat(particle, "alpha", 1f, 0f);

            AnimatorSet moveSet = new AnimatorSet();
            moveSet.playTogether(moveX, moveY, scaleOutX, scaleOutY, alphaOut);
            moveSet.setDuration(1500); // 增加持续时间让效果更持久
            moveSet.setStartDelay(1000);

            particleSet.playSequentially(appearSet, moveSet);
            particleSet.start();
        }

        // 延长清理时间以匹配新的动画持续时间
        container.postDelayed(() -> {
            container.removeView(center);
            for (ImageView particle : particles) {
                container.removeView(particle);
            }
        }, 4000); // 增加清理延迟以匹配更长的动画
    }
    
    // 角色舞蹈动画（使用简单的图标代替）
    public void playCharacterDanceEffect(ViewGroup container) {
        ImageView character = new ImageView(context);
        
        // 使用系统图标作为角色
        Drawable characterDrawable = ContextCompat.getDrawable(context, android.R.drawable.sym_def_app_icon);
        character.setImageDrawable(characterDrawable);
        
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(120, 120);
        character.setLayoutParams(params);
        
        float centerX = container.getWidth() / 2f - 60;
        float centerY = container.getHeight() / 2f - 60;
        character.setX(centerX);
        character.setY(centerY);
        character.setAlpha(0f);
        
        container.addView(character);
        
        // 入场动画
        ObjectAnimator fadeIn = ObjectAnimator.ofFloat(character, "alpha", 0f, 1f);
        ObjectAnimator scaleInX = ObjectAnimator.ofFloat(character, "scaleX", 0f, 1f);
        ObjectAnimator scaleInY = ObjectAnimator.ofFloat(character, "scaleY", 0f, 1f);
        
        AnimatorSet enterSet = new AnimatorSet();
        enterSet.playTogether(fadeIn, scaleInX, scaleInY);
        enterSet.setDuration(500);
        
        // 舞蹈动画 - 左右摇摆
        ObjectAnimator dance1 = ObjectAnimator.ofFloat(character, "rotation", 0f, -15f, 15f, 0f);
        dance1.setDuration(600);
        dance1.setRepeatCount(3);
        
        // 跳跃动画
        ObjectAnimator jump = ObjectAnimator.ofFloat(character, "translationY", 0f, -50f, 0f);
        jump.setDuration(400);
        jump.setRepeatCount(2);
        
        // 缩放脉冲
        ObjectAnimator pulse = ObjectAnimator.ofFloat(character, "scaleX", 1f, 1.2f, 1f);
        ObjectAnimator pulseY = ObjectAnimator.ofFloat(character, "scaleY", 1f, 1.2f, 1f);
        pulse.setRepeatCount(4);
        pulseY.setRepeatCount(4);
        AnimatorSet pulseSet = new AnimatorSet();
        pulseSet.playTogether(pulse, pulseY);
        pulseSet.setDuration(300);
        
        // 退场动画
        ObjectAnimator fadeOut = ObjectAnimator.ofFloat(character, "alpha", 1f, 0f);
        ObjectAnimator scaleOutX = ObjectAnimator.ofFloat(character, "scaleX", 1f, 0f);
        ObjectAnimator scaleOutY = ObjectAnimator.ofFloat(character, "scaleY", 1f, 0f);
        
        AnimatorSet exitSet = new AnimatorSet();
        exitSet.playTogether(fadeOut, scaleOutX, scaleOutY);
        exitSet.setDuration(500);
        
        // 组合所有动画
        AnimatorSet fullDance = new AnimatorSet();
        fullDance.playSequentially(enterSet, dance1, jump, pulseSet, exitSet);
        
        fullDance.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                container.removeView(character);
            }
        });
        
        fullDance.start();
    }
    
    // 彩带效果
    public void playConfettiEffect(ViewGroup container) {
        int confettiCount = 15 + random.nextInt(10); // 15-25个彩带
        
        for (int i = 0; i < confettiCount; i++) {
            container.postDelayed(() -> createConfetti(container), i * 100);
        }
    }
    
    private void createConfetti(ViewGroup container) {
        ImageView confetti = new ImageView(context);
        
        // 随机颜色的小方块作为彩带
        int[] colors = {0xFFFF5722, 0xFF2196F3, 0xFF4CAF50, 0xFFFFEB3B, 0xFF9C27B0};
        int color = colors[random.nextInt(colors.length)];
        confetti.setBackgroundColor(color);
        
        int size = 10 + random.nextInt(15);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(size, size);
        confetti.setLayoutParams(params);
        
        float startX = random.nextFloat() * container.getWidth();
        confetti.setX(startX);
        confetti.setY(-50);
        confetti.setRotation(random.nextFloat() * 360);
        
        container.addView(confetti);
        
        // 下落动画
        float endY = container.getHeight() + 50;
        float duration = 2000 + random.nextFloat() * 1000; // 2-3秒
        
        ObjectAnimator fallY = ObjectAnimator.ofFloat(confetti, "y", -50, endY);
        ObjectAnimator fallX = ObjectAnimator.ofFloat(confetti, "x", startX, 
            startX + (random.nextFloat() - 0.5f) * 200); // 左右摆动
        ObjectAnimator rotate = ObjectAnimator.ofFloat(confetti, "rotation", 
            confetti.getRotation(), confetti.getRotation() + 360 * 3);
        
        AnimatorSet fallSet = new AnimatorSet();
        fallSet.playTogether(fallY, fallX, rotate);
        fallSet.setDuration((long) duration);
        
        fallSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                container.removeView(confetti);
            }
        });
        
        fallSet.start();
    }
    
    // 完整庆祝序列
    public void playCelebrationSequence(ViewGroup container) {
        // 立即开始彩带效果
        playConfettiEffect(container);
        
        // 500ms后开始烟花
        container.postDelayed(() -> playFireworksEffect(container), 500);
        
        // 1000ms后开始角色舞蹈
        container.postDelayed(() -> playCharacterDanceEffect(container), 1000);
    }
}
