<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.concentration"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34" />

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!-- 可选权限 动态权限 -->
    <!-- 获取设备标识IMEI。用于标识用户 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <!-- 读写存储权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- 定位权限 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> <!-- 可选权限 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- suppress DeprecatedClassUsageInspection -->
    <uses-permission android:name="android.permission.GET_TASKS" /> <!-- 如果有视频相关的广告且使用textureView播放，请务必添加，否则黑屏 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- MTG将通过此权限在Android R系统上判定广告对应的应用是否在用户的app上安装，避免投放错误的广告，以此提高用户的广告体验。 -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN" />

            <category android:name="android.intent.category.LAUNCHER" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data android:scheme="https" />
        </intent>
    </queries>

    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.VIBRATE" /> <!-- 动态壁纸权限 -->
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="com.asus.msa.SupplementaryDID.ACCESS" />
    <uses-permission android:name="freemme.permission.msa" />
    <uses-permission android:name="com.example.concentration.openadsdk.permission.TT_PANGOLIN" />

    <permission
        android:name="com.example.concentration.openadsdk.permission.TT_PANGOLIN"
        android:protectionLevel="signature" />
    <permission
        android:name="com.example.concentration.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.example.concentration.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.example.concentration.AdApplication"
        android:allowBackup="false"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="false"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true" >
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <provider
            android:name="com.bytedance.sdk.openadsdk.multipro.TTMultiProvider"
            android:authorities="com.example.concentration.TTMultiProvider"
            android:exported="false" />
        <provider
            android:name="com.bytedance.sdk.openadsdk.TTFileProvider"
            android:authorities="com.example.concentration.TTFileProvider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/anythink_bk_tt_file_path" />
        </provider>
        <provider
            android:name="com.qq.e.comm.GDTFileProvider"
            android:authorities="com.example.concentration.gdt.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/anythink_bk_gdt_file_path" />
        </provider>
        <provider
            android:name="com.sigmob.sdk.SigmobFileV4Provider"
            android:authorities="com.example.concentration.sigprovider"
            android:exported="false"
            android:grantUriPermissions="true"
            android:initOrder="200" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/anythink_bk_sigmob_file_path" />
        </provider>

        <!-- 开屏广告Activity -->
        <activity
            android:name="com.example.concentration.SplashActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.example.concentration.MainActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.SchulteGridActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.ThemeSelectionActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.CountdownActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.GameActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.ColorTrainingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.ColorGameActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.MemoryGameMainActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.MemoryGameActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.CelebrationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.MultiplicationTableActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.MultiplicationConceptActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.ConceptVisualDemoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.ConceptInteractiveCountActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.ConceptAnimationDemoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.ConceptTestGameActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.MultiplicationNumberActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.NumberLearningActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.MemoryCardGameActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.RhythmLearningActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.PuzzleChallengeActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.MultiplicationChallengeActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.SettingsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.UsernameSetupActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.RewardsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.example.concentration.TutorialActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.alliance.ssp.ad.activity.SAAllianceWebViewActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleTask" />
        <activity
            android:name="com.alliance.ssp.ad.activity.NMRewardVideoActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.alliance.ssp.ad.activity.AppInfoViewActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent" />

        <service
            android:name="com.alliance.ssp.ad.service.YTSDKDownloadService"
            android:exported="false" />

        <activity
            android:name="com.alliance.ssp.ad.activity.DeepLinkProxyActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent" />

        <provider
            android:name="com.alliance.ssp.ad.utils.NMSSPFileProvider"
            android:authorities="com.example.concentration.NMSSPFileProvider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/nmssp_file_path" />
        </provider>

        <activity
            android:name="com.ads.admob.saas.SaasH5Activity"
            android:configChanges="keyboard|orientation|screenSize"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.ads.admob.saas.H5Activity"
            android:configChanges="keyboard|orientation|screenSize"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.ads.admob.saas.GameH5Activity"
            android:configChanges="keyboard|orientation|screenSize"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.ads.admob.saas.YmActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.ads.admob.saas.VideoFragmentActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.anythink.china.activity.TransparentActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.anythink.dlopt.activity.ApkConfirmDialogActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />

        <provider
            android:name="com.anythink.dlopt.common.ApkFileProvider"
            android:authorities="com.example.concentration.anythink.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/anythink_file_paths" />
        </provider>

        <receiver
            android:name="com.anythink.dlopt.common.NotificationBroadcastReceiver"
            android:exported="false" >
            <intent-filter>
                <action android:name="anythink_action_notification_click" />
                <action android:name="anythink_action_notification_cancel" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.anythink.dlopt.common.service.ApkDownloadService"
            android:exported="false" />

        <activity
            android:name="com.anythink.core.activity.AnyThinkGdprAuthActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.anythink.basead.ui.ATLandscapeActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
            android:exported="false"
            android:launchMode="standard"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.anythink.basead.ui.ATPortraitActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
            android:exported="false"
            android:launchMode="standard"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.anythink.basead.ui.ATLandscapeTranslucentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
            android:exported="false"
            android:launchMode="standard"
            android:theme="@style/anythink_myoffer_half_screen" />
        <activity
            android:name="com.anythink.basead.ui.ATPortraitTranslucentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
            android:exported="false"
            android:launchMode="standard"
            android:theme="@style/anythink_myoffer_half_screen" />
        <activity
            android:name="com.anythink.core.basead.ui.web.WebLandPageActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
            android:exported="false"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Light.NoTitleBar"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.anythink.basead.ui.RewardExitConfirmDialogActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.anythink.basead.ui.activity.ATMixSplashActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:exported="false"
            android:launchMode="standard"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- 如果targetSdkVersion设置值>=24，则强烈建议添加以下provider，否则会影响app变现 -->
        <!-- android:authorities="${applicationId}.bd.provider" authorities中${applicationId}部分必须替换成app自己的包名 -->
        <!-- 原来的FileProvider在新版本中改为BdFileProvider,继承自v4的FileProvider,需要在应用内引用support-v4包 -->
        <provider
            android:name="com.baidu.mobads.sdk.api.BdFileProvider"
            android:authorities="com.example.concentration.bd.provider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/bd_file_paths" />
        </provider> <!-- 落地页配置 -->
        <activity
            android:name="com.baidu.mobads.sdk.api.AppActivity"
            android:configChanges="screenSize|keyboard|keyboardHidden|orientation"
            android:theme="@android:style/Theme.NoTitleBar" /> <!-- 激励视频、全屏视频配置 -->
        <activity
            android:name="com.baidu.mobads.sdk.api.MobRewardVideoActivity"
            android:configChanges="screenSize|orientation|keyboardHidden|uiMode"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.baidu.mobads.sdk.api.BdShellActivity"
            android:configChanges="screenSize|keyboard|keyboardHidden|orientation|smallestScreenSize|screenLayout"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.beizi.ad.AdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:exported="false"
            android:hardwareAccelerated="true" />
        <activity
            android:name="com.beizi.ad.internal.activity.BeiZiInterstitialActivity"
            android:exported="false"
            android:theme="@style/BeiZiTheme.Transparent" />
        <activity
            android:name="com.beizi.ad.internal.activity.DownloadAppInfoActivity"
            android:exported="false" />
        <activity
            android:name="com.beizi.ad.internal.activity.BeiZiDownloadDialogActivity"
            android:exported="false"
            android:theme="@style/BeiZiTheme.Transparent" />

        <service
            android:name="com.beizi.ad.DownloadService"
            android:exported="false" /> <!-- 声明SDK所需要的组件 -->
        <service
            android:name="com.qq.e.comm.DownloadService"
            android:exported="false"
            android:multiprocess="true" /> <!-- 请开发者注意字母的大小写，ADActivity，而不是AdActivity -->
        <activity
            android:name="com.qq.e.ads.ADActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:exported="false"
            android:multiprocess="true" />
        <activity
            android:name="com.qq.e.ads.PortraitADActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:exported="false"
            android:multiprocess="true"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Light.NoTitleBar" />
        <activity
            android:name="com.qq.e.ads.LandscapeADActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:exported="false"
            android:multiprocess="true"
            android:screenOrientation="sensorLandscape"
            android:theme="@android:style/Theme.Light.NoTitleBar" /> <!-- 用于激励视频可选广告的竖屏透明背景activity -->
        <activity
            android:name="com.qq.e.ads.RewardvideoPortraitADActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:exported="false"
            android:multiprocess="true"
            android:theme="@android:style/Theme.Light.NoTitleBar" >
            <meta-data
                android:name="android.notch_support"
                android:value="true" />
        </activity> <!-- 用于激励视频可选广告的横屏透明背景activity -->
        <activity
            android:name="com.qq.e.ads.RewardvideoLandscapeADActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:exported="false"
            android:multiprocess="true"
            android:screenOrientation="landscape"
            android:theme="@android:style/Theme.Light.NoTitleBar" >
            <meta-data
                android:name="android.notch_support"
                android:value="true" />
        </activity>
        <activity
            android:name="com.qq.e.ads.DialogActivity"
            android:exported="false"
            android:multiprocess="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.AdWebViewActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Light.NoTitleBar" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.KsFullScreenVideoActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="behind"
            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.KsFullScreenLandScapeVideoActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="landscape"
            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.KsRewardVideoActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.KSRewardLandScapeVideoActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="landscape"
            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.FeedDownloadActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$KsTrendsActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Light.NoTitleBar" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$ProfileHomeActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$GoodsPlayBackActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$ProfileVideoDetailActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$TubeProfileActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$ChannelDetailActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$TubeDetailActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$EpisodeDetailActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity1"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity2"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity3"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity4"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity5"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity6"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity7"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity8"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity9"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivity10"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleTop1"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleTop2"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleInstance1"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$FragmentActivitySingleInstance2"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kwad.sdk.api.proxy.app.BaseFragmentActivity$DeveloperConfigActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" />

        <service android:name="com.kwad.sdk.api.proxy.app.FileDownloadService$SharedMainProcessService" />
        <service android:name="com.kwad.sdk.api.proxy.app.FileDownloadService$SeparateProcessService" />
        <service
            android:name="com.kwad.sdk.api.proxy.app.DownloadService"
            android:exported="false" />
        <service
            android:name="com.kwad.sdk.api.proxy.app.ServiceProxyRemote"
            android:exported="false"
            android:process=":kssdk_remote" />
        <service
            android:name="com.kwad.sdk.api.proxy.VideoWallpaperService"
            android:exported="true"
            android:permission="android.permission.BIND_WALLPAPER" >
            <intent-filter android:priority="1000" >
                <action android:name="android.service.wallpaper.WallpaperService" />
            </intent-filter>

            <meta-data
                android:name="android.service.wallpaper"
                android:resource="@xml/ksad_wallpaper" />
        </service>

        <provider
            android:name="com.kwad.sdk.api.proxy.app.AdSdkFileProvider"
            android:authorities="com.example.concentration.adFileProvider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/ksad_file_paths" />
        </provider>

        <meta-data
            android:name="ZEUS_PLUGIN_PANGLE"
            android:value="{                 apiVersionCode:6841,                 packageName:com.byted.pangle,                 minPluginVersion:6841,                 internalPath:**********,                 internalVersionCode:6841             }" />
        <meta-data
            android:name="ZEUS_PLUGIN_com.byted.csj.ext"
            android:value="{apiVersionCode:999,packageName:com.byted.csj.ext,minPluginVersion:1000,maxPluginVersion:999999999,internalPath:&apos;&apos;,internalVersionCode:-1, appKey:&apos;&apos;,appSecretKey:&apos;&apos;}" />

        <activity
            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Activity"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="standard"
            android:theme="@android:style/Theme.NoTitleBar"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Portrait_Activity"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize|uiMode"
            android:exported="false"
            android:launchMode="standard"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.NoTitleBar"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Activity_T"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="standard"
            android:theme="@android:style/Theme.Translucent"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Landscape_Activity"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize|uiMode"
            android:exported="false"
            android:launchMode="standard"
            android:screenOrientation="landscape"
            android:theme="@android:style/Theme.NoTitleBar"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_Activity"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:theme="@android:style/Theme.NoTitleBar"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_SingleTask_Activity_T"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.stub.activity.Stub_SingleTask_Activity"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustResize|stateHidden" />

        <provider
            android:name="com.bytedance.sdk.openadsdk.stub.server.DownloaderServerManager"
            android:authorities="com.example.concentration.pangle.servermanager.downloader.com.bytedance.sdk.openadsdk.adhost"
            android:exported="false"
            android:process=":downloader" />
        <provider
            android:name="com.bytedance.sdk.openadsdk.stub.server.MainServerManager"
            android:authorities="com.example.concentration.pangle.servermanager.main"
            android:exported="false" />
        <provider
            android:name="com.bytedance.pangle.provider.MainProcessProviderProxy"
            android:authorities="com.example.concentration.pangle.provider.proxy.main"
            android:exported="false" />
        <provider
            android:name="com.bytedance.pangle.FileProvider"
            android:authorities="com.example.concentration.pangle.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" />

        <activity
            android:name="com.ss.android.downloadlib.addownload.compliance.AppPrivacyPolicyActivity"
            android:exported="false" />
        <activity
            android:name="com.ss.android.downloadlib.addownload.compliance.AppDetailInfoActivity"
            android:exported="false" />
        <activity
            android:name="com.ss.android.downloadlib.activity.TTDelegateActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.ss.android.downloadlib.activity.JumpKllkActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <receiver
            android:name="com.ss.android.downloadlib.core.download.DownloadReceiver"
            android:exported="false" />

        <service
            android:name="com.ss.android.socialbase.appdownloader.DownloadHandlerService"
            android:exported="false"
            android:stopWithTask="true" />

        <activity
            android:name="com.ss.android.socialbase.appdownloader.view.DownloadTaskDeleteActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.ss.android.socialbase.appdownloader.view.JumpUnknownSourceActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Dialog" />

        <service
            android:name="com.ss.android.socialbase.appdownloader.RetryJobSchedulerService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:stopWithTask="true" />
        <service
            android:name="com.ss.android.socialbase.downloader.downloader.IndependentProcessDownloadService"
            android:exported="false"
            android:process=":downloader" >
            <intent-filter>
                <action android:name="com.ss.android.socialbase.downloader.remote" />
            </intent-filter>
        </service>
        <service
            android:name="com.ss.android.socialbase.downloader.notification.DownloadNotificationService"
            android:exported="false"
            android:stopWithTask="true" />
        <service
            android:name="com.ss.android.socialbase.downloader.downloader.DownloadService"
            android:exported="false"
            android:stopWithTask="true" />
        <service
            android:name="com.ss.android.socialbase.downloader.impls.DownloadHandleService"
            android:exported="false"
            android:stopWithTask="true" />
        <service
            android:name="com.ss.android.socialbase.downloader.downloader.SqlDownloadCacheService"
            android:exported="false"
            android:stopWithTask="true" />

        <meta-data
            android:name="ZEUS_PLUGIN_LIVE"
            android:value="{                 apiVersionCode:2114,                 packageName:com.byted.live.lite,                 minPluginVersion:211400,                 maxPluginVersion:999999,                 isSupportLibIsolate:true             }" />
        <meta-data
            android:name="LIVE_API_VERSION_CODE"
            android:value="2114" />

        <activity
            android:name="com.bytedance.android.openliveplugin.stub.activity.DouyinAuthorizeActivityProxy"
            android:exported="true" />
        <activity
            android:name="com.bytedance.android.openliveplugin.stub.activity.DouyinAuthorizeActivityLiveProcessProxy"
            android:exported="true"
            android:process=":bytelive" />

        <provider
            android:name="com.bytedance.android.openliveplugin.process.server.LiveServerManager"
            android:authorities="com.example.concentration.bytedance.android.openliveplugin.process.server.LiveServerManager"
            android:exported="false"
            android:process=":bytelive" />

        <activity
            android:name="com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait"
            android:configChanges="orientation|keyboardHidden|colorMode|uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Light.NoTitleBar" />
        <activity
            android:name="com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait2"
            android:configChanges="orientation|keyboardHidden|colorMode|uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Light.NoTitleBar" />
        <activity
            android:name="com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait3"
            android:configChanges="orientation|keyboardHidden|colorMode|uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Light.NoTitleBar" />
        <activity
            android:name="com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait4"
            android:configChanges="orientation|keyboardHidden|colorMode|uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Light.NoTitleBar" />
        <activity
            android:name="com.bytedance.android.openliveplugin.stub.activity.StubActivity$Activity_Portrait5"
            android:configChanges="orientation|keyboardHidden|colorMode|uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Light.NoTitleBar" />
        <activity
            android:name="com.byted.live.lite.Activity_bytelive_singleTask4"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:process=":bytelive"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_bytelive_singleTop3"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:process=":bytelive"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_bytelive_singleTop2"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:process=":bytelive"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_bytelive_singleTop5"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:process=":bytelive"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_bytelive_singleTop4"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:process=":bytelive"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_bytelive_standard"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="standard"
            android:process=":bytelive"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_bytelive_singleTop1"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:process=":bytelive"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_bytelive_singleTask1"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:process=":bytelive"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_bytelive_singleTop6"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:process=":bytelive"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_bytelive_singleTask2"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:process=":bytelive"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_bytelive_singleTask3"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:process=":bytelive"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_main_singleTask1"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_main_singleTask2"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_main_singleTask3"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_main_singleTask4"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_main_standard"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="standard"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_main_singleTop1"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_main_singleTop2"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_main_singleInstance1"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleInstance"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_main_singleTop5"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_main_singleTop6"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_main_singleTop3"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_main_singleTop4"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.byted.live.lite.Activity_bytelive_singleInstance1"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:launchMode="singleInstance"
            android:process=":bytelive"
            android:windowSoftInputMode="adjustResize|stateHidden" />

        <provider
            android:name="com.byted.live.lite.ServerManager_bytelive"
            android:authorities="com.example.concentration.pangle.servermanager.bytelive.com.byted.live.lite"
            android:exported="false"
            android:process=":bytelive" />
        <provider
            android:name="com.byted.live.lite.ServerManager_push"
            android:authorities="com.example.concentration.pangle.servermanager.push.com.byted.live.lite"
            android:exported="false"
            android:process=":push" />
        <provider
            android:name="com.byted.live.lite.ServerManager_downloader"
            android:authorities="com.example.concentration.pangle.servermanager.downloader.com.byted.live.lite"
            android:exported="false"
            android:process=":downloader" />
        <provider
            android:name="com.sigmob.sdk.SigmobFileProvider"
            android:authorities="com.example.concentration.sigprovider"
            android:exported="false"
            android:grantUriPermissions="true"
            android:initOrder="200" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/sigmob_provider_paths" />
        </provider> <!-- TT 国内 -->
        <activity
            android:name="com.sigmob.sdk.base.common.TransparentAdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:hardwareAccelerated="true"
            android:multiprocess="true"
            android:screenOrientation="behind"
            android:theme="@style/sig_transparent_style" />
        <activity
            android:name="com.sigmob.sdk.base.common.AdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:hardwareAccelerated="true"
            android:multiprocess="true"
            android:screenOrientation="behind"
            android:theme="@android:style/Theme.Light.NoTitleBar" />
        <activity
            android:name="com.sigmob.sdk.base.common.PortraitAdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:hardwareAccelerated="true"
            android:multiprocess="true"
            android:screenOrientation="sensorPortrait"
            android:theme="@android:style/Theme.Light.NoTitleBar" />
        <activity
            android:name="com.sigmob.sdk.base.common.LandscapeAdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:hardwareAccelerated="true"
            android:multiprocess="true"
            android:screenOrientation="sensorLandscape"
            android:theme="@android:style/Theme.Light.NoTitleBar" />
        <activity
            android:name="com.sigmob.sdk.base.common.PortraitTransparentAdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:hardwareAccelerated="true"
            android:multiprocess="true"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/sig_transparent_style" />
        <activity
            android:name="com.sigmob.sdk.base.common.LandscapeTransparentAdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:hardwareAccelerated="true"
            android:multiprocess="true"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/sig_transparent_style" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.example.concentration.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>