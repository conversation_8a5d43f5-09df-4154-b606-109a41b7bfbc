<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/md_theme_light_background"
    android:orientation="vertical">

    <!-- 顶部导航栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical"
        android:background="@color/md_theme_light_surface"
        android:elevation="4dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="游戏教程"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_onSurface"
            android:gravity="center" />

        <ImageButton
            android:id="@+id/btn_skip"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_skip_next"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="跳过"
            android:visibility="visible" />

    </LinearLayout>

    <!-- 主要内容区域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp"
            android:paddingBottom="120dp">

            <!-- 游戏介绍卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@color/md_theme_light_primaryContainer">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <!-- 游戏图标 -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🎮"
                        android:textSize="48sp"
                        android:layout_gravity="center"
                        android:layout_marginBottom="16dp" />

                    <!-- 游戏标题 -->
                    <TextView
                        android:id="@+id/tv_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="游戏标题"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_onPrimaryContainer"
                        android:gravity="center"
                        android:layout_marginBottom="12dp" />

                    <!-- 游戏描述 -->
                    <TextView
                        android:id="@+id/tv_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="游戏描述"
                        android:textSize="16sp"
                        android:textColor="@color/md_theme_light_onPrimaryContainer"
                        android:gravity="center"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 游戏步骤标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🎯 游戏步骤"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onBackground"
                android:layout_marginBottom="16dp"
                android:gravity="center" />

            <!-- 游戏步骤列表 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_steps"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp" />

        </LinearLayout>

    </ScrollView>

    <!-- 底部按钮区域 - 固定在底部 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="20dp"
        android:background="@color/md_theme_light_surface"
        android:elevation="8dp"
        android:gravity="center">

                <!-- 重播教程按钮 -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_replay"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="12dp"
                    android:text="🔄 重播教程"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    app:strokeColor="@color/md_theme_light_primary"
                    app:strokeWidth="2dp"
                    app:cornerRadius="28dp"
                    android:visibility="gone" />

                <!-- 开始游戏按钮 -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_start_game"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:layout_marginStart="12dp"
                    android:text="🚀 开始游戏"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    style="@style/Widget.Material3.Button"
                    app:backgroundTint="@color/md_theme_light_primary"
                    app:cornerRadius="28dp"
                    android:visibility="gone" />

    </LinearLayout>

</LinearLayout>
