package com.example.concentration;

import android.content.Context;
import java.util.HashMap;
import java.util.Map;

public class StoryManager {
    private static StoryManager instance;
    private Context context;
    private Map<String, StoryTheme> storyThemes;
    
    private StoryManager(Context context) {
        this.context = context.getApplicationContext();
        initializeStoryThemes();
    }
    
    public static StoryManager getInstance(Context context) {
        if (instance == null) {
            instance = new StoryManager(context);
        }
        return instance;
    }
    
    private void initializeStoryThemes() {
        storyThemes = new HashMap<>();
        
        // 默认主题 - 数字探险
        StoryTheme defaultTheme = new StoryTheme(
            "default",
            "数字探险",
            "在神秘的数字王国中，你是一位勇敢的探险家！",
            "收集散落的数字宝石，按顺序找到它们来解开古老的谜题。",
            "太棒了！你成功解开了数字谜题，获得了珍贵的宝石！",
            "继续你的探险之旅，更多的谜题等待着你！"
        );
        storyThemes.put("default", defaultTheme);
        
        // 太空主题 - 星际任务
        StoryTheme spaceTheme = new StoryTheme(
            "space",
            "星际救援",
            "在遥远的太空中，你是一名星际救援队员！",
            "按顺序激活控制面板上的数字按钮，修复受损的太空站系统。",
            "出色！太空站系统修复完成，你拯救了整个空间站！",
            "准备好下一个救援任务，银河系需要你的帮助！"
        );
        storyThemes.put("space", spaceTheme);
        
        // 卡通主题 - 魔法学院
        StoryTheme cartoonTheme = new StoryTheme(
            "cartoon",
            "魔法学院",
            "欢迎来到奇妙的魔法学院，你是一位见习魔法师！",
            "按正确的顺序点击魔法数字，施展强大的魔法咒语。",
            "魔法成功！你的咒语创造了美丽的魔法烟花！",
            "继续练习魔法，成为最强大的魔法师！"
        );
        storyThemes.put("cartoon", cartoonTheme);
        
        // 动物主题 - 森林守护者
        StoryTheme animalTheme = new StoryTheme(
            "animal",
            "森林守护者",
            "在神秘的魔法森林中，你是一位森林守护者！",
            "按顺序点击数字花朵，帮助小动物们找到回家的路。",
            "太好了！小动物们安全回家了，森林恢复了和谐！",
            "森林中还有更多小动物需要你的帮助！"
        );
        storyThemes.put("animal", animalTheme);
    }
    
    public StoryTheme getStoryTheme(String themeId) {
        return storyThemes.getOrDefault(themeId, storyThemes.get("default"));
    }
    
    public String getGameStartMessage(String themeId, int level) {
        StoryTheme theme = getStoryTheme(themeId);
        return theme.getIntroduction() + "\n\n第" + level + "关：" + theme.getTaskDescription();
    }
    
    public String getGameCompleteMessage(String themeId, int level) {
        StoryTheme theme = getStoryTheme(themeId);
        return theme.getSuccessMessage();
    }
    
    public String getNextLevelMessage(String themeId, int nextLevel) {
        StoryTheme theme = getStoryTheme(themeId);
        return theme.getNextLevelMessage() + "\n\n准备挑战第" + nextLevel + "关！";
    }
    
    // 内部类：故事主题
    public static class StoryTheme {
        private String id;
        private String name;
        private String introduction;
        private String taskDescription;
        private String successMessage;
        private String nextLevelMessage;
        
        public StoryTheme(String id, String name, String introduction, 
                         String taskDescription, String successMessage, String nextLevelMessage) {
            this.id = id;
            this.name = name;
            this.introduction = introduction;
            this.taskDescription = taskDescription;
            this.successMessage = successMessage;
            this.nextLevelMessage = nextLevelMessage;
        }
        
        // Getters
        public String getId() { return id; }
        public String getName() { return name; }
        public String getIntroduction() { return introduction; }
        public String getTaskDescription() { return taskDescription; }
        public String getSuccessMessage() { return successMessage; }
        public String getNextLevelMessage() { return nextLevelMessage; }
    }
    
    // 获取关卡标题
    public String getLevelTitle(String themeId, int level, int gridSize) {
        StoryTheme theme = getStoryTheme(themeId);
        String difficulty = getDifficultyName(gridSize);
        return theme.getName() + " - 第" + level + "关 (" + difficulty + ")";
    }
    
    private String getDifficultyName(int gridSize) {
        switch (gridSize) {
            case 3: return "新手";
            case 4: return "进阶";
            case 5: return "专家";
            case 6: return "大师";
            default: return "未知";
        }
    }
    
    // 获取鼓励消息
    public String getEncouragementMessage(String themeId) {
        StoryTheme theme = getStoryTheme(themeId);
        String[] messages = {
            "加油！你可以做到的！",
            "专注精神，胜利就在前方！",
            "每一次点击都让你更接近成功！",
            "相信自己，你是最棒的！"
        };
        
        // 根据主题调整消息
        switch (themeId) {
            case "space":
                return "保持冷静，宇航员！任务即将完成！";
            case "cartoon":
                return "魔法的力量在你心中，继续施展咒语！";
            case "animal":
                return "小动物们相信你，森林守护者！";
            default:
                return messages[(int) (Math.random() * messages.length)];
        }
    }
    
    // 获取失败提示消息
    public String getHintMessage(String themeId, int expectedNumber) {
        StoryTheme theme = getStoryTheme(themeId);
        
        switch (themeId) {
            case "space":
                return "注意！需要激活" + expectedNumber + "号控制按钮！";
            case "cartoon":
                return "魔法咒语需要" + expectedNumber + "号魔法数字！";
            case "animal":
                return "小动物们在" + expectedNumber + "号花朵旁等待！";
            default:
                return "寻找数字" + expectedNumber + "，继续你的探险！";
        }
    }
}
