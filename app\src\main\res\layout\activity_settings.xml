<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/md_theme_light_background"
    android:padding="16dp">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="24dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="设置"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_onBackground"
            android:gravity="center"
            android:layout_marginEnd="48dp" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 语音设置 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_voice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/md_theme_light_surface">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="语音播报"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onSurface" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="开启游戏语音提示和朗读功能"
                            android:textSize="14sp"
                            android:textColor="@color/md_theme_light_onSurfaceVariant"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                    <Switch
                        android:id="@+id/switch_voice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 测试模式设置 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_test_mode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/md_theme_light_surface">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="测试模式"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onSurface" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="开启后所有游戏功能都可直接使用"
                            android:textSize="14sp"
                            android:textColor="@color/md_theme_light_onSurfaceVariant"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                    <Switch
                        android:id="@+id/switch_test_mode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 版本信息 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_about"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/md_theme_light_surface">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="关于应用"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_onSurface" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Crazy Brain v1.0\n专注力训练应用"
                        android:textSize="14sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
