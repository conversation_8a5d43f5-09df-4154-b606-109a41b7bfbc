package com.example.demo.utils;

import android.content.Context;
import android.content.SharedPreferences;

/**
 * sp类具体存储文件的方法
 */

public class SharedPreferencesUtils {

    public static boolean getIsFirstInit(Context context) {
        if (context == null) {
            return true;
        }
        SharedPreferences sp = context.getSharedPreferences("TbDemoInitConfig", Context.MODE_PRIVATE);
        return sp.getBoolean("isFirstInit", true);
    }

    public static void setIsFirstInit(Context context, boolean isFirstInit) {
        if (context == null) {
            return;
        }
        SharedPreferences sp = context.getSharedPreferences("TbDemoInitConfig", Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sp.edit();
        editor.putBoolean("isFirstInit", isFirstInit);
        editor.commit();
    }
}
