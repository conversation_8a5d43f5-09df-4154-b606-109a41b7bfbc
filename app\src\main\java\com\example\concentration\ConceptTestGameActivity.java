package com.example.concentration;

import android.animation.ObjectAnimator;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.material.button.MaterialButton;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ConceptTestGameActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private TextView tvTitle, tvQuestion, tvScore, tvProgress;
    private LinearLayout containerOptions;
    private MaterialButton btnNext, btnComplete;
    
    private int currentQuestion = 0;
    private final int totalQuestions = 6;
    private int score = 0;
    private Handler handler = new Handler();
    private boolean answered = false;
    
    // 题目数据：{题目类型, 问题, 正确答案, 错误答案1, 错误答案2}
    private final String[][] questions = {
        {"选择", "3行小鸟，每行4只，总共多少只？", "12", "7", "10"},
        {"选择", "2×5等于多少？", "10", "7", "12"},
        {"判断", "4个3相加等于4×3", "正确", "错误", ""},
        {"选择", "看图：🐸🐸🐸\\n🐸🐸🐸\\n这是几×几？", "2×3", "3×2", "2×2"},
        {"判断", "乘法就是相同数字的连续相加", "正确", "错误", ""},
        {"选择", "5×2的意思是？", "5个2相加", "2个5相加", "5加2"}
    };
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_concept_test_game);
        
        initViews();
        setupClickListeners();
        startQuestion();
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        tvTitle = findViewById(R.id.tv_title);
        tvQuestion = findViewById(R.id.tv_question);
        tvScore = findViewById(R.id.tv_score);
        tvProgress = findViewById(R.id.tv_progress);
        containerOptions = findViewById(R.id.container_options);
        btnNext = findViewById(R.id.btn_next);
        btnComplete = findViewById(R.id.btn_complete);
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        
        btnNext.setOnClickListener(v -> {
            if (currentQuestion < totalQuestions - 1) {
                currentQuestion++;
                startQuestion();
            }
        });
        
        btnComplete.setOnClickListener(v -> {
            setResult(RESULT_OK);
            finish();
        });
    }
    
    private void startQuestion() {
        String[] question = questions[currentQuestion];
        String type = question[0];
        String questionText = question[1];
        String correctAnswer = question[2];
        String wrongAnswer1 = question[3];
        String wrongAnswer2 = question[4];
        
        // 重置状态
        answered = false;
        
        // 更新UI
        tvTitle.setText("概念测试 - 第" + (currentQuestion + 1) + "题");
        tvQuestion.setText(questionText.replace("\\n", "\n"));
        updateProgress();
        updateScore();
        
        // 创建选项
        createOptions(type, correctAnswer, wrongAnswer1, wrongAnswer2);
        
        // 更新按钮状态
        updateButtons();
    }
    
    private void createOptions(String type, String correctAnswer, String wrongAnswer1, String wrongAnswer2) {
        containerOptions.removeAllViews();
        
        List<String> options = new ArrayList<>();
        options.add(correctAnswer);
        if (!wrongAnswer1.isEmpty()) options.add(wrongAnswer1);
        if (!wrongAnswer2.isEmpty()) options.add(wrongAnswer2);
        
        // 打乱选项顺序（简单方式）
        if (currentQuestion % 2 == 1) {
            Collections.reverse(options);
        }
        
        for (String option : options) {
            MaterialButton optionButton = new MaterialButton(this);
            optionButton.setText(option);
            optionButton.setTextSize(16);
            
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            params.setMargins(0, 8, 0, 8);
            optionButton.setLayoutParams(params);
            
            optionButton.setOnClickListener(v -> onOptionClick(optionButton, correctAnswer));
            
            containerOptions.addView(optionButton);
        }
    }
    
    private void onOptionClick(MaterialButton clickedButton, String correctAnswer) {
        if (answered) return;
        
        answered = true;
        String selectedAnswer = clickedButton.getText().toString();
        
        // 禁用所有选项
        for (int i = 0; i < containerOptions.getChildCount(); i++) {
            MaterialButton button = (MaterialButton) containerOptions.getChildAt(i);
            button.setEnabled(false);
        }
        
        if (selectedAnswer.equals(correctAnswer)) {
            // 正确答案
            clickedButton.setBackgroundTintList(getColorStateList(android.R.color.holo_green_light));
            clickedButton.setText(selectedAnswer + " ✅");
            score++;
            showFeedback("太棒了！答对了！🎉", true);
        } else {
            // 错误答案
            clickedButton.setBackgroundTintList(getColorStateList(android.R.color.holo_red_light));
            clickedButton.setText(selectedAnswer + " ❌");
            
            // 显示正确答案
            for (int i = 0; i < containerOptions.getChildCount(); i++) {
                MaterialButton button = (MaterialButton) containerOptions.getChildAt(i);
                if (button.getText().toString().startsWith(correctAnswer)) {
                    button.setBackgroundTintList(getColorStateList(android.R.color.holo_green_light));
                    button.setText(correctAnswer + " ✅");
                    break;
                }
            }
            
            showFeedback("再想想看，正确答案是：" + correctAnswer, false);
        }
        
        updateScore();
    }
    
    private void showFeedback(String message, boolean isCorrect) {
        // 创建反馈文本
        TextView feedbackView = new TextView(this);
        feedbackView.setText(message);
        feedbackView.setTextSize(14);
        feedbackView.setPadding(16, 16, 16, 16);
        feedbackView.setBackgroundResource(isCorrect ? 
            android.R.color.holo_green_light : android.R.color.holo_orange_light);
        feedbackView.setTextColor(getColor(android.R.color.white));
        
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        params.setMargins(0, 16, 0, 0);
        feedbackView.setLayoutParams(params);
        
        containerOptions.addView(feedbackView);
        
        // 添加动画
        ObjectAnimator fadeIn = ObjectAnimator.ofFloat(feedbackView, "alpha", 0f, 1f);
        fadeIn.setDuration(500);
        fadeIn.start();
    }
    
    private void updateProgress() {
        tvProgress.setText("进度：" + (currentQuestion + 1) + "/" + totalQuestions);
    }
    
    private void updateScore() {
        tvScore.setText("得分：" + score + "/" + totalQuestions);
    }
    
    private void updateButtons() {
        if (currentQuestion < totalQuestions - 1) {
            btnNext.setVisibility(View.VISIBLE);
            btnComplete.setVisibility(View.GONE);
        } else {
            btnNext.setVisibility(View.GONE);
            btnComplete.setVisibility(View.VISIBLE);
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
    }
}
