package com.example.concentration;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Intent;
import android.content.SharedPreferences;

import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.GridLayout;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import android.app.AlertDialog;
import android.view.LayoutInflater;
import android.widget.GridLayout;
import java.util.ArrayList;
import java.util.List;

public class ConceptInteractiveCountActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private TextView tvTitle, tvInstruction, tvProgress, tvAdditionFormula, tvMultiplicationFormula;
    private GridLayout gridItems;
    private MaterialButton btnNext, btnComplete, btnReset, btnCheckAnswer;
    private CardView cardFillBlanks;
    private TextInputEditText etRows, etCols;
    private MaterialButton btnRowsSelector, btnColsSelector;
    private int selectedRows = 0, selectedCols = 0;
    
    private int currentExercise = 0;
    private final int totalExercises = 4;
    private int clickedCount = 0;
    private int targetRows, targetCols;
    private int currentRow = 0; // 当前应该点击的行
    private int currentRowClicked = 0; // 当前行已点击的数量
    private List<TextView> itemViews = new ArrayList<>();
    private Handler handler = new Handler();
    private VoiceManager voiceManager;
    private SharedPreferences prefs;

    // 游戏状态
    private enum GameState {
        OBSERVING,    // 观察阶段
        FILL_BLANKS,  // 填空阶段
        CLICKING,     // 点击阶段
        COMPLETED     // 完成阶段
    }
    private GameState currentState = GameState.OBSERVING;
    
    // 练习数据：{行数, 列数, 物品emoji, 描述}
    private final String[][] exercises = {
        {"2", "3", "🍎", "苹果"},
        {"3", "2", "⭐", "星星"},
        {"2", "4", "🌸", "花朵"},
        {"4", "2", "🎈", "气球"}
    };
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_concept_interactive_count);

        voiceManager = VoiceManager.getInstance(this);
        prefs = getSharedPreferences("concept_interactive_prefs", MODE_PRIVATE);

        initViews();
        setupClickListeners();
        loadProgress();
        startExercise();
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        tvTitle = findViewById(R.id.tv_title);
        tvInstruction = findViewById(R.id.tv_instruction);
        tvProgress = findViewById(R.id.tv_progress);
        tvAdditionFormula = findViewById(R.id.tv_addition_formula);
        tvMultiplicationFormula = findViewById(R.id.tv_multiplication_formula);
        gridItems = findViewById(R.id.grid_items);
        btnNext = findViewById(R.id.btn_next);
        btnComplete = findViewById(R.id.btn_complete);
        btnReset = findViewById(R.id.btn_reset);

        // 新增的填空相关组件
        cardFillBlanks = findViewById(R.id.card_fill_blanks);
        etRows = findViewById(R.id.et_rows);
        etCols = findViewById(R.id.et_cols);
        btnCheckAnswer = findViewById(R.id.btn_check_answer);

        // 数字选择按钮设为null，使用EditText点击事件
        btnRowsSelector = null;
        btnColsSelector = null;
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        
        btnNext.setOnClickListener(v -> {
            if (currentExercise < totalExercises - 1) {
                currentExercise++;
                startExercise();
            } else {
                // 所有练习完成，返回结果
                setResult(RESULT_OK);
                finish();
            }
        });
        
        btnComplete.setOnClickListener(v -> {
            // 保存完成进度
            saveProgress();
            setResult(RESULT_OK);
            finish();
        });
        
        btnReset.setOnClickListener(v -> resetExercise());

        btnCheckAnswer.setOnClickListener(v -> checkFillBlanksAnswer());

        // 由于布局中没有专门的选择按钮，直接使用EditText点击事件

        // 恢复EditText的正常输入功能
        etRows.setFocusable(true);
        etRows.setFocusableInTouchMode(true);
        etRows.setClickable(true);

        etCols.setFocusable(true);
        etCols.setFocusableInTouchMode(true);
        etCols.setClickable(true);
    }
    
    private void startExercise() {
        String[] exercise = exercises[currentExercise];
        targetRows = Integer.parseInt(exercise[0]);
        targetCols = Integer.parseInt(exercise[1]);
        String item = exercise[2];
        String description = exercise[3];

        // 重置状态
        clickedCount = 0;
        currentRow = 0;
        currentRowClicked = 0;
        itemViews.clear();
        currentState = GameState.OBSERVING;

        // 更新UI
        tvTitle.setText("第" + (currentExercise + 1) + "题：观察并填空");
        String instruction = "请数一数有几行，每一行有几个" + description + "，然后填空";
        tvInstruction.setText(instruction);
        updateProgress();

        // 语音播放提示（只播放一次）
        voiceManager.speakWithCallback(instruction, new Runnable() {
            @Override
            public void run() {
                // 语音完成后，显示填空区域
                showFillBlanksPhase();
            }
        });

        // 隐藏公式和填空区域
        tvAdditionFormula.setVisibility(View.INVISIBLE);
        tvMultiplicationFormula.setVisibility(View.INVISIBLE);
        cardFillBlanks.setVisibility(View.GONE);

        // 创建物品网格
        createItemGrid(targetRows, targetCols, item);

        // 更新按钮状态
        updateButtons();
    }

    private void showFillBlanksPhase() {
        currentState = GameState.FILL_BLANKS;

        // 显示填空区域
        cardFillBlanks.setVisibility(View.VISIBLE);

        // 清空输入框和选择状态
        etRows.setText("");
        etCols.setText("");
        selectedRows = 0;
        selectedCols = 0;

        // 清空提示文字，让空格完全透明
        etRows.setHint("");
        etCols.setHint("");

        // 更新指导说明 - 空格不显示任何文字
        String[] exercise = exercises[currentExercise];
        String description = exercise[3];
        String fillInstruction = "___行 × ___个 = ?";
        tvInstruction.setText(fillInstruction);

        // 不再播放重复的语音提示
    }

    private void checkFillBlanksAnswer() {
        String rowsText = etRows.getText().toString().trim();
        String colsText = etCols.getText().toString().trim();

        if (rowsText.isEmpty() || colsText.isEmpty()) {
            Toast.makeText(this, "请填写完整的答案", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            int userRows = Integer.parseInt(rowsText);
            int userCols = Integer.parseInt(colsText);

            if (userRows == targetRows && userCols == targetCols) {
                // 答案正确
                showCorrectAnswer();
            } else {
                // 答案错误
                Toast.makeText(this, "答案不正确，请重新观察", Toast.LENGTH_SHORT).show();
                voiceManager.speakWithCallback("答案不正确，请重新观察", null);
            }
        } catch (NumberFormatException e) {
            Toast.makeText(this, "请输入有效的数字", Toast.LENGTH_SHORT).show();
        }
    }

    private void showCorrectAnswer() {
        currentState = GameState.CLICKING;

        // 隐藏填空区域
        cardFillBlanks.setVisibility(View.GONE);

        // 显示等于几
        int total = targetRows * targetCols;
        String resultText = targetRows + "行 × " + targetCols + "个 = " + total;
        tvInstruction.setText(resultText);

        // 显示公式
        showFormulas();

        // 语音读出完整公式
        String speechText = resultText + "。" + targetRows + "乘以" + targetCols + "等于" + total;
        voiceManager.speakWithCallback(speechText, new Runnable() {
            @Override
            public void run() {
                // 语音完成后，等待用户点击"下一课"
                currentState = GameState.COMPLETED;
                updateButtons();
            }
        });
    }



    private void finishActivity() {
        setResult(RESULT_OK);
        finish();
    }
    
    private void createItemGrid(int rows, int cols, String item) {
        gridItems.removeAllViews();
        gridItems.setRowCount(rows * 2); // 每行需要额外空间放分隔线
        gridItems.setColumnCount(cols);

        // 计算动态文字大小和内边距
        float textSize = calculateDynamicTextSize(cols);
        int padding = calculateDynamicPadding(cols);

        for (int row = 0; row < rows; row++) {
            // 创建当前行的物品
            for (int col = 0; col < cols; col++) {
                int index = row * cols + col;
                TextView itemView = new TextView(this);
                itemView.setText(item);
                itemView.setTextSize(textSize);
                itemView.setPadding(padding, padding, padding, padding);
                itemView.setClickable(true);
                itemView.setTag(index); // 存储索引
                itemView.setGravity(android.view.Gravity.CENTER);

                GridLayout.LayoutParams params = new GridLayout.LayoutParams();
                params.width = 0;
                params.height = GridLayout.LayoutParams.WRAP_CONTENT;
                params.columnSpec = GridLayout.spec(col, 1f);
                params.rowSpec = GridLayout.spec(row * 2); // 每行占用2个位置（物品+分隔线）
                params.setMargins(8, 8, 8, 8);
                itemView.setLayoutParams(params);

                // 设置点击事件（初始禁用）
                itemView.setOnClickListener(this::onItemClick);
                itemView.setClickable(false); // 观察阶段禁用点击

                gridItems.addView(itemView);
                itemViews.add(itemView);
            }

            // 在每行后添加分隔线（除了最后一行）
            if (row < rows - 1) {
                View separator = new View(this);
                separator.setBackgroundColor(0xFF4CAF50); // 绿色分隔线
                GridLayout.LayoutParams separatorParams = new GridLayout.LayoutParams();
                separatorParams.rowSpec = GridLayout.spec(row * 2 + 1);
                separatorParams.columnSpec = GridLayout.spec(0, cols);
                separatorParams.width = GridLayout.LayoutParams.MATCH_PARENT;
                separatorParams.height = 4; // 4dp高度
                separatorParams.setMargins(0, 8, 0, 8);
                separator.setLayoutParams(separatorParams);
                gridItems.addView(separator);
            }
        }
    }
    
    private void onItemClick(View view) {
        // 只有在点击阶段才允许点击
        if (currentState != GameState.CLICKING) {
            return;
        }

        TextView itemView = (TextView) view;
        int index = (int) itemView.getTag();

        // 检查是否已经被点击过
        if (itemView.getAlpha() < 1.0f) {
            return; // 已经被点击过
        }

        // 计算点击的行
        int clickedRow = index / targetCols;

        // 检查是否按正确顺序点击（必须按行点击）
        if (clickedRow != currentRow) {
            // 错误提示
            tvInstruction.setText("请先完成第" + (currentRow + 1) + "行的点击！");
            return;
        }

        clickedCount++;
        currentRowClicked++;



        // 添加点击动画
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(itemView, "scaleX", 1f, 1.2f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(itemView, "scaleY", 1f, 1.2f, 1f);
        ObjectAnimator alpha = ObjectAnimator.ofFloat(itemView, "alpha", 1f, 0.5f);

        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(scaleX, scaleY, alpha);
        animatorSet.setDuration(300);
        animatorSet.start();

        // 更新进度
        updateProgress();

        // 检查是否完成当前行
        if (currentRowClicked == targetCols) {
            // 当前行完成
            showRowResult();
            currentRow++;
            currentRowClicked = 0;

            if (currentRow < targetRows) {
                tvInstruction.setText("很好！现在请点击第" + (currentRow + 1) + "行");
            }
        }

        // 检查是否全部完成
        if (clickedCount == targetRows * targetCols) {
            showFinalFormulas();
        }
    }

    private void showRowResult() {
        // 显示当前行的结果
        String rowText = "第" + (currentRow + 1) + "行：" + targetCols + "个";
        tvInstruction.setText(rowText);

        // 如果不是最后一行，显示累计结果
        if (currentRow < targetRows - 1) {
            int totalSoFar = (currentRow + 1) * targetCols;
            tvInstruction.setText(rowText + "，累计：" + totalSoFar + "个");
        }
    }

    private void checkRowCompletion(int clickedIndex) {
        int row = clickedIndex / targetCols;
        boolean rowCompleted = true;
        
        // 检查这一行是否全部被点击
        for (int col = 0; col < targetCols; col++) {
            int index = row * targetCols + col;
            if (itemViews.get(index).getAlpha() >= 1.0f) {
                rowCompleted = false;
                break;
            }
        }
        
        if (rowCompleted) {
            // 显示这一行的加法公式
            showRowFormula(row + 1);
        }
    }
    
    private void showRowFormula(int completedRows) {
        StringBuilder additionBuilder = new StringBuilder();
        for (int i = 0; i < completedRows; i++) {
            if (i > 0) additionBuilder.append(" + ");
            additionBuilder.append(targetCols);
        }
        
        String additionFormula = additionBuilder.toString() + " = " + (completedRows * targetCols);
        tvAdditionFormula.setText("加法：" + additionFormula);
        tvAdditionFormula.setVisibility(View.VISIBLE);
        
        // 添加动画
        ObjectAnimator fadeIn = ObjectAnimator.ofFloat(tvAdditionFormula, "alpha", 0f, 1f);
        fadeIn.setDuration(500);
        fadeIn.start();
    }
    
    private void showFinalFormulas() {
        // 显示完整的乘法公式
        String multiplicationFormula = targetRows + " × " + targetCols + " = " + (targetRows * targetCols);
        tvMultiplicationFormula.setText("乘法：" + multiplicationFormula);
        tvMultiplicationFormula.setVisibility(View.VISIBLE);
        
        // 添加动画
        ObjectAnimator fadeIn = ObjectAnimator.ofFloat(tvMultiplicationFormula, "alpha", 0f, 1f);
        fadeIn.setDuration(500);
        fadeIn.start();
        
        // 更新指导文字
        tvInstruction.setText("太棒了！你发现了吗？" + targetRows + "个" + targetCols + "相加，就等于" + targetRows + "×" + targetCols);
    }
    
    private void updateProgress() {
        tvProgress.setText("进度：" + clickedCount + "/" + (targetRows * targetCols));
    }
    
    private void resetExercise() {
        // 重置游戏状态到观察阶段
        currentState = GameState.OBSERVING;
        clickedCount = 0;

        // 清空输入框
        etRows.setText("");
        etCols.setText("");

        // 重置所有物品的状态
        for (TextView itemView : itemViews) {
            itemView.setAlpha(1.0f);
            itemView.setScaleX(1.0f);
            itemView.setScaleY(1.0f);
        }

        // 隐藏公式
        tvAdditionFormula.setVisibility(View.INVISIBLE);
        tvMultiplicationFormula.setVisibility(View.INVISIBLE);

        // 重新开始当前练习
        startExercise();
    }
    
    private void updateButtons() {
        // 重置按钮始终可见
        btnReset.setVisibility(View.VISIBLE);

        // 根据游戏状态显示按钮
        if (currentState == GameState.COMPLETED) {
            // 始终显示"下一课"按钮，最后一课点击后自动完成
            btnNext.setText(currentExercise < totalExercises - 1 ? "下一课" : "完成学习");
            btnNext.setVisibility(View.VISIBLE);
            btnComplete.setVisibility(View.GONE);
        } else {
            // 游戏进行中，隐藏导航按钮
            btnNext.setVisibility(View.GONE);
            btnComplete.setVisibility(View.GONE);
        }
    }
    
    /**
     * 根据列数计算动态文字大小
     * 确保最多9个动物能在一行内显示
     */
    private float calculateDynamicTextSize(int cols) {
        // 获取屏幕宽度
        android.util.DisplayMetrics displayMetrics = new android.util.DisplayMetrics();
        getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        int screenWidth = displayMetrics.widthPixels;

        // 减去边距和内边距的空间（估算）
        int availableWidth = screenWidth - 100; // 减去左右边距

        // 根据列数计算每个动物的可用宽度
        int itemWidth = availableWidth / cols;

        // 根据可用宽度计算合适的文字大小
        // 文字大小范围：16sp到36sp
        float textSize;
        if (cols <= 3) {
            textSize = 36f; // 3列或更少，使用大字体
        } else if (cols <= 5) {
            textSize = 28f; // 4-5列，使用中等字体
        } else if (cols <= 7) {
            textSize = 22f; // 6-7列，使用较小字体
        } else {
            textSize = 16f; // 8-9列，使用最小字体
        }

        return textSize;
    }

    /**
     * 根据列数计算动态内边距
     */
    private int calculateDynamicPadding(int cols) {
        if (cols <= 3) {
            return 12; // 3列或更少，使用大内边距
        } else if (cols <= 5) {
            return 10; // 4-5列，使用中等内边距
        } else if (cols <= 7) {
            return 6;  // 6-7列，使用较小内边距
        } else {
            return 4;  // 8-9列，使用最小内边距
        }
    }

    private void enableGridClicking() {
        // 启用所有网格项的点击
        for (TextView itemView : itemViews) {
            itemView.setClickable(true);
        }
    }

    private void showFormulas() {
        // 显示加法公式
        StringBuilder additionFormula = new StringBuilder();
        for (int i = 0; i < targetRows; i++) {
            if (i > 0) additionFormula.append(" + ");
            additionFormula.append(targetCols);
        }
        additionFormula.append(" = ").append(targetRows * targetCols);
        tvAdditionFormula.setText("加法：" + additionFormula.toString());
        tvAdditionFormula.setVisibility(View.VISIBLE);

        // 显示乘法公式
        String multiplicationFormula = targetRows + " × " + targetCols + " = " + (targetRows * targetCols);
        tvMultiplicationFormula.setText("乘法：" + multiplicationFormula);
        tvMultiplicationFormula.setVisibility(View.VISIBLE);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
        if (voiceManager != null) {
            voiceManager.stop();
        }
        if (currentNumberDialog != null && currentNumberDialog.isShowing()) {
            currentNumberDialog.dismiss();
        }
    }

    /**
     * 显示数字选择器对话框
     */
    private void showNumberPicker(String title, int min, int max, int current, NumberPickerCallback callback) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("选择" + title);

        // 创建数字按钮网格
        GridLayout gridLayout = new GridLayout(this);
        gridLayout.setColumnCount(3); // 3列布局
        gridLayout.setPadding(32, 32, 32, 32);

        for (int i = min; i <= max; i++) {
            MaterialButton numberBtn = new MaterialButton(this);
            numberBtn.setText(String.valueOf(i));
            numberBtn.setTextSize(18);

            // 设置按钮样式
            GridLayout.LayoutParams params = new GridLayout.LayoutParams();
            params.width = 120;
            params.height = 120;
            params.setMargins(8, 8, 8, 8);
            numberBtn.setLayoutParams(params);

            // 高亮当前选中的数字
            if (i == current) {
                numberBtn.setBackgroundColor(getResources().getColor(R.color.md_theme_light_primary));
                numberBtn.setTextColor(getResources().getColor(R.color.md_theme_light_onPrimary));
            }

            final int number = i;
            numberBtn.setOnClickListener(v -> {
                callback.onNumberSelected(number);
                // 关闭对话框
                if (currentNumberDialog != null) {
                    currentNumberDialog.dismiss();
                }
            });

            gridLayout.addView(numberBtn);
        }

        builder.setView(gridLayout);
        builder.setNegativeButton("取消", null);

        currentNumberDialog = builder.create();
        currentNumberDialog.show();
    }

    // 数字选择回调接口
    private interface NumberPickerCallback {
        void onNumberSelected(int number);
    }

    // 当前显示的数字选择对话框
    private AlertDialog currentNumberDialog;

    private void loadProgress() {
        // 加载上次学习的练习进度
        currentExercise = prefs.getInt("current_exercise", 0);
        if (currentExercise >= totalExercises) {
            currentExercise = 0; // 如果已完成所有练习，从头开始
        }
    }

    private void saveProgress() {
        // 保存当前练习进度
        prefs.edit().putInt("current_exercise", currentExercise + 1).apply();
    }
}
