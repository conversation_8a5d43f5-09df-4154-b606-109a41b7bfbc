package com.example.concentration;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.text.InputFilter;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;

public class UsernameSetupActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private EditText etUsername;
    private Button btnStartTraining, btnSkip;
    private TextView tvWelcome, tvInstruction;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_username_setup);
        
        initViews();
        setupClickListeners();
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        etUsername = findViewById(R.id.et_username);
        btnStartTraining = findViewById(R.id.btn_start_training);
        btnSkip = findViewById(R.id.btn_skip);
        tvWelcome = findViewById(R.id.tv_welcome);
        tvInstruction = findViewById(R.id.tv_instruction);
        
        // 设置输入限制
        etUsername.setFilters(new InputFilter[]{new InputFilter.LengthFilter(5)});
        
        // 检查是否已有用户名
        String existingUsername = getSharedPreferences("user_prefs", MODE_PRIVATE).getString("username", "");
        if (!existingUsername.isEmpty()) {
            etUsername.setText(existingUsername);
            tvWelcome.setText("🎮 欢迎回来！");
            tvInstruction.setText("您可以修改您的名字或直接开始训练：");
            btnStartTraining.setText("开始训练");
        }
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        
        btnStartTraining.setOnClickListener(v -> {
            String username = etUsername.getText().toString().trim();
            if (!username.isEmpty()) {
                saveUsername(username);
                navigateToRewards();
            } else {
                Toast.makeText(this, "请输入您的名字", Toast.LENGTH_SHORT).show();
                etUsername.requestFocus();
            }
        });
        
        btnSkip.setOnClickListener(v -> {
            // 跳过用户名设置，直接返回主界面
            finish();
        });
    }
    
    private void saveUsername(String username) {
        try {
            getSharedPreferences("user_prefs", MODE_PRIVATE)
                .edit()
                .putString("username", username)
                .apply();
        } catch (Exception e) {
            Toast.makeText(this, "保存用户名失败，请重试", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void navigateToRewards() {
        try {
            Intent intent = new Intent(this, RewardsActivity.class);
            startActivity(intent);
            finish();
        } catch (Exception e) {
            Toast.makeText(this, "启动奖励中心失败，请重试", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
}
