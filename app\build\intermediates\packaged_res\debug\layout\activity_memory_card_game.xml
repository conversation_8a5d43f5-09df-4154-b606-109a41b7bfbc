<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_number_bg">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="记忆卡片游戏"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:gravity="center" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_restart"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="重新开始"
            android:textSize="12sp"
            app:cornerRadius="18dp" />

    </LinearLayout>

    <!-- 游戏说明 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="8dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="#80FFFFFF">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🎯 游戏规则：点击消除两个相同的数字卡片"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_primary"
            android:gravity="center"
            android:padding="12dp" />

    </com.google.android.material.card.MaterialCardView>

    <!-- 游戏信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <TextView
            android:id="@+id/tv_score"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="得分: 0"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:gravity="center" />

        <TextView
            android:id="@+id/tv_timer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="时间: 00:00"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:gravity="center" />

    </LinearLayout>

    <!-- 卡片网格 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_cards"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp" />

</LinearLayout>
