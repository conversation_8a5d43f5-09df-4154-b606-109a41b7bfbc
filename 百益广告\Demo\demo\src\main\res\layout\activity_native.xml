<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:gravity="center"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_native"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginLeft="8dp"
            android:layout_marginEnd="8dp"
            android:layout_marginRight="8dp"
            android:gravity="center"
            android:text="加载自渲染"
            android:textColor="#3399cc"
            android:textSize="14sp" />
    </LinearLayout>

    <com.qq.e.ads.nativ.widget.NativeAdContainer
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_marginTop="80dp"
        android:background="#00000000">

        <RelativeLayout
            android:id="@+id/container_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/container_NATIVE_2IMAGE_2TEXT"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/container_NATIVE_2IMAGE_2TEXT_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginRight="16dp"
                    android:gravity="center"
                    tools:text="标题，短文字" />

                <TextView
                    android:id="@+id/container_NATIVE_2IMAGE_2TEXT_desc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginRight="16dp"
                    android:gravity="center"
                    tools:text="描述，长文字" />

                <ImageView
                    android:id="@+id/container_NATIVE_2IMAGE_2TEXT_iconUrl"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_gravity="center"
                    android:layout_marginTop="12dp"
                    android:contentDescription="Icon图片地址"
                    android:gravity="center" />

                <ImageView
                    android:id="@+id/container_NATIVE_2IMAGE_2TEXT_imgUrl"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="12dp"
                    android:contentDescription="大图地址"
                    android:gravity="center" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/container_NATIVE_3IMAGE"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/container_NATIVE_3IMAGE_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginRight="16dp"
                    android:gravity="center"
                    tools:text="标题，短文字" />

                <TextView
                    android:id="@+id/container_NATIVE_3IMAGE_desc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginRight="16dp"
                    android:gravity="center"
                    tools:text="描述，长文字" />

                <ImageView
                    android:id="@+id/container_NATIVE_3IMAGE_imgList1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="16dp"
                    android:contentDescription="三小图的地址1"
                    android:gravity="center" />

                <ImageView
                    android:id="@+id/container_NATIVE_3IMAGE_imgList2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="12dp"
                    android:contentDescription="三小图的地址2"
                    android:gravity="center" />

                <ImageView
                    android:id="@+id/container_NATIVE_3IMAGE_imgList3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="12dp"
                    android:contentDescription="三小图的地址3"
                    android:gravity="center" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/container_NATIVE_1IMAGE_2TEXT"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/container_NATIVE_1IMAGE_2TEXT_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginRight="16dp"
                    android:gravity="center"
                    tools:text="标题，短文字" />

                <TextView
                    android:id="@+id/container_NATIVE_1IMAGE_2TEXT_desc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginRight="16dp"
                    android:gravity="center"
                    tools:text="描述，长文字" />

                <ImageView
                    android:id="@+id/container_NATIVE_1IMAGE_2TEXT_imgUrl"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:contentDescription="大图地址"
                    android:gravity="center" />
            </LinearLayout>

            <RelativeLayout
                android:id="@+id/container_NATIVE_VIDEO"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone">

                <com.qq.e.ads.nativ.MediaView
                    android:id="@+id/container_NATIVE_VIDEO_MediaView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/container_NATIVE_VIDEO_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginRight="16dp"
                        android:gravity="center"
                        tools:text="标题，短文字" />

                    <TextView
                        android:id="@+id/container_NATIVE_VIDEO_desc"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginRight="16dp"
                        android:gravity="center"
                        tools:text="描述，长文字" />
                </LinearLayout>
            </RelativeLayout>

            <Button
                android:id="@+id/container_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="16dp"
                android:text="点击下载" />
        </RelativeLayout>
    </com.qq.e.ads.nativ.widget.NativeAdContainer>
</RelativeLayout>