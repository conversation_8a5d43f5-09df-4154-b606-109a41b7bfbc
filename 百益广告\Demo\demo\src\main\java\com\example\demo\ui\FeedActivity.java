package com.example.demo.ui;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.Nullable;
import android.support.v7.app.AppCompatActivity;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.view.View;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.example.demo.R;
import com.example.demo.adapter.XRecyclerViewAdapter;
import com.example.demo.bean.AdapterTypeBean;
import com.scwang.smart.refresh.footer.ClassicsFooter;
import com.scwang.smart.refresh.header.ClassicsHeader;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnLoadMoreListener;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//信息流
public class FeedActivity extends AppCompatActivity implements View.OnClickListener {

    private FrameLayout mExpressContainer;
    private EditText edit_width;
    private EditText edit_high;

    private XRecyclerViewAdapter adapter;
    private List<AdapterTypeBean> mData = new ArrayList<>();
    private SmartRefreshLayout refreshLayout;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_feed);
        mExpressContainer = (FrameLayout) findViewById(R.id.express_container);

        refreshLayout = (SmartRefreshLayout) findViewById(R.id.refreshLayout);
        refreshLayout.setRefreshHeader(new ClassicsHeader(this));
        refreshLayout.setRefreshFooter(new ClassicsFooter(this));
        refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(RefreshLayout refreshlayout) {
                refreshlayout.finishRefresh(2000/*,false*/);//传入false表示刷新失败
                refresh();
            }
        });
        refreshLayout.setOnLoadMoreListener(new OnLoadMoreListener() {
            @Override
            public void onLoadMore(RefreshLayout refreshlayout) {
                refreshlayout.finishLoadMore(1000/*,false*/);//传入false表示加载失败
                loadMore();
            }
        });
        RecyclerView recyclerView = findViewById(R.id.a_RecyclerView);
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        recyclerView.setLayoutManager(layoutManager);
        adapter = new XRecyclerViewAdapter(this, mData, new XRecyclerViewAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(View itemView, AdapterTypeBean itemData, int viewType, int position) {

            }
        }, R.layout.item_native_0, R.layout.item_native_1) {
            @Override
            public void onBindViewHolder(XViewHolder viewHolder, AdapterTypeBean itemData, int position) {
                Map<String, Object> itemMap = itemData.getData();
                if (itemData.getType() == 0) {
                    TextView textView = viewHolder.getTextView(R.id.textview);
                    String p = position + "";
                    textView.setText(p);
                } else if (itemData.getType() == 1) {
                    FrameLayout expressContainer = (FrameLayout) viewHolder.get(R.id.express_container);
                    //取控件当前的布局参数
                    RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) expressContainer.getLayoutParams();
                    float width = com.ads.admob.utils.ValueUtils.getFloat(edit_width.getText().toString().trim());
                    float height = com.ads.admob.utils.ValueUtils.getFloat(edit_high.getText().toString().trim());
                    params.width = com.ads.admob.utils.DensityUtil.dp2px(FeedActivity.this, width == 0 ? 350 : width);//设置宽度值
                    params.height = com.ads.admob.utils.DensityUtil.dp2px(FeedActivity.this, height == 0 ? 250 : height);//设置高度值
                    expressContainer.setLayoutParams(params);//使设置好的布局参数应用到控件
                    com.ads.admob.bean.FeedPosition feedPosition = (com.ads.admob.bean.FeedPosition) itemMap.get("position");
                    if (feedPosition != null) {
                        feedPosition.showFeed(FeedActivity.this, expressContainer);
                    }
                }
            }
        };
        recyclerView.setAdapter(adapter);

        findViewById(R.id.btn_express_load).setOnClickListener(this);
        findViewById(R.id.btn_express_load2).setOnClickListener(this);
        edit_width = findViewById(R.id.edit_width);
        edit_high = findViewById(R.id.edit_high);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_express_load:
                mExpressContainer.removeAllViews();
                mExpressContainer.setVisibility(View.VISIBLE);
                refreshLayout.setVisibility(View.GONE);
                loadFeed(mExpressContainer);
                break;
            case R.id.btn_express_load2:
                mExpressContainer.setVisibility(View.GONE);
                refreshLayout.setVisibility(View.VISIBLE);
                refresh();
                break;
        }
    }

    //加载信息流
    private void loadFeed(final FrameLayout container) {
        int width = Integer.parseInt("".equals(edit_width.getText().toString().trim()) ? "350" : edit_width.getText().toString().trim());
        int height = Integer.parseInt("".equals(edit_high.getText().toString().trim()) ? "0" : edit_high.getText().toString().trim());
        com.by.mob.config.ByFeedConfig config = new com.by.mob.config.ByFeedConfig.Builder()
                .codeId("1330513366195650582")//平台申请的代码位id
                .viewWidth(width)//期望模板view的width，默认值350（单位dp）
                .viewHigh(height)//期望模板view的height，默认值0（自适应，单位dp）
                .build();
        com.by.mob.ByManager.loadFeed(config, this, new com.by.mob.ByManager.FeedLoadListener() {
            @Override
            public void onFail(String s) {
                //加载失败
                Toast.makeText(FeedActivity.this, s, Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onLoad(com.ads.admob.bean.FeedPosition position) {
                if (container == null) {
                    for (AdapterTypeBean bean : mData) {
                        if (bean.getType() == 1) {
                            Map<String, Object> map = bean.getData();
                            if (map.isEmpty()) {
                                map.put("position", position);
                            }
                            adapter.notifyDataSetChanged();
                        }
                    }
                } else {
                    position.showFeed(FeedActivity.this, container);
                }
            }

            @Override
            public void onClicked() {

            }

            @Override
            public void onDismiss() {
                //关闭
            }

            @Override
            public void onExposure(com.by.mob.bean.Position position) {

            }

            @Override
            public void onVideoReady() {
                //视频准备就绪开始播放（非视频不回调）
            }

            @Override
            public void onVideoComplete() {
                //视频播放完成（非视频不回调）
            }
        });
    }

    private Handler handlerMain;
    private boolean loadingFeed = false;

    private void refresh() {
        if (loadingFeed) {
            Toast.makeText(this, "请求频率过快", Toast.LENGTH_SHORT).show();
            return;
        }
        if (handlerMain == null) {
            handlerMain = new Handler(Looper.getMainLooper());
        }
        handlerMain.postDelayed(new Runnable() {
            @Override
            public void run() {
                loadingFeed = false;
            }
        }, 3000);
        loadingFeed = true;
        mData.clear();
        adapter.notifyDataSetChanged();
        setData();
    }

    private void loadMore() {
        setData();
    }

    private void setData() {
        for (int i = 0; i < 10; i++) {
            AdapterTypeBean bean = new AdapterTypeBean();
            if (i == 5) {
                bean.setType(1);
                Map<String, Object> map = new HashMap<>();
                bean.setData(map);
            } else {
                bean.setType(0);
            }
            mData.add(bean);
            adapter.notifyDataSetChanged();
        }
        loadFeed(null);//传入null，缓存不展现
    }
}
