package com.example.concentration;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import java.util.ArrayList;
import java.util.List;

public class RewardsActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private TextView tvWelcomeUsername;
    private TextView tvTotalCrystals, tvTotalFragments, tvTotalScore, tvTotalGames;
    private TextView tvSchulteStats, tvMemoryStats, tvColorStats, tvMultiplicationStats;
    private LinearProgressIndicator progressOverall, progressSchulte, progressMemory, progressColor, progressMultiplication;
    private RecyclerView recyclerAchievements, recyclerRecentRecords;
    
    private SharedPreferences prefs;
    private CrystalManager crystalManager;
    private GameDataManager gameDataManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_rewards);
        
        prefs = getSharedPreferences("game_prefs", MODE_PRIVATE);
        crystalManager = CrystalManager.getInstance(this);
        gameDataManager = new GameDataManager(this);
        
        initViews();
        setupClickListeners();
        loadRewardsData();
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        
        // 总体统计
        tvTotalCrystals = findViewById(R.id.tv_total_crystals);
        tvTotalFragments = findViewById(R.id.tv_total_fragments);
        tvTotalScore = findViewById(R.id.tv_total_score);
        tvTotalGames = findViewById(R.id.tv_total_games);
        
        // 各游戏统计
        tvSchulteStats = findViewById(R.id.tv_schulte_stats);
        tvMemoryStats = findViewById(R.id.tv_memory_stats);
        tvColorStats = findViewById(R.id.tv_color_stats);
        tvMultiplicationStats = findViewById(R.id.tv_multiplication_stats);
        
        // 进度条
        progressOverall = findViewById(R.id.progress_overall);
        progressSchulte = findViewById(R.id.progress_schulte);
        progressMemory = findViewById(R.id.progress_memory);
        progressColor = findViewById(R.id.progress_color);
        progressMultiplication = findViewById(R.id.progress_multiplication);
        
        // 列表
        recyclerAchievements = findViewById(R.id.recycler_achievements);
        recyclerRecentRecords = findViewById(R.id.recycler_recent_records);
        
        setupRecyclerViews();
    }
    
    private void setupRecyclerViews() {
        recyclerAchievements.setLayoutManager(new LinearLayoutManager(this));
        recyclerRecentRecords.setLayoutManager(new LinearLayoutManager(this));
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
    }
    
    private void loadRewardsData() {
        loadCrystalData();
        loadGameStats();
        loadProgressData();
        loadAchievements();
        loadRecentRecords();
    }
    
    private void loadCrystalData() {
        // 加载水晶数据
        int totalCrystals = crystalManager.getTotalCrystals();
        int totalFragments = crystalManager.getTotalFragments();
        
        tvTotalCrystals.setText("💎 " + totalCrystals);
        tvTotalFragments.setText("✨ " + totalFragments + "/30");
    }
    
    private void loadGameStats() {
        // 加载游戏统计数据
        int totalScore = prefs.getInt("total_score", 0);
        int totalGames = prefs.getInt("total_games", 0);
        
        tvTotalScore.setText("总分: " + totalScore);
        tvTotalGames.setText("总游戏数: " + totalGames);
        
        // 各游戏统计
        loadSchulteStats();
        loadMemoryStats();
        loadColorStats();
        loadMultiplicationStats();
    }
    
    private void loadSchulteStats() {
        int bestTime = prefs.getInt("schulte_best_time", 0);
        int gamesPlayed = prefs.getInt("schulte_games_played", 0);
        double avgTime = prefs.getFloat("schulte_avg_time", 0);
        
        String stats = String.format("最佳: %ds | 平均: %.1fs | 游戏数: %d", 
                bestTime / 1000, avgTime / 1000, gamesPlayed);
        tvSchulteStats.setText(stats);
    }
    
    private void loadMemoryStats() {
        int bestScore = prefs.getInt("memory_best_score", 0);
        int gamesPlayed = prefs.getInt("memory_games_played", 0);
        double avgScore = prefs.getFloat("memory_avg_score", 0);
        
        String stats = String.format("最佳: %d | 平均: %.1f | 游戏数: %d", 
                bestScore, avgScore, gamesPlayed);
        tvMemoryStats.setText(stats);
    }
    
    private void loadColorStats() {
        int bestTime = prefs.getInt("color_best_time", 0);
        int gamesPlayed = prefs.getInt("color_games_played", 0);
        double avgTime = prefs.getFloat("color_avg_time", 0);
        
        String stats = String.format("最佳: %ds | 平均: %.1fs | 游戏数: %d", 
                bestTime / 1000, avgTime / 1000, gamesPlayed);
        tvColorStats.setText(stats);
    }
    
    private void loadMultiplicationStats() {
        int stage1Progress = prefs.getInt("stage1_progress", 0);
        int stage2Progress = prefs.getInt("stage2_progress", 0);
        int stage3Progress = prefs.getInt("stage3_progress", 0);
        
        String stats = String.format("阶段1: %d%% | 阶段2: %d%% | 阶段3: %d%%", 
                stage1Progress, stage2Progress, stage3Progress);
        tvMultiplicationStats.setText(stats);
    }
    
    private void loadProgressData() {
        // 计算总体进度
        int schulteProgress = calculateSchulteProgress();
        int memoryProgress = calculateMemoryProgress();
        int colorProgress = calculateColorProgress();
        int multiplicationProgress = calculateMultiplicationProgress();
        
        int overallProgress = (schulteProgress + memoryProgress + colorProgress + multiplicationProgress) / 4;
        
        progressOverall.setProgress(overallProgress);
        progressSchulte.setProgress(schulteProgress);
        progressMemory.setProgress(memoryProgress);
        progressColor.setProgress(colorProgress);
        progressMultiplication.setProgress(multiplicationProgress);
    }
    
    private int calculateSchulteProgress() {
        // 基于最佳时间和游戏数量计算进度
        int gamesPlayed = prefs.getInt("schulte_games_played", 0);
        int bestTime = prefs.getInt("schulte_best_time", Integer.MAX_VALUE);
        
        int gameProgress = Math.min(100, gamesPlayed * 10); // 每玩一次增加10%，最多100%
        int timeProgress = 0;
        if (bestTime < Integer.MAX_VALUE) {
            // 30秒以下为100%，60秒以上为0%
            timeProgress = Math.max(0, Math.min(100, (60000 - bestTime) * 100 / 30000));
        }
        
        return (gameProgress + timeProgress) / 2;
    }
    
    private int calculateMemoryProgress() {
        int gamesPlayed = prefs.getInt("memory_games_played", 0);
        int bestScore = prefs.getInt("memory_best_score", 0);
        
        int gameProgress = Math.min(100, gamesPlayed * 10);
        int scoreProgress = Math.min(100, bestScore * 10); // 假设10分为满分
        
        return (gameProgress + scoreProgress) / 2;
    }
    
    private int calculateColorProgress() {
        int gamesPlayed = prefs.getInt("color_games_played", 0);
        int bestTime = prefs.getInt("color_best_time", Integer.MAX_VALUE);
        
        int gameProgress = Math.min(100, gamesPlayed * 10);
        int timeProgress = 0;
        if (bestTime < Integer.MAX_VALUE) {
            timeProgress = Math.max(0, Math.min(100, (30000 - bestTime) * 100 / 20000));
        }
        
        return (gameProgress + timeProgress) / 2;
    }
    
    private int calculateMultiplicationProgress() {
        int stage1 = prefs.getInt("stage1_progress", 0);
        int stage2 = prefs.getInt("stage2_progress", 0);
        int stage3 = prefs.getInt("stage3_progress", 0);
        
        return (stage1 + stage2 + stage3) / 3;
    }
    
    private void loadAchievements() {
        try {
            List<Achievement> achievements = generateAchievements();
            if (achievements == null) {
                achievements = new ArrayList<>();
            }
            AchievementAdapter adapter = new AchievementAdapter(achievements);
            recyclerAchievements.setAdapter(adapter);
        } catch (Exception e) {
            // 如果加载失败，显示空列表
            AchievementAdapter adapter = new AchievementAdapter(new ArrayList<>());
            recyclerAchievements.setAdapter(adapter);
        }
    }
    
    private void loadRecentRecords() {
        try {
            List<GameRecord> records = gameDataManager.getRecentRecords(10);
            if (records == null) {
                records = new ArrayList<>();
            }
            RecentRecordsAdapter adapter = new RecentRecordsAdapter(records);
            recyclerRecentRecords.setAdapter(adapter);
        } catch (Exception e) {
            // 如果加载失败，显示空列表
            RecentRecordsAdapter adapter = new RecentRecordsAdapter(new ArrayList<>());
            recyclerRecentRecords.setAdapter(adapter);
        }
    }
    
    private List<Achievement> generateAchievements() {
        List<Achievement> achievements = new ArrayList<>();
        
        // 基于统计数据生成成就
        int totalGames = prefs.getInt("total_games", 0);
        int totalCrystals = crystalManager.getTotalCrystals();
        
        // 游戏次数成就
        achievements.add(new Achievement("🎮", "游戏新手", "完成10次游戏", totalGames >= 10));
        achievements.add(new Achievement("🎯", "游戏达人", "完成50次游戏", totalGames >= 50));
        achievements.add(new Achievement("🏆", "游戏大师", "完成100次游戏", totalGames >= 100));
        
        // 水晶收集成就
        achievements.add(new Achievement("💎", "水晶收集者", "收集10个水晶", totalCrystals >= 10));
        achievements.add(new Achievement("✨", "水晶大师", "收集50个水晶", totalCrystals >= 50));
        
        // 各游戏专项成就
        addSchulteAchievements(achievements);
        addMemoryAchievements(achievements);
        addColorAchievements(achievements);
        addMultiplicationAchievements(achievements);
        
        return achievements;
    }
    
    private void addSchulteAchievements(List<Achievement> achievements) {
        int bestTime = prefs.getInt("schulte_best_time", Integer.MAX_VALUE);
        achievements.add(new Achievement("⚡", "舒尔特速度王", "30秒内完成舒尔特方格", bestTime <= 30000));
    }
    
    private void addMemoryAchievements(List<Achievement> achievements) {
        int bestScore = prefs.getInt("memory_best_score", 0);
        achievements.add(new Achievement("🧠", "记忆大师", "记忆游戏获得满分", bestScore >= 100));
    }
    
    private void addColorAchievements(List<Achievement> achievements) {
        int bestTime = prefs.getInt("color_best_time", Integer.MAX_VALUE);
        achievements.add(new Achievement("🌈", "色彩专家", "20秒内完成颜色识别", bestTime <= 20000));
    }
    
    private void addMultiplicationAchievements(List<Achievement> achievements) {
        int stage1 = prefs.getInt("stage1_progress", 0);
        int stage2 = prefs.getInt("stage2_progress", 0);
        int stage3 = prefs.getInt("stage3_progress", 0);
        
        achievements.add(new Achievement("📚", "乘法学者", "完成乘法阶段1", stage1 >= 100));
        achievements.add(new Achievement("🎓", "乘法专家", "完成乘法阶段2", stage2 >= 100));
        achievements.add(new Achievement("👑", "乘法大师", "完成乘法阶段3", stage3 >= 100));
    }
    
    // 成就数据类
    public static class Achievement {
        public String icon;
        public String title;
        public String description;
        public boolean unlocked;
        
        public Achievement(String icon, String title, String description, boolean unlocked) {
            this.icon = icon;
            this.title = title;
            this.description = description;
            this.unlocked = unlocked;
        }
    }
    
    // 游戏记录数据类
    public static class GameRecord {
        public String gameType;
        public String result;
        public String time;
        public long timestamp;
        
        public GameRecord(String gameType, String result, String time, long timestamp) {
            this.gameType = gameType;
            this.result = result;
            this.time = time;
            this.timestamp = timestamp;
        }
    }
}
