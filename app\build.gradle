apply plugin: 'com.android.application'

//添加依赖库目录
repositories {
    flatDir {
        dirs 'libs'
    }
}

android {
    namespace 'com.example.concentration'
    compileSdkVersion 34
    buildToolsVersion "34.0.0"

    defaultConfig {
        applicationId "com.example.concentration"
        minSdkVersion 24
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"
        multiDexEnabled true

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    //广告添加相关依赖包
    implementation fileTree(dir: "libs", include: ["*.jar", "*.aar"])//导入广告相关aar和jar包
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.github.bumptech.glide:glide:4.9.0'
    implementation 'com.google.code.gson:gson:2.8.5'
    implementation "androidx.legacy:legacy-support-v4:1.0.0"//加入 AndroidX V4 依赖支持库

    //adalliance_adn_sdk需要引入的第三方库支持
    implementation 'com.squareup.okhttp3:okhttp:3.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:3.12.0'

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}