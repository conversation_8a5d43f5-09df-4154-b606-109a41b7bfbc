package com.example.demo.ui;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v7.app.AppCompatActivity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.Toast;

import com.example.demo.R;

//Banner
public class BannerActivity extends AppCompatActivity implements View.OnClickListener {

    private FrameLayout mBannerContainer;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_banner);
        mBannerContainer = findViewById(R.id.banner_container);
        findViewById(R.id.btn_banner).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_banner:
                loadBanner();
                break;
        }
    }

    //加载Banner
    private void loadBanner() {
        com.by.mob.config.ByBannerConfig config = new com.by.mob.config.ByBannerConfig.Builder()
                .codeId("1330513198897446943")//平台申请的代码位id
                .viewWidth(350)//期望模板view的width（height自适应），默认值350（单位dp）
                .container(mBannerContainer)//承载视图的容器
                .build();
        com.by.mob.ByManager.loadBanner(config, this, new com.by.mob.ByManager.BannerLoadListener() {
            @Override
            public void onFail(String s) {
                //加载失败
                Toast.makeText(BannerActivity.this, s, Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onClicked() {

            }

            @Override
            public void onDismiss() {
                //关闭
            }

            @Override
            public void onExposure(com.by.mob.bean.Position position) {

            }
        });
    }
}
