<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary">

    <!-- 应用Logo和名称 -->
    <LinearLayout
        android:id="@+id/app_info_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="vertical"
        android:gravity="center">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@mipmap/ic_launcher"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="疯狂大脑"
            android:textSize="24sp"
            android:textColor="@android:color/white"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="专注力训练"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:layout_marginTop="8dp" />

    </LinearLayout>

    <!-- 开屏广告容器 -->
    <FrameLayout
        android:id="@+id/splash_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- 版权信息 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="32dp"
        android:text="© 2025 疯狂大脑"
        android:textSize="12sp"
        android:textColor="@android:color/white"
        android:alpha="0.7" />

</RelativeLayout>
