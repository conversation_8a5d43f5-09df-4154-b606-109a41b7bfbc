<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:fillViewport="true">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center">

    <!-- 庆祝容器 -->
    <FrameLayout
        android:id="@+id/celebration_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- 内容容器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_gravity="center">

        <!-- 移除星星图片以节省空间 -->

        <!-- 副标题 -->
        <TextView
            android:id="@+id/tv_sub_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="挑战成功！"
            android:textSize="32sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:layout_marginBottom="24dp"
            android:gravity="center" />

        <!-- 时间结果卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@color/md_theme_light_primaryContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_time_result"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="完成时间: --"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onPrimaryContainer"
                    android:gravity="center" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 最佳记录卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@color/md_theme_light_secondaryContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_best_record"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="最佳记录: --"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSecondaryContainer"
                    android:gravity="center" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 水晶奖励卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_crystal_reward"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:visibility="gone"
            app:cardCornerRadius="20dp"
            app:cardElevation="12dp"
            app:cardBackgroundColor="#FFD700">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="24dp"
                android:gravity="center_vertical"
                android:background="@drawable/gradient_gold">

                <TextView
                    android:id="@+id/tv_crystal_emoji"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🔷"
                    android:textSize="56sp"
                    android:layout_marginEnd="20dp"
                    android:shadowColor="#80000000"
                    android:shadowDx="2"
                    android:shadowDy="2"
                    android:shadowRadius="4" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_crystal_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="获得水晶奖励！"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="#000000"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_crystal_description"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="+3个舒尔特水晶碎片"
                        android:textSize="18sp"
                        android:textColor="#000000" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </FrameLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="24dp">

        <!-- 再来一次按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_play_again"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="再来一次"
            android:textSize="18sp"
            android:textStyle="bold"
            style="@style/Widget.MaterialComponents.Button"
            app:backgroundTint="@color/md_theme_light_primary"
            android:textColor="@android:color/white"
            app:cornerRadius="16dp"
            android:elevation="6dp"
            android:layout_marginBottom="12dp" />

        <!-- 返回主页按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_back_to_main"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="返回主页"
            android:textSize="18sp"
            android:textStyle="bold"
            style="@style/Widget.MaterialComponents.Button"
            app:backgroundTint="@color/md_theme_light_primary"
            android:textColor="@android:color/white"
            app:cornerRadius="16dp"
            android:elevation="6dp" />

    </LinearLayout>

</LinearLayout>

</ScrollView>
