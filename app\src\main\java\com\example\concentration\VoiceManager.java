package com.example.concentration;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.speech.tts.TextToSpeech;
import android.speech.tts.UtteranceProgressListener;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import java.util.Locale;

public class VoiceManager implements TextToSpeech.OnInitListener {
    private static final String TAG = "VoiceManager";
    private static VoiceManager instance;
    private TextToSpeech tts;
    private Context context;
    private boolean isInitialized = false;
    private boolean isEnabled = true;
    private SharedPreferences prefs;
    
    private VoiceManager(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences("voice_settings", Context.MODE_PRIVATE);
        this.isEnabled = prefs.getBoolean("voice_enabled", true);
        initTTS();
    }

    // 预热TTS，减少首次使用延迟
    public void warmUp() {
        if (tts != null && isInitialized) {
            // 播放一个很短的无声音频来预热TTS
            tts.speak("", TextToSpeech.QUEUE_FLUSH, null, "warmup");
        }
    }
    
    public static synchronized VoiceManager getInstance(Context context) {
        if (instance == null) {
            instance = new VoiceManager(context);
        }
        return instance;
    }
    
    private void initTTS() {
        try {
            tts = new TextToSpeech(context, this);
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize TTS", e);
        }
    }
    
    @Override
    public void onInit(int status) {
        if (status == TextToSpeech.SUCCESS) {
            int result = tts.setLanguage(Locale.CHINESE);
            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                // 如果中文不支持，使用英文
                tts.setLanguage(Locale.ENGLISH);
            }
            
            // 设置语音参数 - 小女孩轻快声音
            tts.setSpeechRate(1.2f); // 较快的语速
            tts.setPitch(1.3f); // 较高音调，模拟小女孩声音
            
            isInitialized = true;
            Log.d(TAG, "TTS initialized successfully");
        } else {
            Log.e(TAG, "TTS initialization failed");
        }
    }
    
    public void speak(String text) {
        if (!isEnabled || !isInitialized || tts == null) {
            return;
        }
        
        try {
            tts.speak(text, TextToSpeech.QUEUE_FLUSH, null, null);
        } catch (Exception e) {
            Log.e(TAG, "Failed to speak: " + text, e);
        }
    }
    
    public void speakWithDelay(String text, long delayMs) {
        if (!isEnabled || !isInitialized) {
            return;
        }

        new android.os.Handler().postDelayed(() -> speak(text), delayMs);
    }

    /**
     * 播放语音并在完成后执行回调
     */
    public void speakWithCallback(String text, Runnable onComplete) {
        Log.d(TAG, "speakWithCallback called: enabled=" + isEnabled + ", initialized=" + isInitialized + ", tts=" + (tts != null));

        if (!isEnabled || !isInitialized || tts == null) {
            Log.w(TAG, "TTS not ready, executing callback immediately");
            if (onComplete != null) {
                new Handler(Looper.getMainLooper()).post(onComplete);
            }
            return;
        }

        try {
            String utteranceId = "utterance_" + System.currentTimeMillis();
            Bundle params = new Bundle();

            // 设置语音完成监听器
            tts.setOnUtteranceProgressListener(new android.speech.tts.UtteranceProgressListener() {
                @Override
                public void onStart(String utteranceId) {
                    Log.d(TAG, "TTS started: " + text);
                }

                @Override
                public void onDone(String utteranceId) {
                    Log.d(TAG, "TTS completed: " + text);
                    if (onComplete != null) {
                        new android.os.Handler(android.os.Looper.getMainLooper()).post(onComplete);
                    }
                }

                @Override
                public void onError(String utteranceId) {
                    Log.e(TAG, "TTS error: " + text);
                    if (onComplete != null) {
                        new android.os.Handler(android.os.Looper.getMainLooper()).post(onComplete);
                    }
                }
            });

            int result = tts.speak(text, TextToSpeech.QUEUE_FLUSH, params, utteranceId);
            Log.d(TAG, "TTS speak result: " + result + " for text: " + text);
        } catch (Exception e) {
            Log.e(TAG, "Failed to speak with callback: " + text, e);
            if (onComplete != null) {
                new Handler(Looper.getMainLooper()).post(onComplete);
            }
        }
    }
    
    // 乘法表相关语音
    public void speakMultiplication(int num1, int num2, int result) {
        String text = num1 + "乘以" + num2 + "等于" + result;
        speak(text);
    }
    
    public void speakMultiplicationQuestion(int num1, int num2) {
        String text = num1 + "乘以" + num2 + "等于几";
        speak(text);
    }
    
    // 倒计时语音
    public void speakCountdown(int number) {
        if (number > 0) {
            speak(String.valueOf(number));
        } else {
            speak("开始");
        }
    }

    /**
     * 停止语音播放
     */
    public void stop() {
        if (tts != null && isInitialized) {
            tts.stop();
        }
    }
    
    // 游戏反馈语音
    public void speakCorrect() {
        speak("正确");
    }
    
    public void speakWrong() {
        speak("错误");
    }
    
    public void speakGameComplete() {
        speak("恭喜完成");
    }
    
    public void speakGameStart() {
        speak("游戏开始");
    }
    
    // 数字语音
    public void speakNumber(int number) {
        speak(String.valueOf(number));
    }
    
    // 颜色语音
    public void speakColor(String colorName) {
        speak(colorName);
    }
    
    // 设置相关
    public void setEnabled(boolean enabled) {
        this.isEnabled = enabled;
        prefs.edit().putBoolean("voice_enabled", enabled).apply();
    }
    
    public boolean isEnabled() {
        return isEnabled;
    }
    
    public boolean isInitialized() {
        return isInitialized;
    }
    
    public void shutdown() {
        if (tts != null) {
            tts.stop();
            tts.shutdown();
            tts = null;
        }
        isInitialized = false;
    }
    
    // 释放资源
    public static void release() {
        if (instance != null) {
            instance.shutdown();
            instance = null;
        }
    }
}
