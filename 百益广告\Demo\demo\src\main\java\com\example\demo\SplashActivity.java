package com.example.demo;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.support.annotation.Nullable;
import android.support.v7.app.AppCompatActivity;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.widget.FrameLayout;
import android.widget.Toast;

import com.example.demo.utils.SharedPreferencesUtils;

//开屏
public class SplashActivity extends AppCompatActivity {

    // 设置一个变量来控制当前开屏页面是否可以跳转，
    // 当开屏为普链类时，点击会打开一个落地页，此时开发者还不能打开自己的App主页。
    // 当从落地页返回以后，才可以跳转到开发者自己的App主页；
    // 当开屏是App类时只会下载App。
    private boolean canJump;

    private FrameLayout mSplashContainer;

    private Dialog dialog;

    private final String TAG = "Demo___";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        requestWindowFeature(Window.FEATURE_NO_TITLE);// 隐藏标题栏
        getSupportActionBar().hide();// 隐藏ActionBar
        //getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);// 隐藏状态栏

        setContentView(R.layout.activity_splash);
        mSplashContainer = (FrameLayout) findViewById(R.id.splash_container);

        if (SharedPreferencesUtils.getIsFirstInit(getApplicationContext())) {
            //隐私政策示例
            dialog = com.ads.admob.utils.DialogUtils.show(this, "SDK", new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    SharedPreferencesUtils.setIsFirstInit(SplashActivity.this, false);
                    initOaid();
                    initTb();
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            loadSplash();
                        }
                    }, 1000);
                    dialog.dismiss();
                }
            });
        } else {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    loadSplash();
                }
            }, 1000);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        next();
        canJump = true;
    }

    @Override
    protected void onPause() {
        super.onPause();
        canJump = false;
    }

    private void next() {
        if (canJump) {
            //跳转页面的逻辑
            Intent intent = new Intent(this, MainActivity.class);
            startActivity(intent);
            finish();
        } else {
            canJump = true;
        }
    }

    //广告初始化【TbManager.init】之前执行oaid_sdk初始化操作
    private void initOaid() {
        //System.loadLibrary("msaoaidsec");
        int code = com.bun.miitmdid.core.MdidSdkHelper.InitSdk(this, true,
                new com.bun.miitmdid.interfaces.IIdentifierListener() {
                    @Override
                    public void OnSupport(boolean b, com.bun.miitmdid.interfaces.IdSupplier idSupplier) {

                    }
                });
    }

    //SDK初始化
    private void initTb() {
        Log.d(TAG, "initTb");
        com.by.mob.ByManager.init(this, App.config, new com.by.mob.ByManager.IsInitListener() {
            @Override
            public void onFail(String s) {
                Log.d(TAG, "onFail=" + s);
            }

            @Override
            public void onSuccess() {
                Log.d(TAG, "onSuccess");
            }

            @Override
            public void onDpSuccess() {
                Log.d(TAG, "onDpSuccess");
            }
        });
    }

    //加载开屏
    private void loadSplash() {
        com.by.mob.config.BySplashConfig config = new com.by.mob.config.BySplashConfig.Builder()
                .codeId(TbConfig.splashCodeId)//平台申请的代码位id
                .container(mSplashContainer)//承载视图的容器。可⾃定义⾼度⽐例,但不能低于0.75
                .build();
        com.by.mob.ByManager.loadSplash(config, this, new com.by.mob.ByManager.SplashLoadListener() {
            @Override
            public void onFail(String s) {
                Log.d(TAG, "onFail=" + s);
                //加载失败
                Toast.makeText(SplashActivity.this, s, Toast.LENGTH_SHORT).show();
                next();
            }

            @Override
            public void onTick(long l) {
                Log.d(TAG, "onTick");
            }

            @Override
            public void onClicked() {
                Log.d(TAG, "onClicked");
            }

            @Override
            public void onDismiss() {
                Log.d(TAG, "onDismiss");
                //被关闭
                next();
            }

            @Override
            public void onExposure(com.by.mob.bean.Position position) {
                Log.d(TAG, "onExposure");
            }
        });
    }
}
