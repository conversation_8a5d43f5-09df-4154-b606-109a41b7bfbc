<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:gravity="center_vertical"
    android:background="?attr/selectableItemBackground">

    <TextView
        android:id="@+id/tv_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="🏆"
        android:textSize="24sp"
        android:layout_marginEnd="16dp" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="成就标题"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_onSurface" />

        <TextView
            android:id="@+id/tv_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="成就描述"
            android:textSize="14sp"
            android:textColor="@color/md_theme_light_onSurfaceVariant"
            android:layout_marginTop="2dp" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="🔒 未解锁"
        android:textSize="12sp"
        android:textColor="@color/md_theme_light_onSurfaceVariant"
        android:layout_marginStart="8dp" />

</LinearLayout>
