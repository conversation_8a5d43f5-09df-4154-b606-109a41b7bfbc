package com.example.concentration;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

public class ThemeSelectionActivity extends AppCompatActivity {
    
    private ThemeManager themeManager;
    private GameDataManager gameDataManager;
    
    private CardView themeDefault, themeAnimal, themeSpace, themeCartoon;
    private TextView animalStatus, spaceStatus, cartoonStatus;
    private ImageView btnBack;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_theme_selection);
        
        themeManager = ThemeManager.getInstance(this);
        gameDataManager = new GameDataManager(this);
        
        initViews();
        setupClickListeners();
        updateThemeStatus();
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        themeDefault = findViewById(R.id.theme_default);
        themeAnimal = findViewById(R.id.theme_animal);
        themeSpace = findViewById(R.id.theme_space);
        themeCartoon = findViewById(R.id.theme_cartoon);
        
        animalStatus = findViewById(R.id.animal_status);
        spaceStatus = findViewById(R.id.space_status);
        cartoonStatus = findViewById(R.id.cartoon_status);
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        
        themeDefault.setOnClickListener(v -> selectTheme(ThemeManager.ThemeType.DEFAULT));
        themeAnimal.setOnClickListener(v -> selectTheme(ThemeManager.ThemeType.ANIMAL));
        themeSpace.setOnClickListener(v -> selectTheme(ThemeManager.ThemeType.SPACE));
        themeCartoon.setOnClickListener(v -> selectTheme(ThemeManager.ThemeType.CARTOON));
    }
    
    private void selectTheme(ThemeManager.ThemeType theme) {
        boolean testMode = SettingsActivity.isTestModeEnabled(this);

        if (themeManager.isThemeUnlocked(theme) || testMode) {
            themeManager.setCurrentTheme(theme);
            String message = "已切换到" + theme.getName();
            if (testMode && !themeManager.isThemeUnlocked(theme)) {
                message += " (测试模式)";
            }
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show();

            // 直接返回到方格游戏主页，不跳转到APP首页
            finish();
        } else {
            int totalGames = gameDataManager.getTotalCount();
            int needed = theme.getUnlockRequirement() - totalGames;
            Toast.makeText(this,
                "还需要完成 " + needed + " 次训练才能解锁" + theme.getName(),
                Toast.LENGTH_LONG).show();
        }
    }
    
    private void updateThemeStatus() {
        int totalGames = gameDataManager.getTotalCount();
        boolean testMode = SettingsActivity.isTestModeEnabled(this);

        // 检查并解锁新主题
        themeManager.checkAndUnlockThemes(totalGames);

        // 更新动物主题状态
        if (themeManager.isThemeUnlocked(ThemeManager.ThemeType.ANIMAL) || testMode) {
            if (testMode && !themeManager.isThemeUnlocked(ThemeManager.ThemeType.ANIMAL)) {
                animalStatus.setText("✓ 测试解锁");
            } else {
                animalStatus.setText("✓ 已解锁");
            }
            animalStatus.setTextColor(getResources().getColor(R.color.theme_animal_primary));
            themeAnimal.setAlpha(1.0f);
        } else {
            int needed = ThemeManager.ThemeType.ANIMAL.getUnlockRequirement() - totalGames;
            animalStatus.setText("🔒 还需" + needed + "次");
            animalStatus.setTextColor(getResources().getColor(R.color.md_theme_light_onSurfaceVariant));
            themeAnimal.setAlpha(0.6f);
        }

        // 更新太空主题状态
        if (themeManager.isThemeUnlocked(ThemeManager.ThemeType.SPACE) || testMode) {
            if (testMode && !themeManager.isThemeUnlocked(ThemeManager.ThemeType.SPACE)) {
                spaceStatus.setText("✓ 测试解锁");
            } else {
                spaceStatus.setText("✓ 已解锁");
            }
            spaceStatus.setTextColor(getResources().getColor(R.color.theme_space_primary));
            themeSpace.setAlpha(1.0f);
        } else {
            int needed = ThemeManager.ThemeType.SPACE.getUnlockRequirement() - totalGames;
            spaceStatus.setText("🔒 还需" + needed + "次");
            spaceStatus.setTextColor(getResources().getColor(R.color.md_theme_light_onSurfaceVariant));
            themeSpace.setAlpha(0.6f);
        }

        // 更新卡通主题状态
        if (themeManager.isThemeUnlocked(ThemeManager.ThemeType.CARTOON) || testMode) {
            if (testMode && !themeManager.isThemeUnlocked(ThemeManager.ThemeType.CARTOON)) {
                cartoonStatus.setText("✓ 测试解锁");
            } else {
                cartoonStatus.setText("✓ 已解锁");
            }
            cartoonStatus.setTextColor(getResources().getColor(R.color.theme_cartoon_primary));
            themeCartoon.setAlpha(1.0f);
        } else {
            int needed = ThemeManager.ThemeType.CARTOON.getUnlockRequirement() - totalGames;
            cartoonStatus.setText("🔒 还需" + needed + "次");
            cartoonStatus.setTextColor(getResources().getColor(R.color.md_theme_light_onSurfaceVariant));
            themeCartoon.setAlpha(0.6f);
        }
        
        // 高亮当前选中的主题
        highlightCurrentTheme();
    }
    
    private void highlightCurrentTheme() {
        // 重置所有主题的边框
        resetThemeBorders();
        
        // 高亮当前主题
        ThemeManager.ThemeType currentTheme = themeManager.getCurrentTheme();
        switch (currentTheme) {
            case DEFAULT:
                highlightTheme(themeDefault);
                break;
            case ANIMAL:
                if (themeManager.isThemeUnlocked(ThemeManager.ThemeType.ANIMAL)) {
                    highlightTheme(themeAnimal);
                }
                break;
            case SPACE:
                if (themeManager.isThemeUnlocked(ThemeManager.ThemeType.SPACE)) {
                    highlightTheme(themeSpace);
                }
                break;
            case CARTOON:
                if (themeManager.isThemeUnlocked(ThemeManager.ThemeType.CARTOON)) {
                    highlightTheme(themeCartoon);
                }
                break;
        }
    }
    
    private void resetThemeBorders() {
        themeDefault.setCardElevation(6);
        themeAnimal.setCardElevation(6);
        themeSpace.setCardElevation(6);
        themeCartoon.setCardElevation(6);
    }
    
    private void highlightTheme(CardView themeCard) {
        themeCard.setCardElevation(12);
    }
}
