package com.example.concentration;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;

public class NumberSelectionAdapter extends RecyclerView.Adapter<NumberSelectionAdapter.NumberViewHolder> {
    
    private List<MultiplicationNumberActivity.NumberItem> numberItems;
    private OnNumberClickListener listener;
    
    public interface OnNumberClickListener {
        void onNumberClick(MultiplicationNumberActivity.NumberItem item);
    }
    
    public NumberSelectionAdapter(List<MultiplicationNumberActivity.NumberItem> numberItems, OnNumberClickListener listener) {
        this.numberItems = numberItems;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public NumberViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_number_selection, parent, false);
        return new NumberViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull NumberViewHolder holder, int position) {
        MultiplicationNumberActivity.NumberItem item = numberItems.get(position);
        
        // 设置数字
        holder.tvNumber.setText(String.valueOf(item.number));
        
        // 设置进度
        holder.progressBar.setProgress(item.progress);
        holder.tvProgress.setText(item.progress + "%");
        
        // 设置最佳时间
        holder.tvBestTime.setText("最佳: " + item.bestTime);
        
        // 设置游戏次数
        holder.tvTotalGames.setText("已玩: " + item.totalGames + "次");
        
        // 设置解锁状态
        if (item.isUnlocked) {
            holder.cardView.setAlpha(1.0f);
            holder.cardView.setClickable(true);
            holder.tvStatus.setText("✅ 已解锁");
            holder.tvStatus.setTextColor(Color.parseColor("#4CAF50"));
            
            // 根据进度设置卡片颜色
            if (item.progress >= 80) {
                holder.cardView.setCardBackgroundColor(Color.parseColor("#E8F5E8"));
            } else if (item.progress >= 50) {
                holder.cardView.setCardBackgroundColor(Color.parseColor("#FFF3E0"));
            } else {
                holder.cardView.setCardBackgroundColor(Color.WHITE);
            }
        } else {
            holder.cardView.setAlpha(0.5f);
            holder.cardView.setClickable(false);
            holder.tvStatus.setText("🔒 未解锁");
            holder.tvStatus.setTextColor(Color.parseColor("#9E9E9E"));
            holder.cardView.setCardBackgroundColor(Color.parseColor("#F5F5F5"));
        }
        
        // 设置点击事件
        holder.cardView.setOnClickListener(v -> {
            if (item.isUnlocked && listener != null) {
                listener.onNumberClick(item);
            }
        });
    }
    
    @Override
    public int getItemCount() {
        return numberItems.size();
    }
    
    static class NumberViewHolder extends RecyclerView.ViewHolder {
        CardView cardView;
        TextView tvNumber;
        TextView tvProgress;
        TextView tvBestTime;
        TextView tvTotalGames;
        TextView tvStatus;
        ProgressBar progressBar;
        
        NumberViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_number);
            tvNumber = itemView.findViewById(R.id.tv_number);
            tvProgress = itemView.findViewById(R.id.tv_progress);
            tvBestTime = itemView.findViewById(R.id.tv_best_time);
            tvTotalGames = itemView.findViewById(R.id.tv_total_games);
            tvStatus = itemView.findViewById(R.id.tv_status);
            progressBar = itemView.findViewById(R.id.progress_bar);
        }
    }
}
