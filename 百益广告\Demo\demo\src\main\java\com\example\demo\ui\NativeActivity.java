package com.example.demo.ui;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v7.app.AppCompatActivity;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.bumptech.glide.Glide;
import com.example.demo.R;

import java.util.ArrayList;
import java.util.List;

//自渲染
public class NativeActivity extends AppCompatActivity implements View.OnClickListener {

    private List<com.qq.e.ads.nativ.NativeUnifiedADData> dataList = new ArrayList<>();
    private com.qq.e.ads.nativ.NativeUnifiedADData data;

    private com.qq.e.ads.nativ.widget.NativeAdContainer container;
    private View downloadLayout;

    private View container_native_2IMAGE_2TEXT;
    private View container_native_3IMAGE;
    private View container_native_1IMAGE_2TEXT;
    private View container_native_VIDEO;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_native);
        findViewById(R.id.btn_native).setOnClickListener(this);
        container = findViewById(R.id.container);
        downloadLayout = findViewById(R.id.container_button);
        downloadLayout.setVisibility(View.INVISIBLE);

        //加载自渲染需要通过 getAdPatternType() 判断样式，返回值对应 AdPatternType类里的常量：
        //如果返回 NATIVE_2IMAGE_2TEXT，通过 getIconUrl() 和 getImgUrl() 获取图片地址
        //如果返回 NATIVE_3IMAGE，通过 getImgList() 获取三个小图的地址
        //如果返回 NATIVE_1IMAGE_2TEXT，通过 getImgUrl() 获取图片地址
        //如果代码位不支持视频，这里不会出现 NATIVE_VIDEO
        container_native_2IMAGE_2TEXT = findViewById(R.id.container_NATIVE_2IMAGE_2TEXT);
        container_native_3IMAGE = findViewById(R.id.container_NATIVE_3IMAGE);
        container_native_1IMAGE_2TEXT = findViewById(R.id.container_NATIVE_1IMAGE_2TEXT);
        container_native_VIDEO = findViewById(R.id.container_NATIVE_VIDEO);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (data != null) {
            // 必须要在Actiivty.onResume()时通知到内容数据，以便重置并恢复状态
            data.resume();
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_native:
                dataList.clear();//ListView中展示，可在下拉刷新页面时，清空dataList
                container_native_2IMAGE_2TEXT.setVisibility(View.GONE);
                container_native_3IMAGE.setVisibility(View.GONE);
                container_native_1IMAGE_2TEXT.setVisibility(View.GONE);
                container_native_VIDEO.setVisibility(View.GONE);
                loadNative();
                break;
        }
    }

    //加载自渲染
    private void loadNative() {
        com.by.mob.config.ByNativeConfig config = new com.by.mob.config.ByNativeConfig.Builder()
                .codeId("1376876229310824503")//平台申请的代码位id
                .count(3)//单次请求数量，返回不大于 count 的数量
                .build();
        com.by.mob.ByManager.loadNative(config, this, new com.by.mob.ByManager.NativeLoadListener() {
            @Override
            public void onLoad(com.ads.admob.bean.NativePosition position, List<com.qq.e.ads.nativ.NativeUnifiedADData> list, com.ads.admob.bean.NativeShowRequest nativeShowRequest) {
                downloadLayout.setVisibility(View.VISIBLE);
                dataList.addAll(list);
                int index = 0;
                com.qq.e.ads.nativ.NativeUnifiedADData nativeUnifiedData = dataList.get(index);
                //绘制view
                drawView(position, nativeUnifiedData, nativeShowRequest);
            }

            @Override
            public void onFail(String s) {
                //加载失败
                Toast.makeText(NativeActivity.this, s, Toast.LENGTH_SHORT).show();
            }
        });
    }

    //展现自渲染
    private void showNative(com.ads.admob.bean.NativePosition position, final com.qq.e.ads.nativ.NativeUnifiedADData nativeUnifiedData, com.ads.admob.bean.NativeShowRequest bean, com.qq.e.ads.nativ.MediaView mediaView) {
        // NativeUnifiedData 请求原始数据
        // NativeShowRequest 请求原始数据
        // NativeContainer 容器
        // downloadButton 容器内的点击按钮
        // MediaView 播放控件（必须为容器NativeContainer的子View）（没有可传null）
        // autoPlayMuted 设置视频在预览页自动播放时是否静音
        com.by.mob.ByManager.showNative(this, position, nativeUnifiedData, bean, container, downloadLayout, mediaView, true, new com.by.mob.ByManager.NativeShowListener() {
            @Override
            public void onClicked() {
                Log.d("NativeActivity", "onClicked()");
                data = nativeUnifiedData;
            }

            @Override
            public void onExposure(com.by.mob.bean.Position position) {
                Log.d("NativeActivity", "onExposure()");
            }

            @Override
            public void onVideoCompleted() {

            }

            @Override
            public void onVideoLoaded(int i) {

            }

            @Override
            public void onVideoPause() {

            }

            @Override
            public void onVideoResume() {

            }

            @Override
            public void onVideoStart() {

            }
        });
    }

    //绘制view
    private void drawView(com.ads.admob.bean.NativePosition position, com.qq.e.ads.nativ.NativeUnifiedADData nativeUnifiedData, com.ads.admob.bean.NativeShowRequest bean) {
        String title = nativeUnifiedData.getTitle();
        String desc = nativeUnifiedData.getDesc();
        com.qq.e.ads.nativ.MediaView mediaView = null;
        int patternType = nativeUnifiedData.getAdPatternType();
        if (com.qq.e.comm.constants.AdPatternType.NATIVE_2IMAGE_2TEXT == patternType) {
            container_native_2IMAGE_2TEXT.setVisibility(View.VISIBLE);
            TextView container_NATIVE_2IMAGE_2TEXT_title = findViewById(R.id.container_NATIVE_2IMAGE_2TEXT_title);
            TextView container_NATIVE_2IMAGE_2TEXT_desc = findViewById(R.id.container_NATIVE_2IMAGE_2TEXT_desc);
            container_NATIVE_2IMAGE_2TEXT_title.setText(title);
            container_NATIVE_2IMAGE_2TEXT_desc.setText(desc);
            ImageView container_NATIVE_2IMAGE_2TEXT_iconUrl = findViewById(R.id.container_NATIVE_2IMAGE_2TEXT_iconUrl);
            ImageView container_NATIVE_2IMAGE_2TEXT_imgUrl = findViewById(R.id.container_NATIVE_2IMAGE_2TEXT_imgUrl);
            String iconUrl = nativeUnifiedData.getIconUrl();
            String imgUrl = nativeUnifiedData.getImgUrl();
            Glide.with(NativeActivity.this)
                    .load(iconUrl)
                    .into(container_NATIVE_2IMAGE_2TEXT_iconUrl);
            Glide.with(NativeActivity.this)
                    .load(imgUrl)
                    .into(container_NATIVE_2IMAGE_2TEXT_imgUrl);
        } else if (com.qq.e.comm.constants.AdPatternType.NATIVE_3IMAGE == patternType) {
            container_native_3IMAGE.setVisibility(View.VISIBLE);
            TextView container_NATIVE_3IMAGE_title = findViewById(R.id.container_NATIVE_3IMAGE_title);
            TextView container_NATIVE_3IMAGE_desc = findViewById(R.id.container_NATIVE_3IMAGE_desc);
            container_NATIVE_3IMAGE_title.setText(title);
            container_NATIVE_3IMAGE_desc.setText(desc);
            ImageView container_NATIVE_3IMAGE_imgList1 = findViewById(R.id.container_NATIVE_3IMAGE_imgList1);
            ImageView container_NATIVE_3IMAGE_imgList2 = findViewById(R.id.container_NATIVE_3IMAGE_imgList2);
            ImageView container_NATIVE_3IMAGE_imgList3 = findViewById(R.id.container_NATIVE_3IMAGE_imgList3);
            List<String> imgList = nativeUnifiedData.getImgList();
            for (int i = 0; i < imgList.size(); i++) {
                if (i == 0) {
                    Glide.with(NativeActivity.this)
                            .load(imgList.get(i))
                            .into(container_NATIVE_3IMAGE_imgList1);
                }
                if (i == 1) {
                    Glide.with(NativeActivity.this)
                            .load(imgList.get(i))
                            .into(container_NATIVE_3IMAGE_imgList2);
                }
                if (i == 2) {
                    Glide.with(NativeActivity.this)
                            .load(imgList.get(i))
                            .into(container_NATIVE_3IMAGE_imgList3);
                }
            }
        } else if (com.qq.e.comm.constants.AdPatternType.NATIVE_1IMAGE_2TEXT == patternType) {
            container_native_1IMAGE_2TEXT.setVisibility(View.VISIBLE);
            TextView container_NATIVE_1IMAGE_2TEXT_title = findViewById(R.id.container_NATIVE_1IMAGE_2TEXT_title);
            TextView container_NATIVE_1IMAGE_2TEXT_desc = findViewById(R.id.container_NATIVE_1IMAGE_2TEXT_desc);
            container_NATIVE_1IMAGE_2TEXT_title.setText(title);
            container_NATIVE_1IMAGE_2TEXT_desc.setText(desc);
            ImageView container_NATIVE_1IMAGE_2TEXT_imgUrl = findViewById(R.id.container_NATIVE_1IMAGE_2TEXT_imgUrl);
            String imgUrl = nativeUnifiedData.getImgUrl();
            Glide.with(NativeActivity.this)
                    .load(imgUrl)
                    .into(container_NATIVE_1IMAGE_2TEXT_imgUrl);
        } else if (com.qq.e.comm.constants.AdPatternType.NATIVE_VIDEO == patternType) {
            container_native_VIDEO.setVisibility(View.VISIBLE);
            TextView container_NATIVE_VIDEO_title = findViewById(R.id.container_NATIVE_VIDEO_title);
            TextView container_NATIVE_VIDEO_desc = findViewById(R.id.container_NATIVE_VIDEO_desc);
            container_NATIVE_VIDEO_title.setText(title);
            container_NATIVE_VIDEO_desc.setText(desc);
            mediaView = findViewById(R.id.container_NATIVE_VIDEO_MediaView);
        }
        showNative(position, nativeUnifiedData, bean, mediaView);
    }
}
