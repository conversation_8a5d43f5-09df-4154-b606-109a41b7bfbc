<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_memory"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:layout_margin="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:foreground="?android:attr/selectableItemBackground">

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:text="?"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/md_theme_light_onSurface"
        android:gravity="center" />

</androidx.cardview.widget.CardView>
