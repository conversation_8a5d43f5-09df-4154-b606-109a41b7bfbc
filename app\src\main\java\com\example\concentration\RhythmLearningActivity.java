package com.example.concentration;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.material.button.MaterialButton;

public class RhythmLearningActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private TextView tvTitle, tvCurrentEquation, tvProgress;
    private MaterialButton btnStart, btnNext;
    
    private int currentNumber;
    private SharedPreferences prefs;
    private VoiceManager voiceManager;
    private int currentIndex = 0;
    private Handler rhythmHandler = new Handler();
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_rhythm_learning);
        
        currentNumber = getIntent().getIntExtra("number", 1);
        prefs = getSharedPreferences("multiplication_prefs", MODE_PRIVATE);
        voiceManager = VoiceManager.getInstance(this);

        initViews();
        setupClickListeners();
        loadProgress();
        updateUI();
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        tvTitle = findViewById(R.id.tv_title);
        tvCurrentEquation = findViewById(R.id.tv_current_equation);
        tvProgress = findViewById(R.id.tv_progress);
        btnStart = findViewById(R.id.btn_start);
        btnNext = findViewById(R.id.btn_next);
        
        tvTitle.setText(currentNumber + " 的节拍学习");
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        btnStart.setOnClickListener(v -> startRhythmLearning());
        btnNext.setOnClickListener(v -> nextEquation());
    }
    
    private void updateUI() {
        tvProgress.setText("进度: " + (currentIndex + 1) + "/9");
        tvCurrentEquation.setText(currentNumber + " × " + (currentIndex + 1) + " = " + (currentNumber * (currentIndex + 1)));

        // 保存当前进度
        saveCurrentProgress();
    }

    private void saveCurrentProgress() {
        // 保存当前学习进度
        String progressKey = "rhythm_learning_" + currentNumber + "_progress";
        prefs.edit().putInt(progressKey, currentIndex).apply();
    }

    private void loadProgress() {
        // 加载上次学习进度
        String progressKey = "rhythm_learning_" + currentNumber + "_progress";
        currentIndex = prefs.getInt(progressKey, 0);

        // 确保不超出范围
        if (currentIndex >= 9) {
            currentIndex = 0;
        }
    }
    
    private void startRhythmLearning() {
        btnStart.setEnabled(false);
        btnNext.setEnabled(false);

        // 开始节拍演示
        startRhythmDemo();
    }

    private void startRhythmDemo() {
        int multiplier = currentIndex + 1;
        int result = currentNumber * multiplier;

        // 节拍演示：先显示算式，然后闪烁显示结果
        tvCurrentEquation.setText(currentNumber + " × " + multiplier + " = ?");

        // 语音读出问题
        voiceManager.speakMultiplicationQuestion(currentNumber, multiplier);

        rhythmHandler.postDelayed(() -> {
            tvCurrentEquation.setText(currentNumber + " × " + multiplier + " = " + result);
            tvCurrentEquation.setTextColor(0xFFFFD700); // 金色

            // 语音读出答案
            voiceManager.speakMultiplication(currentNumber, multiplier, result);

            rhythmHandler.postDelayed(() -> {
                tvCurrentEquation.setTextColor(0xFFFFFFFF); // 白色
                // 显示跟读提示
                btnNext.setText("点击跟读");
                btnNext.setEnabled(true);
                setupFollowReadButton();
            }, 1000);
        }, 2000);
    }
    
    private void nextEquation() {
        currentIndex++;
        if (currentIndex >= 9) {
            // 学习完成
            gameCompleted();
        } else {
            updateUI();
            btnStart.setEnabled(true);
            btnNext.setEnabled(false);
        }
    }
    
    private void setupFollowReadButton() {
        // 改为点击跟读，不再使用语音识别
        btnNext.setOnClickListener(v -> {
            // 用户点击跟读
            int multiplier = currentIndex + 1;
            int result = currentNumber * multiplier;

            // 再次播放语音让用户跟读
            voiceManager.speakMultiplication(currentNumber, multiplier, result);

            // 延迟后自动进入下一个
            rhythmHandler.postDelayed(() -> {
                btnNext.setText("下一个");
                btnNext.setOnClickListener(v2 -> nextEquation());
            }, 3000); // 给用户3秒时间跟读
        });
    }

    private void gameCompleted() {
        // 奖励水晶碎片
        CrystalManager crystalManager = CrystalManager.getInstance(this);
        crystalManager.addCrystalFragments(CrystalManager.CrystalType.MULTIPLICATION, 10);

        // 返回结果
        Intent result = new Intent();
        result.putExtra("progress_gain", 20); // 节拍学习获得20%进度
        result.putExtra("game_time", 0L); // 节拍学习不计时
        setResult(RESULT_OK, result);
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (rhythmHandler != null) {
            rhythmHandler.removeCallbacksAndMessages(null);
        }
        if (voiceManager != null) {
            voiceManager.stop();
        }
    }
}
