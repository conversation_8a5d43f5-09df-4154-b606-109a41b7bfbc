package com.example.concentration;

import android.content.Context;
import android.content.SharedPreferences;
import java.util.HashMap;
import java.util.Map;

/**
 * 教程管理器
 * 管理各个游戏的首次教程显示和教程内容
 */
public class TutorialManager {
    
    private static TutorialManager instance;
    private SharedPreferences prefs;
    private Context context;
    
    // 游戏类型枚举
    public enum GameType {
        SCHULTE_GRID("schulte_grid", "舒尔特方格"),
        SEQUENCE_MEMORY("sequence_memory", "序列记忆"),
        COLOR_TRAINING("color_training", "颜色训练"),
        MULTIPLICATION_TABLE("multiplication_table", "乘法表学习");
        
        private final String key;
        private final String name;
        
        GameType(String key, String name) {
            this.key = key;
            this.name = name;
        }
        
        public String getKey() { return key; }
        public String getName() { return name; }
    }
    
    // 教程内容数据类
    public static class TutorialContent {
        public String title;
        public String description;
        public String voiceText;
        public String[] steps;
        public String[] stepVoices;
        
        public TutorialContent(String title, String description, String voiceText, String[] steps, String[] stepVoices) {
            this.title = title;
            this.description = description;
            this.voiceText = voiceText;
            this.steps = steps;
            this.stepVoices = stepVoices;
        }
    }
    
    private Map<GameType, TutorialContent> tutorialContents;
    
    private TutorialManager(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences("tutorial_prefs", Context.MODE_PRIVATE);
        initializeTutorialContents();
    }
    
    public static TutorialManager getInstance(Context context) {
        if (instance == null) {
            instance = new TutorialManager(context);
        }
        return instance;
    }
    
    /**
     * 初始化所有游戏的教程内容
     */
    private void initializeTutorialContents() {
        tutorialContents = new HashMap<>();
        
        // 舒尔特方格教程
        tutorialContents.put(GameType.SCHULTE_GRID, new TutorialContent(
            "舒尔特方格训练",
            "提升注意力和视觉搜索能力的经典训练方法",
            "欢迎来到舒尔特方格训练！这是一个提升注意力和视觉搜索能力的经典训练方法。",
            new String[]{
                "1. 选择网格大小（3×3到6×6）",
                "2. 按数字顺序依次点击（1、2、3...）",
                "3. 尽可能快速准确地完成",
                "4. 挑战更大的网格提升难度"
            },
            new String[]{
                "首先选择适合的网格大小，从3乘3开始比较容易",
                "游戏开始后，按照数字顺序依次点击，从1开始",
                "要尽可能快速准确地完成，这样能更好地训练注意力",
                "完成后可以挑战更大的网格，提升训练难度"
            }
        ));
        
        // 序列记忆教程
        tutorialContents.put(GameType.SEQUENCE_MEMORY, new TutorialContent(
            "序列记忆训练",
            "训练工作记忆和序列记忆能力",
            "欢迎来到序列记忆训练！这个游戏能有效训练你的工作记忆和序列记忆能力。",
            new String[]{
                "1. 选择训练模式（数字内容或位置记忆）",
                "2. 仔细观察闪烁的数字或位置",
                "3. 按照顺序准确回忆并输入",
                "4. 逐步提升序列长度和难度"
            },
            new String[]{
                "首先选择训练模式，数字内容模式记忆数字序列，位置模式记忆位置序列",
                "游戏开始后仔细观察闪烁的数字或位置，集中注意力",
                "观察结束后，按照刚才看到的顺序准确回忆并输入",
                "随着练习可以逐步提升序列长度，挑战更高难度"
            }
        ));
        
        // 颜色训练教程
        tutorialContents.put(GameType.COLOR_TRAINING, new TutorialContent(
            "颜色识别训练",
            "提升视觉注意力和色彩敏感度",
            "欢迎来到颜色识别训练！这个游戏能提升你的视觉注意力和色彩敏感度。",
            new String[]{
                "1. 选择游戏模式和难度",
                "2. 观察目标颜色或颜色名称",
                "3. 快速点击匹配的颜色方块",
                "4. 达到12个正确答案即可过关"
            },
            new String[]{
                "首先选择适合的游戏模式和难度等级",
                "游戏中要仔细观察目标颜色或颜色名称",
                "然后快速点击与目标匹配的颜色方块",
                "需要达到12个正确答案才能过关，加油！"
            }
        ));
        
        // 乘法表学习教程
        tutorialContents.put(GameType.MULTIPLICATION_TABLE, new TutorialContent(
            "乘法表学习",
            "系统化学习九九乘法表",
            "欢迎来到乘法表学习！通过三个阶段系统化学习九九乘法表。",
            new String[]{
                "1. 阶段一：基础概念学习",
                "2. 阶段二：分数字逐个掌握",
                "3. 阶段三：综合应用挑战",
                "4. 完成前一阶段才能解锁下一阶段"
            },
            new String[]{
                "第一阶段学习乘法的基础概念，理解乘法的含义",
                "第二阶段分数字逐个学习，从2的乘法表到9的乘法表",
                "第三阶段进行综合应用挑战，测试掌握程度",
                "需要完成前一阶段达到80%进度才能解锁下一阶段"
            }
        ));
    }
    
    /**
     * 检查是否是首次进入游戏
     */
    public boolean isFirstTime(GameType gameType) {
        return !prefs.getBoolean("tutorial_shown_" + gameType.getKey(), false);
    }
    
    /**
     * 标记教程已显示
     */
    public void markTutorialShown(GameType gameType) {
        prefs.edit().putBoolean("tutorial_shown_" + gameType.getKey(), true).apply();
    }
    
    /**
     * 重置教程状态（用于测试）
     */
    public void resetTutorialStatus(GameType gameType) {
        prefs.edit().putBoolean("tutorial_shown_" + gameType.getKey(), false).apply();
    }
    
    /**
     * 获取教程内容
     */
    public TutorialContent getTutorialContent(GameType gameType) {
        return tutorialContents.get(gameType);
    }
    
    /**
     * 重置所有教程状态（用于测试模式）
     */
    public void resetAllTutorials() {
        SharedPreferences.Editor editor = prefs.edit();
        for (GameType gameType : GameType.values()) {
            editor.putBoolean("tutorial_shown_" + gameType.getKey(), false);
        }
        editor.apply();
    }
}
