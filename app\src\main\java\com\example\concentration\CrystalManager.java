package com.example.concentration;

import android.content.Context;
import android.content.SharedPreferences;

/**
 * 水晶积分管理器
 * 管理整个APP的水晶收集和兑换系统
 */
public class CrystalManager {
    
    private static CrystalManager instance;
    private SharedPreferences prefs;
    private Context context;
    
    // 水晶类型枚举
    public enum CrystalType {
        SCHULTE_GRID("schulte_crystal", "舒尔特水晶", "🔷"),
        SEQUENCE_MEMORY("memory_crystal", "记忆水晶", "🔮"), 
        COLOR_TRAINING("color_crystal", "色彩水晶", "💎"),
        MULTIPLICATION("math_crystal", "数学水晶", "💠");
        
        private final String key;
        private final String name;
        private final String emoji;
        
        CrystalType(String key, String name, String emoji) {
            this.key = key;
            this.name = name;
            this.emoji = emoji;
        }
        
        public String getKey() { return key; }
        public String getName() { return name; }
        public String getEmoji() { return emoji; }
    }
    
    // 水晶奖励配置
    public static class CrystalReward {
        public final CrystalType type;
        public final int fragments;
        public final String description;
        
        public CrystalReward(CrystalType type, int fragments, String description) {
            this.type = type;
            this.fragments = fragments;
            this.description = description;
        }
    }
    
    private CrystalManager(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences("crystal_data", Context.MODE_PRIVATE);
    }
    
    public static synchronized CrystalManager getInstance(Context context) {
        if (instance == null) {
            instance = new CrystalManager(context);
        }
        return instance;
    }
    
    /**
     * 获取水晶碎片数量
     */
    public int getCrystalFragments(CrystalType type) {
        return prefs.getInt(type.getKey() + "_fragments", 0);
    }
    
    /**
     * 获取完整水晶数量
     */
    public int getCompleteCrystals(CrystalType type) {
        return prefs.getInt(type.getKey() + "_complete", 0);
    }
    
    /**
     * 获取总水晶数量（所有类型的完整水晶）
     */
    public int getTotalCrystals() {
        int total = 0;
        for (CrystalType type : CrystalType.values()) {
            total += getCompleteCrystals(type);
        }
        return total;
    }
    
    /**
     * 添加水晶碎片
     */
    public boolean addCrystalFragments(CrystalType type, int amount) {
        int currentFragments = getCrystalFragments(type);
        int newFragments = currentFragments + amount;
        
        // 检查是否可以合成完整水晶（30个碎片 = 1个完整水晶）
        int newCompleteCrystals = newFragments / 30;
        int remainingFragments = newFragments % 30;
        
        if (newCompleteCrystals > 0) {
            // 有新的完整水晶生成
            int currentComplete = getCompleteCrystals(type);
            prefs.edit()
                .putInt(type.getKey() + "_fragments", remainingFragments)
                .putInt(type.getKey() + "_complete", currentComplete + newCompleteCrystals)
                .apply();
            return true; // 返回true表示有新水晶生成
        } else {
            // 只是增加碎片
            prefs.edit()
                .putInt(type.getKey() + "_fragments", newFragments)
                .apply();
            return false;
        }
    }
    
    /**
     * 消耗完整水晶
     */
    public boolean consumeCrystals(CrystalType type, int amount) {
        int currentComplete = getCompleteCrystals(type);
        if (currentComplete >= amount) {
            prefs.edit()
                .putInt(type.getKey() + "_complete", currentComplete - amount)
                .apply();
            return true;
        }
        return false;
    }
    
    /**
     * 获取舒尔特方格的水晶奖励配置
     */
    public static CrystalReward getSchulteGridReward(int gridSize) {
        switch (gridSize) {
            case 3: return new CrystalReward(CrystalType.SCHULTE_GRID, 1, "3×3完成");
            case 4: return new CrystalReward(CrystalType.SCHULTE_GRID, 2, "4×4完成");
            case 5: return new CrystalReward(CrystalType.SCHULTE_GRID, 3, "5×5完成");
            case 6: return new CrystalReward(CrystalType.SCHULTE_GRID, 4, "6×6完成");
            default: return new CrystalReward(CrystalType.SCHULTE_GRID, 1, "完成");
        }
    }
    
    /**
     * 获取序列记忆游戏的水晶奖励配置
     */
    public static CrystalReward getMemoryGameReward(String mode, String difficulty) {
        int baseReward = 2;
        
        // 根据难度调整奖励
        switch (difficulty) {
            case "easy": baseReward = 1; break;
            case "medium": baseReward = 2; break;
            case "hard": baseReward = 3; break;
        }
        
        // 根据模式调整奖励
        if ("position".equals(mode)) {
            baseReward += 1; // 位置记忆更难，额外奖励
        }
        
        return new CrystalReward(CrystalType.SEQUENCE_MEMORY, baseReward, 
            mode + "模式" + difficulty + "难度完成");
    }
    
    /**
     * 获取颜色训练游戏的水晶奖励配置
     */
    public static CrystalReward getColorTrainingReward(String difficulty) {
        switch (difficulty) {
            case "easy": return new CrystalReward(CrystalType.COLOR_TRAINING, 1, "简单模式完成");
            case "medium": return new CrystalReward(CrystalType.COLOR_TRAINING, 2, "中等模式完成");
            case "hard": return new CrystalReward(CrystalType.COLOR_TRAINING, 3, "困难模式完成");
            default: return new CrystalReward(CrystalType.COLOR_TRAINING, 2, "完成");
        }
    }
    
    /**
     * 获取乘法表学习的水晶奖励配置
     */
    public static CrystalReward getMultiplicationReward(int stage, int progress) {
        switch (stage) {
            case 1: // 基础概念学习
                if (progress >= 100) {
                    return new CrystalReward(CrystalType.MULTIPLICATION, 2, "基础概念完成");
                }
                break;
            case 2: // 数字学习
                if (progress >= 100) {
                    return new CrystalReward(CrystalType.MULTIPLICATION, 3, "数字学习完成");
                }
                break;
            case 3: // 挑战模式
                // 每通过一关给1个碎片
                return new CrystalReward(CrystalType.MULTIPLICATION, 1, "挑战关卡完成");
        }
        return null;
    }
    
    /**
     * 获取水晶进度文本
     */
    public String getCrystalProgressText(CrystalType type) {
        int fragments = getCrystalFragments(type);
        int complete = getCompleteCrystals(type);
        return String.format("%s %d个 + %d/30碎片", type.getEmoji(), complete, fragments);
    }
    
    /**
     * 获取总水晶概览文本
     */
    public String getTotalCrystalText() {
        int total = getTotalCrystals();
        return String.format("💎 总水晶: %d个", total);
    }

    /**
     * 获取所有类型的水晶碎片总数
     */
    public int getTotalFragments() {
        int total = 0;
        for (CrystalType type : CrystalType.values()) {
            total += getCrystalFragments(type);
        }
        return total;
    }
}
