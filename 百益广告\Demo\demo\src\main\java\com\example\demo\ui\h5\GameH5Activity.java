package com.example.demo.ui.h5;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v7.app.AppCompatActivity;
import android.view.Window;

import com.example.demo.R;
import com.tencent.smtt.sdk.WebView;

public class GameH5Activity extends AppCompatActivity {

    private WebView webView;

    @Override
    protected void onCreate(@Nullable Bundle bundle) {

        requestWindowFeature(Window.FEATURE_NO_TITLE);// 隐藏标题栏
        getSupportActionBar().hide();// 隐藏ActionBar
        //getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);// 隐藏状态栏

        super.onCreate(bundle);

        String userId = getIntent().getStringExtra("userId");

        setContentView(R.layout.activity_game_h5);

        webView = findViewById(R.id.a_webView);

        com.by.mob.config.ByH5GameConfig config = new com.by.mob.config.ByH5GameConfig.Builder()
                .codeId("1509369198176911367")//【必填】
                .uid(userId)//【必填】用户的ID，请确保渠道用户id的唯一性（跟游戏进度相关）
                .nick(userId)//【必填】用户昵称，如果没有昵称，可以用uid代替
                .sex(0)//用户在第三方渠道的性别，1：男；2：女；0：其他未知
                .avatarUrl("")//游戏头像,可以为空使用默认头像
                .build();
        com.by.mob.ByH5GameManager.load(this, webView, config, new com.by.mob.ByH5GameManager.h5GameListener() {
            @Override
            public void onError(int code, String msg) {

            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        webView.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        webView.onPause();
    }
}
