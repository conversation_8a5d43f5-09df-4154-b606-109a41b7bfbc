package com.example.concentration;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class RecentRecordsAdapter extends RecyclerView.Adapter<RecentRecordsAdapter.ViewHolder> {
    
    private List<RewardsActivity.GameRecord> records;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd HH:mm", Locale.getDefault());
    
    public RecentRecordsAdapter(List<RewardsActivity.GameRecord> records) {
        this.records = records;
    }
    
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_game_record, parent, false);
        return new ViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        RewardsActivity.GameRecord record = records.get(position);
        
        holder.tvGameType.setText(record.gameType);
        holder.tvResult.setText(record.result);
        holder.tvTime.setText(record.time);
        holder.tvTimestamp.setText(dateFormat.format(new Date(record.timestamp)));
    }
    
    @Override
    public int getItemCount() {
        return records.size();
    }
    
    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvGameType, tvResult, tvTime, tvTimestamp;
        
        ViewHolder(View itemView) {
            super(itemView);
            tvGameType = itemView.findViewById(R.id.tv_game_type);
            tvResult = itemView.findViewById(R.id.tv_result);
            tvTime = itemView.findViewById(R.id.tv_time);
            tvTimestamp = itemView.findViewById(R.id.tv_timestamp);
        }
    }
}
