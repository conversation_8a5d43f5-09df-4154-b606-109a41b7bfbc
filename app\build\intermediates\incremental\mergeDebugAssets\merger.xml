<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="wind-sdk-4.21.1.aar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\assets"><file name="sig_appelements.html" path="C:\Users\<USER>\.gradle\caches\transforms-3\02d81beee2b37ae2d502f14e157c45a2\transformed\jetified-wind-sdk-4.21.1\assets\sig_appelements.html"/></source></dataSet><dataSet config="open_ad_sdk_6.8.4.1.aar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\assets"><file name="1906642177" path="C:\Users\<USER>\.gradle\caches\transforms-3\3b4f66e0a4ce502db267fc5b4bad436e\transformed\jetified-open_ad_sdk_6.8.4.1\assets\1906642177"/></source></dataSet><dataSet config="kssdk-ct-3.3.76.1.aar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-3.3.76.1\assets"><file name="ksad_common_encrypt_image.png" path="C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-3.3.76.1\assets\ksad_common_encrypt_image.png"/><file name="ksad_idc.json" path="C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-3.3.76.1\assets\ksad_idc.json"/><file name="saio_res/zt_7e46b28a-8c93-4940-8238-4c60e64e3c81.png" path="C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-3.3.76.1\assets\saio_res\zt_7e46b28a-8c93-4940-8238-4c60e64e3c81.png"/><file name="saio_res/zt_92827f75-21cd-4faa-9aa5-74191b262edd.png" path="C:\Users\<USER>\.gradle\caches\transforms-3\eb7e0db8c5a1e03615b67a657c6a7904\transformed\jetified-kssdk-ct-3.3.76.1\assets\saio_res\zt_92827f75-21cd-4faa-9aa5-74191b262edd.png"/></source></dataSet><dataSet config="GDTSDK.unionNormal.4.640.1510.aar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\assets"><file name="gdt_plugin/gdtadv2.jar" path="C:\Users\<USER>\.gradle\caches\transforms-3\c3f3eaede59b9b2d48f859a253992eff\transformed\jetified-GDTSDK.unionNormal.4.640.1510\assets\gdt_plugin\gdtadv2.jar"/></source></dataSet><dataSet config="Baidu_MobAds_SDK-release_v9.3912.aar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\assets"><file name="bdxadsdk.jar" path="C:\Users\<USER>\.gradle\caches\transforms-3\c22a337e172cba8d7de762a847f89241\transformed\jetified-Baidu_MobAds_SDK-release_v9.3912\assets\bdxadsdk.jar"/></source></dataSet><dataSet config="anythink_network_mobrain_mix_plus.aar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\197b2095d494c640280cf459638165d1\transformed\jetified-anythink_network_mobrain_mix_plus\assets"><file name="anythink/pl/46/6.4.87-6.8.4.1-0.jar" path="C:\Users\<USER>\.gradle\caches\transforms-3\197b2095d494c640280cf459638165d1\transformed\jetified-anythink_network_mobrain_mix_plus\assets\anythink\pl\46\6.4.87-6.8.4.1-0.jar"/></source></dataSet><dataSet config="admob-20250529.aar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\assets"><file name="joke.txt" path="C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\assets\joke.txt"/><file name="tb_com.cer" path="C:\Users\<USER>\.gradle\caches\transforms-3\8587ff1e875ba758084d056fe5d02138\transformed\jetified-admob-20250529\assets\tb_com.cer"/></source></dataSet><dataSet config="adalliance_adn_sdk.3.11.3.aar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\assets"><file name="supplierconfig.json" path="C:\Users\<USER>\.gradle\caches\transforms-3\a82f280c26d47ddf20a253a4a0ee7a09\transformed\jetified-adalliance_adn_sdk.3.11.3\assets\supplierconfig.json"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\专注力训练\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\专注力训练\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\专注力训练\app\build\intermediates\shader_assets\debug\out"/></dataSet></merger>