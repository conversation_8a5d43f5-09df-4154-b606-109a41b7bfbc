<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/grid_cell_pressed" />
            <stroke android:width="2dp" android:color="@color/primary_color" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/grid_cell_normal" />
            <stroke android:width="1dp" android:color="@color/grid_border" />
            <corners android:radius="8dp" />
        </shape>
    </item>
</selector>
