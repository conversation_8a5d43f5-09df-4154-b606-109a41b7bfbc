<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:fillViewport="true">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- 顶部导航栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- 返回按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            app:icon="@android:drawable/ic_menu_revert"
            app:iconSize="24dp"
            style="@style/Widget.Material3.Button.IconButton.Filled.Tonal"
            app:backgroundTint="@color/md_theme_light_secondaryContainer" />

        <!-- 标题 -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="🎯 舒尔特方格训练"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_onSurface"
            android:gravity="center"
            android:layout_marginHorizontal="16dp" />

        <!-- 主题选择按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_theme_selection"
            android:layout_width="48dp"
            android:layout_height="48dp"
            app:icon="@android:drawable/ic_menu_preferences"
            app:iconSize="24dp"
            style="@style/Widget.Material3.Button.IconButton.Filled.Tonal"
            app:backgroundTint="@color/md_theme_light_secondaryContainer" />

        <!-- 教程按钮 - 灯泡图标 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_tutorial"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="💡"
            android:textSize="20sp"
            style="@style/Widget.Material3.Button.IconButton"
            android:background="@android:color/transparent" />

    </LinearLayout>

    <!-- 主要内容区域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 今日最佳成绩卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:strokeWidth="0dp"
                app:cardBackgroundColor="@color/md_theme_light_primary">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp"
                    android:gravity="center">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="今日最佳"
                        android:textSize="16sp"
                        android:textColor="@color/md_theme_light_onPrimary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_best_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="00:00.00"
                        android:textSize="32sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_onPrimary"
                        android:fontFamily="monospace"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="用时最短记录"
                        android:textSize="12sp"
                        android:textColor="@color/md_theme_light_onPrimary"
                        android:alpha="0.8" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 统计数据行 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="20dp">

                <!-- 平均用时卡片 -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/md_theme_light_surface">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="平均用时"
                            android:textSize="12sp"
                            android:textColor="@color/md_theme_light_onSurfaceVariant"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/tv_average_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="00:00.00"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onSurface"
                            android:fontFamily="monospace"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/tv_difficulty_label"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="5x5 方格平均时间"
                            android:textSize="10sp"
                            android:textColor="@color/md_theme_light_onSurfaceVariant"
                            android:gravity="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- 训练次数卡片 -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/md_theme_light_surface">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="训练次数"
                            android:textSize="12sp"
                            android:textColor="@color/md_theme_light_onSurfaceVariant"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/tv_play_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onSurface"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/tv_today_games"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="本周训练次数"
                            android:textSize="10sp"
                            android:textColor="@color/md_theme_light_onSurfaceVariant" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- 水晶积分显示 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/md_theme_light_surface">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🔷"
                        android:textSize="24sp"
                        android:layout_marginEnd="12dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="舒尔特水晶"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onSurface"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/tv_crystal_progress"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0个 + 0/30碎片"
                            android:textSize="12sp"
                            android:textColor="@color/md_theme_light_onSurfaceVariant" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="每次完成获得1-4个碎片"
                        android:textSize="10sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant"
                        android:gravity="end" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 开始新训练卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="6dp"
                app:strokeWidth="0dp"
                app:cardBackgroundColor="@color/md_theme_light_surface">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="开始新的训练"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_onSurface"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="选择方格大小开始训练，挑战自己的专注力极限"
                        android:textSize="14sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant"
                        android:layout_marginBottom="20dp" />

                    <!-- 难度选择和开始按钮 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="难度："
                            android:textSize="16sp"
                            android:textColor="@color/md_theme_light_onSurface"
                            android:layout_marginEnd="12dp" />

                        <TextView
                            android:id="@+id/tv_difficulty"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="3×3"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_primary"
                            android:background="@drawable/difficulty_background"
                            android:padding="8dp"
                            android:minWidth="60dp"
                            android:gravity="center"
                            android:clickable="true"
                            android:focusable="true" />

                        <TextView
                            android:id="@+id/tv_number_mode"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="简单"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_primary"
                            android:background="@drawable/difficulty_background"
                            android:padding="8dp"
                            android:minWidth="60dp"
                            android:gravity="center"
                            android:clickable="true"
                            android:focusable="true"
                            android:layout_marginStart="8dp" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="1dp"
                            android:layout_weight="1" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_start_game"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="开始"
                            android:textSize="16sp"
                            app:cornerRadius="24dp"
                            app:backgroundTint="@color/md_theme_light_primary"
                            app:icon="@android:drawable/ic_media_play"
                            app:iconSize="20dp" />

                    </LinearLayout>



                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 最近训练记录卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:strokeWidth="0dp"
                app:cardBackgroundColor="@color/md_theme_light_surface">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- 标题行 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="最近训练记录"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onSurface" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="查看全部"
                            android:textSize="14sp"
                            android:textColor="@color/md_theme_light_primary"
                            android:clickable="true"
                            android:focusable="true" />

                    </LinearLayout>

                    <!-- 表头 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingVertical="8dp"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"
                            android:text="日期"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onSurfaceVariant" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="方格大小"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onSurfaceVariant"
                            android:gravity="center" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.5"
                            android:text="用时"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onSurfaceVariant"
                            android:gravity="center" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="状态"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:textColor="@color/md_theme_light_onSurfaceVariant"
                            android:gravity="center" />

                    </LinearLayout>

                    <!-- 分隔线 -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/md_theme_light_outline"
                        android:layout_marginBottom="12dp" />

                    <!-- 记录列表容器 -->
                    <LinearLayout
                        android:id="@+id/records_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- 示例记录 - 将由代码动态生成 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingVertical="8dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:text="暂无记录"
                                android:textSize="14sp"
                                android:textColor="@color/md_theme_light_onSurfaceVariant" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>

</ScrollView>
