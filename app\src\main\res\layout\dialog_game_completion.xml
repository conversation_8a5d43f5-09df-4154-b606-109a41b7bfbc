<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/dialog_background"
    android:gravity="center">

    <!-- 成功图标 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="🏆"
        android:textSize="64sp"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- 标题 -->
    <TextView
        android:id="@+id/tv_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="挑战成功！"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="@color/md_theme_light_primary"
        android:layout_marginBottom="8dp"
        android:gravity="center" />

    <!-- 消息 -->
    <TextView
        android:id="@+id/tv_dialog_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="恭喜完成挑战！"
        android:textSize="16sp"
        android:textColor="@color/md_theme_light_onSurfaceVariant"
        android:layout_marginBottom="20dp"
        android:gravity="center" />

    <!-- 成绩展示区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp"
        android:gravity="center">

        <!-- 完成时间卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@color/md_theme_light_primaryContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="完成时间"
                    android:textSize="12sp"
                    android:textColor="@color/md_theme_light_onPrimaryContainer"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_game_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="30.123"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onPrimaryContainer"
                    android:fontFamily="monospace" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 最佳纪录卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@color/md_theme_light_secondaryContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="最佳纪录"
                    android:textSize="12sp"
                    android:textColor="@color/md_theme_light_onSecondaryContainer"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_record_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="25.456"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSecondaryContainer"
                    android:fontFamily="monospace" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_back_to_main"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="返回主页"
            android:textSize="14sp"
            android:textStyle="bold"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            app:strokeColor="@color/md_theme_light_primary"
            app:strokeWidth="2dp"
            android:textColor="@color/md_theme_light_primary"
            app:cornerRadius="28dp"
            android:backgroundTint="@android:color/white" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_play_again"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="再来一次"
            android:textSize="14sp"
            android:textStyle="bold"
            style="@style/Widget.MaterialComponents.Button"
            app:backgroundTint="@color/md_theme_light_primary"
            app:cornerRadius="28dp"
            android:textColor="@android:color/white" />

    </LinearLayout>

</LinearLayout>
