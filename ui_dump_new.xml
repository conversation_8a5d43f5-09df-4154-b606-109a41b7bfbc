<?xml version='1.0' encoding='UTF-8' standalone='yes' ?><hierarchy rotation="0"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1344,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1344,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,140][1344,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="com.example.concentration:id/action_bar_root" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,140][1344,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,140][1344,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.ScrollView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" bounds="[0,140][1344,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,140][1344,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,280][1260,449]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.view.View" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,280][252,448]" xpos="[0,0][0,0]" /><node index="1" text="&#129504; 疯狂大脑" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[252,280][1092,449]" xpos="[0,0][0,0]" /><node index="2" text="" resource-id="com.example.concentration:id/btn_settings" class="android.widget.ImageButton" package="com.example.concentration" content-desc="设置" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[1092,280][1260,448]" xpos="[0,0][0,0]" /></node><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,533][1260,618]" xpos="[0,0][0,0]"><node index="0" text="✨ 神经元拉拉小手，冲啊！ ✨" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[230,533][1114,618]" xpos="[0,0][0,0]" /></node><node index="2" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,758][1260,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="com.example.concentration:id/card_schulte" class="android.widget.FrameLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,758][1260,1250]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,758][1260,1250]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[168,842][1176,1110]" xpos="[0,0][0,0]"><node index="0" text="&#127919;" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[168,901][308,1051]" xpos="[0,0][0,0]" /><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[350,842][1176,1110]" xpos="[0,0][0,0]"><node index="0" text="舒尔特方格" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[350,842][1176,954]" xpos="[0,0][0,0]" /><node index="1" text="提升注意力集中度和视觉搜索能力" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[350,968][1176,1110]" xpos="[0,0][0,0]" /></node></node></node></node><node index="1" text="" resource-id="com.example.concentration:id/card_sequence_memory" class="android.widget.FrameLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,1334][1260,1836]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,1334][1260,1836]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[168,1418][1176,1620]" xpos="[0,0][0,0]"><node index="0" text="&#129518;" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[168,1444][308,1594]" xpos="[0,0][0,0]" /><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[350,1418][1176,1620]" xpos="[0,0][0,0]"><node index="0" text="数字序列记忆" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[350,1418][1176,1530]" xpos="[0,0][0,0]" /><node index="1" text="训练短期记忆和数字处理能力" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[350,1544][1176,1620]" xpos="[0,0][0,0]" /></node></node><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[168,1676][1176,1752]" xpos="[0,0][0,0]"><node index="0" text="开始训练" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[168,1676][392,1752]" xpos="[0,0][0,0]" /></node></node></node><node index="2" text="" resource-id="com.example.concentration:id/card_color_training" class="android.widget.FrameLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,1920][1260,2422]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,1920][1260,2422]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[168,2004][1176,2206]" xpos="[0,0][0,0]"><node index="0" text="&#127752;" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[168,2030][308,2180]" xpos="[0,0][0,0]" /><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[350,2004][1176,2206]" xpos="[0,0][0,0]"><node index="0" text="颜色识别训练" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[350,2004][1176,2116]" xpos="[0,0][0,0]" /><node index="1" text="提升视觉处理和反应速度" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[350,2130][1176,2206]" xpos="[0,0][0,0]" /></node></node><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[168,2262][1176,2338]" xpos="[0,0][0,0]"><node index="0" text="开始训练" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[168,2262][392,2338]" xpos="[0,0][0,0]" /></node></node></node><node index="3" text="" resource-id="com.example.concentration:id/card_multiplication_table" class="android.widget.FrameLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,2534][1260,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[84,2534][1260,2632]" xpos="[0,0][0,0]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[168,2618][1176,2632]" xpos="[0,0][0,0]"><node index="0" text="✖️" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][0,0]" xpos="[0,0][0,0]" /><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[350,2618][1176,2632]" xpos="[0,0][0,0]"><node index="0" text="九九乘法表" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[350,2618][1176,2632]" xpos="[0,0][0,0]" /><node index="1" text="从零开始学习乘法，寓教于乐" resource-id="" class="android.widget.TextView" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][0,0]" xpos="[0,0][0,0]" /></node></node></node></node></node></node></node></node></node></node></node><node index="1" text="" resource-id="android:id/statusBarBackground" class="android.view.View" package="com.example.concentration" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1344,140]" xpos="[0,0][0,0]" /></node></hierarchy>