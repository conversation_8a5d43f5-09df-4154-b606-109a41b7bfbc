package com.example.demo.ui;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v7.app.AppCompatActivity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.Toast;

import com.example.demo.R;

//Draw竖版视频信息流
public class DrawFeedActivity extends AppCompatActivity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_draw_feed);
        FrameLayout mContainer = findViewById(R.id.container);

        //加载Draw竖版视频信息流
        com.by.mob.config.ByDrawFeedConfig config = new com.by.mob.config.ByDrawFeedConfig.Builder()
                .codeId("1330515190466232331")//平台申请的代码位id
                .count(1)//广告请求个数（1~3）
                .viewHigh(0)//视图view的height（单位dp），传0默认占满屏幕
                .container(mContainer)//承载视图的容器
                .build();
        com.by.mob.ByManager.loadDrawFeed(config, this, new com.by.mob.ByManager.DrawFeedLoadListener() {
            @Override
            public void onFail(String s) {
                //加载失败
                Toast.makeText(DrawFeedActivity.this, s, Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onRenderFail() {
                //渲染失败
            }

            @Override
            public void onRenderSuccess() {

            }

            @Override
            public void onVideoCompleted() {

            }

            @Override
            public void onVideoPause() {

            }

            @Override
            public void onVideoResume() {

            }

            @Override
            public void onVideoStart() {

            }

            @Override
            public void getView(View view) {

            }

            @Override
            public void onClicked() {

            }
        });
    }
}
