<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/md_theme_light_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="center_horizontal">

        <!-- 标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="32dp">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

        </LinearLayout>

        <!-- 欢迎区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="48dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🧠"
                android:textSize="80sp"
                android:layout_marginBottom="16dp" />

            <TextView
                android:id="@+id/tv_welcome"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🎮 欢迎来到疯狂大脑！"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onBackground"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/tv_instruction"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="请输入您的名字，开始您的专注力训练之旅："
                android:textSize="16sp"
                android:textColor="@color/md_theme_light_onSurfaceVariant"
                android:gravity="center"
                android:lineSpacingExtra="4dp" />

        </LinearLayout>

        <!-- 输入区域 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="👤 您的名字"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSurface"
                    android:layout_marginBottom="12dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="请输入您的名字（最多5个字）"
                    app:boxBackgroundMode="outline"
                    app:boxCornerRadiusTopStart="12dp"
                    app:boxCornerRadiusTopEnd="12dp"
                    app:boxCornerRadiusBottomStart="12dp"
                    app:boxCornerRadiusBottomEnd="12dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_username"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:maxLines="1"
                        android:textSize="18sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="💡 提示：名字将显示在奖励中心，建议使用真实姓名或昵称"
                    android:textSize="12sp"
                    android:textColor="@color/md_theme_light_onSurfaceVariant"
                    android:layout_marginTop="8dp"
                    android:lineSpacingExtra="2dp" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 按钮区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <Button
                android:id="@+id/btn_start_training"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="🚀 开始训练"
                android:textSize="18sp"
                android:textStyle="bold"
                android:backgroundTint="@color/md_theme_light_primary"
                android:textColor="@color/md_theme_light_onPrimary"
                android:layout_marginBottom="16dp"
                app:cornerRadius="16dp" />

            <Button
                android:id="@+id/btn_skip"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="稍后再说"
                android:textSize="16sp"
                android:backgroundTint="@color/md_theme_light_surfaceVariant"
                android:textColor="@color/md_theme_light_onSurfaceVariant"
                app:cornerRadius="12dp"
                style="@style/Widget.Material3.Button.TextButton" />

        </LinearLayout>

    </LinearLayout>
</ScrollView>
