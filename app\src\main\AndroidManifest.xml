<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!-- 可选权限 动态权限-->
    <!--获取设备标识IMEI。用于标识用户-->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <!--读写存储权限-->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!--定位权限-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <application
        android:name=".AdApplication"
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:requestLegacyExternalStorage="false">

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <provider
            android:name="com.bytedance.sdk.openadsdk.multipro.TTMultiProvider"
            android:authorities="${applicationId}.TTMultiProvider"
            android:exported="false" />
        <provider
            android:name="com.bytedance.sdk.openadsdk.TTFileProvider"
            android:authorities="${applicationId}.TTFileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/anythink_bk_tt_file_path" />
        </provider>

        <provider
            android:name="com.qq.e.comm.GDTFileProvider"
            android:authorities="${applicationId}.gdt.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/anythink_bk_gdt_file_path" />
        </provider>

        <provider
            android:name="com.sigmob.sdk.SigmobFileV4Provider"
            android:authorities="${applicationId}.sigprovider"
            android:exported="false"
            android:initOrder="200"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/anythink_bk_sigmob_file_path"/>
        </provider>

        <!-- 开屏广告Activity -->
        <activity
            android:name=".SplashActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity android:name=".MainActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />

        <activity
            android:name=".SchulteGridActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".ThemeSelectionActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".CountdownActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".GameActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />

        <activity
            android:name=".ColorTrainingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".ColorGameActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".MemoryGameMainActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".MemoryGameActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".CelebrationActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".MultiplicationTableActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".MultiplicationConceptActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".ConceptVisualDemoActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".ConceptInteractiveCountActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".ConceptAnimationDemoActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".ConceptTestGameActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".MultiplicationNumberActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".NumberLearningActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".MemoryCardGameActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".RhythmLearningActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".PuzzleChallengeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".MultiplicationChallengeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".SettingsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".UsernameSetupActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
        <activity
            android:name=".TutorialActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="false" />
    </application>

</manifest>    