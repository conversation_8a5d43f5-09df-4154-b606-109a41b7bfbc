<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_number_bg">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="节拍学习"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:gravity="center"
            android:layout_marginEnd="48dp" />

    </LinearLayout>

    <!-- 主要内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp">

        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="进度: 1/9"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:layout_marginBottom="32dp" />

        <TextView
            android:id="@+id/tv_current_equation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1 × 1 = 1"
            android:textSize="32sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:layout_marginBottom="48dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_start"
            android:layout_width="200dp"
            android:layout_height="56dp"
            android:text="开始学习"
            android:textSize="16sp"
            android:textStyle="bold"
            app:cornerRadius="28dp"
            android:layout_marginBottom="16dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_next"
            android:layout_width="200dp"
            android:layout_height="56dp"
            android:text="下一个"
            android:textSize="16sp"
            android:textStyle="bold"
            app:cornerRadius="28dp"
            android:enabled="false" />

    </LinearLayout>

</LinearLayout>
