package com.example.concentration;

import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.ViewGroup;
import android.widget.GridLayout;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.material.button.MaterialButton;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class ColorGameActivity extends AppCompatActivity {
    
    private TextView tvTimer, tvScore, tvInstruction, tvTargetName, tvTargetTime;
    private View viewTargetColor;
    private TextView tvTargetShape;
    private GridLayout gridColors;
    
    private ColorManager colorManager;
    private SoundManager soundManager;
    private GameDataManager gameDataManager;
    private SharedPreferences prefs;
    
    private String difficulty;
    private String gameMode;
    private int gridSize;
    private int totalRounds;
    private int currentRound = 0;
    private int correctAnswers = 0;
    private int wrongAnswers = 0;
    private int requiredCorrectAnswers = 12; // 需要答对12个才能过关
    
    private ColorManager.ColorInfo targetColor;
    private List<ColorManager.ColorInfo> colorOptions;
    private long gameStartTime;
    private long roundStartTime;
    private List<Long> reactionTimes = new ArrayList<>();
    
    private boolean isGameRunning = false;
    private long targetTimeMillis = 30000; // 默认30秒目标时间
    
    private Handler timerHandler = new Handler();
    private Runnable timerRunnable;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_color_game);
        
        getGameParameters();
        initializeManagers();
        initViews();
        setupClickListeners();
        startGame();
    }
    
    private void getGameParameters() {
        Intent intent = getIntent();
        difficulty = intent.getStringExtra("difficulty");
        gameMode = intent.getStringExtra("game_mode");
        gridSize = intent.getIntExtra("grid_size", 3);
        totalRounds = intent.getIntExtra("total_rounds", 10);
    }
    
    private void initializeManagers() {
        colorManager = ColorManager.getInstance();
        soundManager = SoundManager.getInstance(this);
        gameDataManager = new GameDataManager(this);
        prefs = getSharedPreferences("color_training_prefs", MODE_PRIVATE);
    }
    
    private void initViews() {
        tvTimer = findViewById(R.id.tv_timer);
        tvScore = findViewById(R.id.tv_score);
        tvInstruction = findViewById(R.id.tv_instruction);
        tvTargetName = findViewById(R.id.tv_target_name);
        tvTargetTime = findViewById(R.id.tv_target_time);
        viewTargetColor = findViewById(R.id.view_target_color);
        tvTargetShape = findViewById(R.id.tv_target_shape);
        gridColors = findViewById(R.id.grid_colors);
        
        // 设置网格布局
        gridColors.setColumnCount(gridSize);
        gridColors.setRowCount(gridSize);
    }
    
    private void setupClickListeners() {
        // 暂停功能已移除
    }

    private void setTargetTime() {
        // 根据难度设置目标时间（仅作为参考，不判断失败）
        // 困难模式网格更大，需要更多时间
        switch (difficulty) {
            case "easy":
                targetTimeMillis = 30000; // 30秒 (3x3网格)
                break;
            case "medium":
                targetTimeMillis = 45000; // 45秒 (4x4网格)
                break;
            case "hard":
                targetTimeMillis = 60000; // 60秒 (5x5网格)
                break;
            default:
                targetTimeMillis = 30000;
                break;
        }

        // 显示目标时间
        double targetSeconds = targetTimeMillis / 1000.0;
        String difficultyText = getDifficultyDisplayName(difficulty);
        tvTargetTime.setText("🎯 " + difficultyText + " 参考时间: " + targetSeconds + "秒");
    }

    private String getDifficultyDisplayName(String difficulty) {
        switch (difficulty) {
            case "easy":
                return "简单";
            case "medium":
                return "中等";
            case "hard":
                return "困难";
            default:
                return "中等";
        }
    }
    
    private void startGame() {
        // 根据难度设置目标时间
        setTargetTime();

        isGameRunning = true;
        gameStartTime = System.currentTimeMillis();
        currentRound = 0;
        correctAnswers = 0;
        wrongAnswers = 0;
        reactionTimes.clear();
        
        startTimer();
        nextRound();
    }
    
    private void startTimer() {
        timerRunnable = new Runnable() {
            @Override
            public void run() {
                if (isGameRunning) {
                    long elapsedTime = System.currentTimeMillis() - gameStartTime;
                    updateTimerDisplay(elapsedTime);
                }
                timerHandler.postDelayed(this, 10); // 更新频率10ms，显示毫秒
            }
        };
        timerHandler.post(timerRunnable);
    }
    
    private void updateTimerDisplay(long elapsedTime) {
        int minutes = (int) (elapsedTime / 60000);
        int seconds = (int) ((elapsedTime % 60000) / 1000);
        int milliseconds = (int) (elapsedTime % 1000);
        
        String timeText = String.format("⏱️ %02d:%02d.%03d", minutes, seconds, milliseconds);
        tvTimer.setText(timeText);
    }
    
    private void nextRound() {
        // 检查是否已达到成功条件
        if (correctAnswers >= requiredCorrectAnswers) {
            endGame();
            return;
        }

        currentRound++;
        updateScoreDisplay();

        // 生成新的目标颜色和选项
        generateRound();
        roundStartTime = System.currentTimeMillis();
    }
    
    private void generateRound() {
        // 获取目标颜色
        targetColor = colorManager.getRandomTargetColor(difficulty);

        // 生成颜色选项 - 必须在setupTargetDisplay之前，因为干扰模式需要colorOptions
        colorOptions = colorManager.getColorOptions(targetColor, difficulty, gridSize);

        // 根据游戏模式设置显示
        setupTargetDisplay();

        // 创建颜色选择网格
        createColorGrid();
    }
    
    private void setupTargetDisplay() {
        switch (gameMode) {
            case "color_match":
                // 颜色匹配模式：显示颜色块
                tvInstruction.setText("🎯 找到相同的颜色");
                viewTargetColor.setBackgroundColor(targetColor.color);
                viewTargetColor.setVisibility(View.VISIBLE);
                tvTargetShape.setVisibility(View.GONE);
                tvTargetName.setText(targetColor.name);
                break;
                
            case "color_name":
                // 颜色名称模式：只显示颜色名称
                tvInstruction.setText("📝 找到这个颜色");
                viewTargetColor.setVisibility(View.GONE);
                tvTargetShape.setVisibility(View.GONE);
                tvTargetName.setText(targetColor.name);
                break;
                
            case "interference":
                // 干扰模式：显示错误的颜色名称，但确保名称在选项中
                tvInstruction.setText("🌀 找到文字表示的颜色");
                String interferenceColorName = getInterferenceColorNameFromOptions();
                viewTargetColor.setBackgroundColor(targetColor.color);
                viewTargetColor.setVisibility(View.VISIBLE);
                tvTargetShape.setVisibility(View.GONE);
                tvTargetName.setText(interferenceColorName);
                tvTargetName.setTextColor(getRandomTextColor());
                break;
        }
    }
    
    private int getRandomTextColor() {
        int[] colors = {
            Color.parseColor("#F44336"), // 红色
            Color.parseColor("#2196F3"), // 蓝色
            Color.parseColor("#4CAF50"), // 绿色
            Color.parseColor("#FF9800"), // 橙色
            Color.parseColor("#9C27B0")  // 紫色
        };
        Random random = new Random();
        return colors[random.nextInt(colors.length)];
    }

    private String getInterferenceColorNameFromOptions() {
        // 从当前选项中随机选择一个不同于目标颜色的颜色名称
        List<String> availableNames = new ArrayList<>();
        for (ColorManager.ColorInfo option : colorOptions) {
            if (!option.equals(targetColor)) {
                availableNames.add(option.name);
            }
        }

        if (availableNames.isEmpty()) {
            return targetColor.name; // 如果没有其他选项，返回目标颜色名称
        }

        Random random = new Random();
        return availableNames.get(random.nextInt(availableNames.size()));
    }
    
    private void createColorGrid() {
        gridColors.removeAllViews();
        
        for (int i = 0; i < colorOptions.size(); i++) {
            ColorManager.ColorInfo colorInfo = colorOptions.get(i);
            MaterialButton colorButton = createColorButton(colorInfo, i);
            
            // 设置GridLayout参数
            GridLayout.LayoutParams params = new GridLayout.LayoutParams();
            params.width = 0;
            params.height = 0;
            params.columnSpec = GridLayout.spec(i % gridSize, 1f);
            params.rowSpec = GridLayout.spec(i / gridSize, 1f);
            params.setMargins(8, 8, 8, 8);
            colorButton.setLayoutParams(params);
            
            gridColors.addView(colorButton);
        }
    }
    
    private MaterialButton createColorButton(ColorManager.ColorInfo colorInfo, int index) {
        MaterialButton button = new MaterialButton(this);
        
        // 设置颜色
        button.setBackgroundTintList(getColorStateList(android.R.color.transparent));
        button.setBackgroundColor(colorInfo.color);
        
        // 设置文字（如果需要）
        if (gameMode.equals("color_name") || gameMode.equals("interference")) {
            button.setText(colorInfo.name);
            button.setTextColor(colorManager.getContrastTextColor(colorInfo.color));
            button.setTextSize(12);
        }
        
        // 设置样式
        button.setCornerRadius(12);
        button.setElevation(6);
        button.setStrokeWidth(3);
        button.setStrokeColor(getColorStateList(android.R.color.white));
        
        // 设置点击事件
        button.setOnClickListener(v -> onColorSelected(colorInfo));
        
        return button;
    }
    
    private void onColorSelected(ColorManager.ColorInfo selectedColor) {
        if (!isGameRunning) return;
        
        long reactionTime = System.currentTimeMillis() - roundStartTime;
        reactionTimes.add(reactionTime);
        
        boolean isCorrect = false;
        
        // 根据游戏模式判断正确性
        switch (gameMode) {
            case "color_match":
            case "color_name":
                isCorrect = selectedColor.equals(targetColor);
                break;
            case "interference":
                // 干扰模式：需要根据文字内容选择，而不是显示的颜色
                isCorrect = selectedColor.name.equals(tvTargetName.getText().toString());
                break;
        }
        
        if (isCorrect) {
            correctAnswers++;
            soundManager.playCorrectSound();
        } else {
            wrongAnswers++;
            soundManager.playWrongSound();
        }

        updateScoreDisplay();

        // 游戏继续进行，直到达到成功条件

        // 短暂延迟后进入下一轮
        new Handler().postDelayed(this::nextRound, 500);
    }
    
    private void updateScoreDisplay() {
        tvScore.setText(String.format("✅ %d  ❌ %d  (需要%d个正确)", correctAnswers, wrongAnswers, requiredCorrectAnswers));
    }
    

    
    private void endGame() {
        isGameRunning = false;
        timerHandler.removeCallbacks(timerRunnable);

        // 计算统计数据
        long totalTime = System.currentTimeMillis() - gameStartTime;
        int totalAttempts = correctAnswers + wrongAnswers;
        float accuracy = totalAttempts > 0 ? (float) correctAnswers / totalAttempts : 0f;
        float avgReactionTime = calculateAverageReactionTime();

        // 判断是否成功
        boolean isSuccess = correctAnswers >= requiredCorrectAnswers;

        // 保存统计数据
        saveGameStatistics(accuracy, avgReactionTime, totalTime);

        // 跳转到庆祝页面
        Intent intent = new Intent(this, CelebrationActivity.class);
        intent.putExtra("game_type", "color_training");
        intent.putExtra("is_success", isSuccess);
        intent.putExtra("score", correctAnswers);
        intent.putExtra("total", totalAttempts);
        intent.putExtra("time", totalTime);
        intent.putExtra("accuracy", accuracy);
        intent.putExtra("avg_reaction_time", avgReactionTime);
        intent.putExtra("correct_answers", correctAnswers);
        intent.putExtra("wrong_answers", wrongAnswers);
        intent.putExtra("required_correct", requiredCorrectAnswers);

        // 添加水晶奖励（仅成功时）
        if (isSuccess) {
            CrystalManager crystalManager = CrystalManager.getInstance(this);
            CrystalManager.CrystalReward reward = CrystalManager.getColorTrainingReward(difficulty);
            boolean newCrystalGenerated = crystalManager.addCrystalFragments(reward.type, reward.fragments);

            intent.putExtra("crystal_reward", reward.fragments);
            intent.putExtra("crystal_description", reward.description);
            intent.putExtra("new_crystal_generated", newCrystalGenerated);
        }

        startActivity(intent);
        finish();
    }
    
    private float calculateAverageReactionTime() {
        if (reactionTimes.isEmpty()) return 0f;
        
        long total = 0;
        for (Long time : reactionTimes) {
            total += time;
        }
        return total / (float) reactionTimes.size() / 1000f; // 转换为秒
    }
    
    private void saveGameStatistics(float accuracy, float avgReactionTime, long totalTime) {
        SharedPreferences.Editor editor = prefs.edit();

        // 更新最佳准确率
        float currentBest = prefs.getFloat("best_accuracy", 0f);
        if (accuracy > currentBest) {
            editor.putFloat("best_accuracy", accuracy);
        }

        // 更新平均反应时间
        float currentAvgTime = prefs.getFloat("avg_reaction_time", 0f);
        int totalGames = prefs.getInt("total_games", 0);
        float newAvgTime = (currentAvgTime * totalGames + avgReactionTime) / (totalGames + 1);
        editor.putFloat("avg_reaction_time", newAvgTime);

        // 更新总游戏次数
        editor.putInt("total_games", totalGames + 1);

        // 保存完成时间（如果成功）
        boolean isSuccess = correctAnswers >= requiredCorrectAnswers;
        if (isSuccess) {
            // 更新最佳完成时间
            long currentBestTime = prefs.getLong("best_completion_time", Long.MAX_VALUE);
            if (totalTime < currentBestTime) {
                editor.putLong("best_completion_time", totalTime);
            }

            // 保存最近一次完成时间
            editor.putLong("last_completion_time", totalTime);
        }

        editor.apply();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (timerHandler != null) {
            timerHandler.removeCallbacks(timerRunnable);
        }
        if (soundManager != null) {
            soundManager.stopBackgroundMusic();
        }
    }
}
