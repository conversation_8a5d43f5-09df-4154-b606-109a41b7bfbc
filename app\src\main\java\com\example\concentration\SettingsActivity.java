package com.example.concentration;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageButton;
import android.widget.Switch;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.material.card.MaterialCardView;

public class SettingsActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private TextView tvTitle;
    private Switch switchVoice, switchTestMode;
    private MaterialCardView cardVoice, cardTestMode, cardAbout;
    private int aboutClickCount = 0;
    
    private SharedPreferences prefs;
    private VoiceManager voiceManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);
        
        prefs = getSharedPreferences("app_settings", MODE_PRIVATE);
        voiceManager = VoiceManager.getInstance(this);
        
        initViews();
        setupClickListeners();
        loadSettings();
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        tvTitle = findViewById(R.id.tv_title);
        switchVoice = findViewById(R.id.switch_voice);
        switchTestMode = findViewById(R.id.switch_test_mode);
        cardVoice = findViewById(R.id.card_voice);
        cardTestMode = findViewById(R.id.card_test_mode);
        cardAbout = findViewById(R.id.card_about);
        
        tvTitle.setText("设置");
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        
        // 语音设置
        switchVoice.setOnCheckedChangeListener((buttonView, isChecked) -> {
            voiceManager.setEnabled(isChecked);
            prefs.edit().putBoolean("voice_enabled", isChecked).apply();
            
            if (isChecked) {
                voiceManager.speak("语音已开启");
            }
        });
        
        cardVoice.setOnClickListener(v -> switchVoice.setChecked(!switchVoice.isChecked()));
        
        // 测试模式设置
        switchTestMode.setOnCheckedChangeListener((buttonView, isChecked) -> {
            prefs.edit().putBoolean("test_mode_enabled", isChecked).apply();
            
            if (isChecked) {
                voiceManager.speak("测试模式已开启，所有游戏已解锁");
            } else {
                voiceManager.speak("测试模式已关闭");
            }
        });
        
        cardTestMode.setOnClickListener(v -> switchTestMode.setChecked(!switchTestMode.isChecked()));

        // 关于应用点击事件 - 点击7次触发测试模式
        cardAbout.setOnClickListener(v -> {
            aboutClickCount++;
            if (aboutClickCount >= 7) {
                // 显示测试模式选项
                cardTestMode.setVisibility(View.VISIBLE);
                // 自动开启测试模式
                switchTestMode.setChecked(true);
                prefs.edit().putBoolean("test_mode_enabled", true).apply();
                // 显示提示
                android.widget.Toast.makeText(this, "测试模式已开启！", android.widget.Toast.LENGTH_SHORT).show();
                // 重置计数
                aboutClickCount = 0;
            }
        });
    }
    
    private void loadSettings() {
        // 加载语音设置
        boolean voiceEnabled = prefs.getBoolean("voice_enabled", true);
        switchVoice.setChecked(voiceEnabled);
        
        // 加载测试模式设置
        boolean testModeEnabled = prefs.getBoolean("test_mode_enabled", false);
        switchTestMode.setChecked(testModeEnabled);
    }
    
    // 静态方法供其他Activity调用
    public static boolean isTestModeEnabled(android.content.Context context) {
        SharedPreferences prefs = context.getSharedPreferences("app_settings", android.content.Context.MODE_PRIVATE);
        return prefs.getBoolean("test_mode_enabled", false);
    }
    
    public static boolean isVoiceEnabled(android.content.Context context) {
        SharedPreferences prefs = context.getSharedPreferences("app_settings", android.content.Context.MODE_PRIVATE);
        return prefs.getBoolean("voice_enabled", true);
    }
}
