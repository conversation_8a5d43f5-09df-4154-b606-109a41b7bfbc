<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.example.demo">

    <!-- 可选权限 动态权限-->
    <!--获取设备标识IMEI。⽤于标识⽤户-->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <!--读写存储权限-->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!--定位权限-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <!--用户朗读口令时需要用到该权限【可选】-->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <!--用户下载过后安装时用到该权限【可选】-->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <application
        android:name=".App"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/tb_network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:replace="android:allowBackup,android:networkSecurityConfig">

        <activity
            android:name=".SplashActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".MainActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.ads.admob.more.TbWebViewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.channel_num.ChannelNumManageActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.SplashActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.InteractionActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.BannerActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.FeedActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.RewardVideoActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.DrawFeedActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.NativeActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.h5.WebActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.h5.GameH5Activity"
            android:configChanges="keyboard|orientation|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.VideoFragmentActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.NewsFeedFragmentActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:screenOrientation="portrait" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <provider
            android:name="com.bytedance.sdk.openadsdk.multipro.TTMultiProvider"
            android:authorities="${applicationId}.TTMultiProvider"
            android:exported="false" />
        <provider
            android:name="com.bytedance.sdk.openadsdk.TTFileProvider"
            android:authorities="${applicationId}.TTFileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/anythink_bk_tt_file_path" />
        </provider>

        <provider
            android:name="com.qq.e.comm.GDTFileProvider"
            android:authorities="${applicationId}.gdt.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/anythink_bk_gdt_file_path" />
        </provider>

        <provider
            android:name="com.sigmob.sdk.SigmobFileV4Provider"
            android:authorities="${applicationId}.sigprovider"
            android:exported="false"
            android:initOrder="200"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/anythink_bk_sigmob_file_path"/>
        </provider>

    </application>
</manifest>