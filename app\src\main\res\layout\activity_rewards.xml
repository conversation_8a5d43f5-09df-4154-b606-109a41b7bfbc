<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/md_theme_light_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="24dp">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="🏆 奖励中心"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onBackground"
                android:gravity="center" />

            <View
                android:layout_width="48dp"
                android:layout_height="48dp" />

        </LinearLayout>

        <!-- 总体统计卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📊 总体统计"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSurface"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:id="@+id/tv_total_crystals"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="💎 0"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_primary" />

                    <TextView
                        android:id="@+id/tv_total_fragments"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="✨ 0/30"
                        android:textSize="16sp"
                        android:textColor="@color/md_theme_light_secondary" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_total_score"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="总分: 0"
                        android:textSize="14sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant" />

                    <TextView
                        android:id="@+id/tv_total_games"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="总游戏数: 0"
                        android:textSize="14sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant" />

                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="总体进度"
                    android:textSize="14sp"
                    android:textColor="@color/md_theme_light_onSurfaceVariant"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="8dp" />

                <com.google.android.material.progressindicator.LinearProgressIndicator
                    android:id="@+id/progress_overall"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:indicatorColor="@color/md_theme_light_primary"
                    app:trackColor="@color/md_theme_light_surfaceVariant" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 各游戏进度卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🎮 游戏进度"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSurface"
                    android:layout_marginBottom="16dp" />

                <!-- 舒尔特方格 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🔢 舒尔特方格"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_onSurface"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/tv_schulte_stats"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="最佳: -- | 平均: -- | 游戏数: 0"
                        android:textSize="12sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant"
                        android:layout_marginBottom="4dp" />

                    <com.google.android.material.progressindicator.LinearProgressIndicator
                        android:id="@+id/progress_schulte"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:indicatorColor="@color/md_theme_light_primary" />

                </LinearLayout>

                <!-- 记忆游戏 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🧠 记忆游戏"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_onSurface"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/tv_memory_stats"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="最佳: -- | 平均: -- | 游戏数: 0"
                        android:textSize="12sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant"
                        android:layout_marginBottom="4dp" />

                    <com.google.android.material.progressindicator.LinearProgressIndicator
                        android:id="@+id/progress_memory"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:indicatorColor="@color/md_theme_light_secondary" />

                </LinearLayout>

                <!-- 颜色识别 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🌈 颜色识别"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_onSurface"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/tv_color_stats"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="最佳: -- | 平均: -- | 游戏数: 0"
                        android:textSize="12sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant"
                        android:layout_marginBottom="4dp" />

                    <com.google.android.material.progressindicator.LinearProgressIndicator
                        android:id="@+id/progress_color"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:indicatorColor="@color/md_theme_light_tertiary" />

                </LinearLayout>

                <!-- 乘法学习 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="📚 乘法学习"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_onSurface"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/tv_multiplication_stats"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="阶段1: 0% | 阶段2: 0% | 阶段3: 0%"
                        android:textSize="12sp"
                        android:textColor="@color/md_theme_light_onSurfaceVariant"
                        android:layout_marginBottom="4dp" />

                    <com.google.android.material.progressindicator.LinearProgressIndicator
                        android:id="@+id/progress_multiplication"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:indicatorColor="@color/md_theme_light_primary" />

                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 成就系统 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🏅 成就系统"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSurface"
                    android:layout_marginBottom="16dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_achievements"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 最近记录 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📝 最近记录"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSurface"
                    android:layout_marginBottom="16dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_recent_records"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </LinearLayout>
</ScrollView>
