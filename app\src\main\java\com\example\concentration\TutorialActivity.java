package com.example.concentration;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import java.util.Arrays;

/**
 * 教程显示Activity
 * 显示游戏教程内容，包含语音播放和动画效果
 */
public class TutorialActivity extends AppCompatActivity {
    
    private ImageButton btnBack, btnSkip;
    private TextView tvTitle, tvDescription;
    private RecyclerView recyclerSteps;
    private MaterialButton btnStartGame, btnReplay;
    private MaterialCardView cardContent;
    
    private TutorialManager tutorialManager;
    private VoiceManager voiceManager;
    private TutorialManager.GameType gameType;
    private TutorialManager.TutorialContent content;
    private TutorialStepsAdapter stepsAdapter;
    
    private Handler handler = new Handler();
    private int currentStep = 0;
    private boolean isPlaying = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_tutorial);
        
        // 获取游戏类型
        String gameTypeKey = getIntent().getStringExtra("game_type");
        gameType = getGameTypeFromKey(gameTypeKey);
        
        if (gameType == null) {
            finish();
            return;
        }
        
        initializeManagers();
        initViews();
        setupClickListeners();
        loadTutorialContent();
        startTutorial();
    }
    
    private void initializeManagers() {
        tutorialManager = TutorialManager.getInstance(this);
        voiceManager = VoiceManager.getInstance(this);
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        btnSkip = findViewById(R.id.btn_skip);
        tvTitle = findViewById(R.id.tv_title);
        tvDescription = findViewById(R.id.tv_description);
        recyclerSteps = findViewById(R.id.recycler_steps);
        btnStartGame = findViewById(R.id.btn_start_game);
        btnReplay = findViewById(R.id.btn_replay);
        cardContent = findViewById(R.id.card_content);
        
        // 设置RecyclerView
        recyclerSteps.setLayoutManager(new LinearLayoutManager(this));
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        
        btnSkip.setOnClickListener(v -> {
            voiceManager.stop();
            skipToEnd();
        });
        
        btnStartGame.setOnClickListener(v -> {
            // 标记教程已显示
            tutorialManager.markTutorialShown(gameType);
            startTargetGame();
        });
        
        btnReplay.setOnClickListener(v -> {
            currentStep = 0;
            startTutorial();
        });
    }
    
    private void loadTutorialContent() {
        content = tutorialManager.getTutorialContent(gameType);
        if (content == null) {
            finish();
            return;
        }
        
        tvTitle.setText(content.title);
        tvDescription.setText(content.description);
        
        // 设置步骤适配器
        stepsAdapter = new TutorialStepsAdapter(Arrays.asList(content.steps));
        recyclerSteps.setAdapter(stepsAdapter);
    }
    
    private void startTutorial() {
        if (isPlaying) return;
        
        isPlaying = true;
        currentStep = 0;
        
        // 隐藏开始按钮，显示跳过按钮
        btnStartGame.setVisibility(View.GONE);
        btnReplay.setVisibility(View.GONE);
        btnSkip.setVisibility(View.VISIBLE);
        
        // 重置步骤显示
        stepsAdapter.resetSteps();
        
        // 开始播放介绍
        playIntroduction();
    }
    
    private void playIntroduction() {
        // 添加卡片动画
        animateCard();

        // 播放游戏介绍语音，完成后开始播放步骤
        voiceManager.speakWithCallback(content.voiceText, this::playNextStep);
    }
    
    private void playNextStep() {
        if (currentStep >= content.steps.length) {
            // 所有步骤播放完毕
            tutorialCompleted();
            return;
        }

        // 高亮当前步骤
        stepsAdapter.highlightStep(currentStep);

        currentStep++;

        // 播放当前步骤语音，完成后播放下一步
        if ((currentStep - 1) < content.stepVoices.length) {
            voiceManager.speakWithCallback(content.stepVoices[currentStep - 1], this::playNextStep);
        } else {
            // 没有语音时直接播放下一步
            handler.postDelayed(this::playNextStep, 1000);
        }
    }
    
    private void tutorialCompleted() {
        isPlaying = false;
        
        // 显示开始游戏按钮
        btnStartGame.setVisibility(View.VISIBLE);
        btnReplay.setVisibility(View.VISIBLE);
        btnSkip.setVisibility(View.GONE);
        
        // 播放完成语音
        voiceManager.speak("教程介绍完毕，现在可以开始游戏了！");
        
        // 高亮所有步骤
        stepsAdapter.highlightAllSteps();
    }
    
    private void skipToEnd() {
        isPlaying = false;
        handler.removeCallbacksAndMessages(null);
        
        // 直接跳到完成状态
        tutorialCompleted();
    }
    
    private void animateCard() {
        cardContent.animate()
            .scaleX(1.05f)
            .scaleY(1.05f)
            .setDuration(300)
            .withEndAction(() -> {
                cardContent.animate()
                    .scaleX(1.0f)
                    .scaleY(1.0f)
                    .setDuration(300)
                    .start();
            })
            .start();
    }
    
    private void startTargetGame() {
        Intent intent = null;
        
        switch (gameType) {
            case SCHULTE_GRID:
                intent = new Intent(this, SchulteGridActivity.class);
                break;
            case SEQUENCE_MEMORY:
                intent = new Intent(this, MemoryGameMainActivity.class);
                break;
            case COLOR_TRAINING:
                intent = new Intent(this, ColorTrainingActivity.class);
                break;
            case MULTIPLICATION_TABLE:
                intent = new Intent(this, MultiplicationTableActivity.class);
                break;
        }
        
        if (intent != null) {
            startActivity(intent);
        }
        
        finish();
    }
    
    private TutorialManager.GameType getGameTypeFromKey(String key) {
        if (key == null) return null;
        
        for (TutorialManager.GameType type : TutorialManager.GameType.values()) {
            if (type.getKey().equals(key)) {
                return type;
            }
        }
        return null;
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
        if (voiceManager != null) {
            voiceManager.stop();
        }
    }
}
