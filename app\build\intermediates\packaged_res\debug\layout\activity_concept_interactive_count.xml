<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_light_bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 顶部标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="第1题：点数苹果"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onBackground"
                android:gravity="center"
                android:layout_marginEnd="48dp" />

        </LinearLayout>

        <!-- 指导说明 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/md_theme_light_tertiaryContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:id="@+id/tv_instruction"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="请按行点击所有的苹果，看看加法和乘法的关系"
                    android:textSize="14sp"
                    android:textColor="@color/md_theme_light_onTertiaryContainer"
                    android:gravity="center"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_progress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="进度：0/6"
                    android:textSize="12sp"
                    android:textColor="@color/md_theme_light_onTertiaryContainer"
                    android:gravity="center" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 物品网格区域 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">



                <!-- 物品网格 -->
                <GridLayout
                    android:id="@+id/grid_items"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 填空练习区域 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_fill_blanks"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/md_theme_light_tertiaryContainer"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                android:gravity="center">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📝 请填空"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onTertiaryContainer"
                    android:gravity="center"
                    android:layout_marginBottom="16dp" />

                <!-- 填空输入区域 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        app:boxStrokeColor="@color/md_theme_light_primary"
                        app:hintTextColor="@color/md_theme_light_primary">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_rows"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:maxLength="1"
                            android:background="@android:color/transparent" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="行 × "
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_onTertiaryContainer"
                        android:layout_marginHorizontal="8dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        app:boxStrokeColor="@color/md_theme_light_primary"
                        app:hintTextColor="@color/md_theme_light_primary">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_cols"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:maxLength="1"
                            android:background="@android:color/transparent" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="个"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/md_theme_light_onTertiaryContainer"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_check_answer"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:text="检查答案"
                    android:textSize="14sp"
                    app:cornerRadius="24dp"
                    app:backgroundTint="@color/md_theme_light_primary"
                    android:textColor="@android:color/white" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 加法公式显示 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/md_theme_light_primaryContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_addition_formula"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="加法：3 + 3 = 6"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onPrimaryContainer"
                    android:visibility="invisible" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 乘法公式显示 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/md_theme_light_secondaryContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                android:gravity="center">

                <TextView
                    android:id="@+id/tv_multiplication_formula"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="乘法：2 × 3 = 6"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onSecondaryContainer"
                    android:visibility="invisible" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 控制按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_reset"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="重新开始"
                android:textSize="14sp"
                app:cornerRadius="24dp"
                app:backgroundTint="@color/md_theme_light_outline"
                android:textColor="@android:color/white"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_next"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="下一题 ▶️"
                android:textSize="14sp"
                app:cornerRadius="24dp"
                app:backgroundTint="@color/md_theme_light_primary"
                android:textColor="@android:color/white"
                android:layout_marginStart="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_complete"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="完成学习 ✅"
                android:textSize="14sp"
                app:cornerRadius="24dp"
                app:backgroundTint="@color/md_theme_light_tertiary"
                android:textColor="@android:color/white"
                android:layout_marginStart="8dp"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
