package com.example.concentration;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.button.MaterialButton;
import java.util.ArrayList;
import java.util.List;

public class MultiplicationNumberActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private TextView tvTitle, tvProgress;
    private RecyclerView recyclerNumbers;
    private MaterialButton btnBackToStages;
    
    private SharedPreferences prefs;
    private NumberSelectionAdapter adapter;
    private List<NumberItem> numberItems;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_multiplication_number);
        
        prefs = getSharedPreferences("multiplication_prefs", MODE_PRIVATE);
        
        initViews();
        setupClickListeners();
        setupNumberGrid();
        updateProgress();
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        tvTitle = findViewById(R.id.tv_title);
        tvProgress = findViewById(R.id.tv_progress);
        recyclerNumbers = findViewById(R.id.recycler_numbers);
        btnBackToStages = findViewById(R.id.btn_back_to_stages);
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        btnBackToStages.setOnClickListener(v -> finish());
    }
    
    private void setupNumberGrid() {
        numberItems = new ArrayList<>();
        
        // 创建1-9的数字学习项
        for (int i = 1; i <= 9; i++) {
            NumberItem item = new NumberItem();
            item.number = i;
            item.isUnlocked = isNumberUnlocked(i);
            item.progress = getNumberProgress(i);
            item.bestTime = getNumberBestTime(i);
            item.totalGames = getNumberTotalGames(i);
            numberItems.add(item);
        }
        
        // 设置RecyclerView
        recyclerNumbers.setLayoutManager(new GridLayoutManager(this, 3));
        adapter = new NumberSelectionAdapter(numberItems, this::onNumberClick);
        recyclerNumbers.setAdapter(adapter);
    }
    
    private boolean isNumberUnlocked(int number) {
        if (number == 1) return true; // 数字1始终解锁

        // 检查测试模式
        if (SettingsActivity.isTestModeEnabled(this)) {
            return true; // 测试模式下所有数字都解锁
        }

        // 前一个数字达到80%进度才能解锁下一个
        int prevProgress = getNumberProgress(number - 1);
        return prevProgress >= 80;
    }
    
    private int getNumberProgress(int number) {
        return prefs.getInt("number_" + number + "_progress", 0);
    }
    
    private String getNumberBestTime(int number) {
        long bestTime = prefs.getLong("number_" + number + "_best_time", 0);
        if (bestTime == 0) return "暂无记录";
        
        return String.format("%.1f秒", bestTime / 1000.0);
    }
    
    private int getNumberTotalGames(int number) {
        return prefs.getInt("number_" + number + "_total_games", 0);
    }
    
    private void onNumberClick(NumberItem item) {
        if (!item.isUnlocked) {
            // 显示解锁提示
            return;
        }
        
        // 进入数字学习界面
        Intent intent = new Intent(this, NumberLearningActivity.class);
        intent.putExtra("number", item.number);
        startActivity(intent);
    }
    
    private void updateProgress() {
        int totalProgress = 0;
        int unlockedCount = 0;
        
        for (int i = 1; i <= 9; i++) {
            if (isNumberUnlocked(i)) {
                unlockedCount++;
                totalProgress += getNumberProgress(i);
            }
        }
        
        int averageProgress = unlockedCount > 0 ? totalProgress / unlockedCount : 0;
        tvProgress.setText("整体进度：" + averageProgress + "% (" + unlockedCount + "/9 已解锁)");

        // 保存阶段2进度到SharedPreferences
        prefs.edit().putInt("stage2_progress", averageProgress).apply();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 刷新数据
        setupNumberGrid();
        updateProgress();
    }
    
    // 数字项数据类
    public static class NumberItem {
        public int number;
        public boolean isUnlocked;
        public int progress;
        public String bestTime;
        public int totalGames;
    }
}
