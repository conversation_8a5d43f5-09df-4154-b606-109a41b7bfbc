<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape>
            <solid android:color="@color/md_theme_light_primary" />
            <corners android:radius="24dp" />
        </shape>
    </item>
    <item>
        <shape>
            <gradient
                android:startColor="@color/gradient_start"
                android:endColor="@color/gradient_end"
                android:angle="45" />
            <corners android:radius="24dp" />
        </shape>
    </item>
</selector>
