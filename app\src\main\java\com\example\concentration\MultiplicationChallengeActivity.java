package com.example.concentration;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.button.MaterialButton;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;

public class MultiplicationChallengeActivity extends AppCompatActivity {
    
    private ImageButton btnBack;
    private TextView tvTitle, tvScore, tvTimer, tvQuestion, tvLevel;
    private RecyclerView recyclerAnswers;
    private MaterialButton btnNext, btnRestart;
    
    private SharedPreferences prefs;
    private SoundManager soundManager;
    private VoiceManager voiceManager;
    private ChallengeAnswerAdapter adapter;
    
    private int currentLevel = 1;
    private int currentQuestion = 0;
    private int score = 0;
    private long startTime;
    private Handler timerHandler = new Handler();
    private Runnable timerRunnable;
    
    private List<ChallengeQuestion> questions;
    private Random random = new Random();
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_multiplication_challenge);
        
        prefs = getSharedPreferences("multiplication_prefs", MODE_PRIVATE);
        soundManager = SoundManager.getInstance(this);
        voiceManager = VoiceManager.getInstance(this);
        
        initViews();
        setupClickListeners();
        generateQuestions();
        startGame();
    }
    
    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        tvTitle = findViewById(R.id.tv_title);
        tvScore = findViewById(R.id.tv_score);
        tvTimer = findViewById(R.id.tv_timer);
        tvQuestion = findViewById(R.id.tv_question);
        tvLevel = findViewById(R.id.tv_level);
        recyclerAnswers = findViewById(R.id.recycler_answers);
        btnNext = findViewById(R.id.btn_next);
        btnRestart = findViewById(R.id.btn_restart);
        
        tvTitle.setText("九九乘法表挑战");
        tvScore.setText("得分: 0");
        tvLevel.setText("关卡: " + currentLevel);
    }
    
    private void setupClickListeners() {
        btnBack.setOnClickListener(v -> finish());
        btnNext.setOnClickListener(v -> nextQuestion());
        btnRestart.setOnClickListener(v -> restartGame());
    }
    
    private void generateQuestions() {
        questions = new ArrayList<>();
        
        // 根据关卡生成不同难度的题目
        int questionCount = Math.min(10 + currentLevel * 5, 50); // 每关15-50题
        
        for (int i = 0; i < questionCount; i++) {
            ChallengeQuestion question = new ChallengeQuestion();
            
            // 根据关卡调整难度范围
            int maxNumber = Math.min(2 + currentLevel, 9);
            question.num1 = random.nextInt(maxNumber) + 1;
            question.num2 = random.nextInt(9) + 1;
            question.correctAnswer = question.num1 * question.num2;
            
            // 生成错误答案选项
            question.options = generateOptions(question.correctAnswer);
            
            questions.add(question);
        }
        
        Collections.shuffle(questions);
    }
    
    private List<Integer> generateOptions(int correctAnswer) {
        List<Integer> options = new ArrayList<>();
        options.add(correctAnswer);
        
        // 生成3个错误答案
        while (options.size() < 4) {
            int wrongAnswer;
            if (random.nextBoolean()) {
                wrongAnswer = correctAnswer + random.nextInt(10) + 1;
            } else {
                wrongAnswer = Math.max(1, correctAnswer - random.nextInt(10) - 1);
            }
            
            if (!options.contains(wrongAnswer)) {
                options.add(wrongAnswer);
            }
        }
        
        Collections.shuffle(options);
        return options;
    }
    
    private void startGame() {
        currentQuestion = 0;
        score = 0;
        startTime = System.currentTimeMillis();
        
        setupQuestion();
        startTimer();
    }
    
    private void setupQuestion() {
        if (currentQuestion >= questions.size()) {
            // 关卡完成
            levelCompleted();
            return;
        }
        
        ChallengeQuestion question = questions.get(currentQuestion);
        tvQuestion.setText(question.num1 + " × " + question.num2 + " = ?");

        // 语音读出题目
        voiceManager.speakMultiplicationQuestion(question.num1, question.num2);

        // 设置答案选项
        recyclerAnswers.setLayoutManager(new GridLayoutManager(this, 2));
        adapter = new ChallengeAnswerAdapter(question.options, this::onAnswerClick);
        recyclerAnswers.setAdapter(adapter);
        
        btnNext.setEnabled(false);
    }
    
    private void onAnswerClick(int selectedAnswer) {
        ChallengeQuestion question = questions.get(currentQuestion);
        
        if (selectedAnswer == question.correctAnswer) {
            // 答对了
            score += (10 + currentLevel * 2); // 关卡越高得分越多
            soundManager.playCorrectSound();
            voiceManager.speakCorrect();
        } else {
            soundManager.playWrongSound();
            voiceManager.speakWrong();
        }
        
        tvScore.setText("得分: " + score);
        adapter.showResult(question.correctAnswer);
        btnNext.setEnabled(true);
    }
    
    private void nextQuestion() {
        currentQuestion++;
        setupQuestion();
    }
    
    private void levelCompleted() {
        timerHandler.removeCallbacks(timerRunnable);
        
        long gameTime = System.currentTimeMillis() - startTime;
        int accuracy = calculateAccuracy();
        
        // 保存关卡记录
        saveLevelRecord(gameTime, accuracy);
        
        // 检查是否可以进入下一关
        if (accuracy >= 80 && currentLevel < 9) {
            // 解锁下一关
            currentLevel++;
            tvLevel.setText("关卡: " + currentLevel);
            generateQuestions();
            startGame();
        } else {
            // 显示完成结果
            showCompletionDialog(gameTime, accuracy);
        }
    }
    
    private int calculateAccuracy() {
        // 简化计算，基于得分估算正确率
        int maxScore = questions.size() * (10 + currentLevel * 2);
        return Math.min(100, (score * 100) / maxScore);
    }
    
    private void saveLevelRecord(long gameTime, int accuracy) {
        String levelKey = "challenge_level_" + currentLevel;
        
        // 保存最佳时间
        long bestTime = prefs.getLong(levelKey + "_best_time", Long.MAX_VALUE);
        if (gameTime < bestTime) {
            prefs.edit().putLong(levelKey + "_best_time", gameTime).apply();
        }
        
        // 保存最高正确率
        int bestAccuracy = prefs.getInt(levelKey + "_best_accuracy", 0);
        if (accuracy > bestAccuracy) {
            prefs.edit().putInt(levelKey + "_best_accuracy", accuracy).apply();
        }
        
        // 更新游戏次数
        int playCount = prefs.getInt(levelKey + "_play_count", 0);
        prefs.edit().putInt(levelKey + "_play_count", playCount + 1).apply();
        
        // 更新阶段3总进度
        updateStage3Progress();

        // 奖励水晶碎片（基于准确率）
        CrystalManager crystalManager = CrystalManager.getInstance(this);
        int crystalReward = Math.max(5, accuracy / 10); // 最少5个碎片，最多10个
        crystalManager.addCrystalFragments(CrystalManager.CrystalType.MULTIPLICATION, crystalReward);
    }
    
    private void updateStage3Progress() {
        int totalProgress = 0;
        for (int i = 1; i <= 9; i++) {
            int accuracy = prefs.getInt("challenge_level_" + i + "_best_accuracy", 0);
            totalProgress += accuracy;
        }
        int averageProgress = totalProgress / 9;
        prefs.edit().putInt("stage3_progress", averageProgress).apply();
    }
    
    private void showCompletionDialog(long gameTime, int accuracy) {
        // 保存游戏统计
        GameDataManager gameDataManager = new GameDataManager(this);
        gameDataManager.saveMultiplicationGameResult(currentLevel, score, gameTime);

        // 返回结果
        Intent result = new Intent();
        result.putExtra("level", currentLevel);
        result.putExtra("score", score);
        result.putExtra("accuracy", accuracy);
        result.putExtra("game_time", gameTime);
        setResult(RESULT_OK, result);
        finish();
    }
    
    private void restartGame() {
        generateQuestions();
        startGame();
    }
    
    private void startTimer() {
        timerRunnable = new Runnable() {
            @Override
            public void run() {
                long elapsed = System.currentTimeMillis() - startTime;
                int seconds = (int) (elapsed / 1000);
                int minutes = seconds / 60;
                seconds = seconds % 60;
                
                tvTimer.setText(String.format("时间: %02d:%02d", minutes, seconds));
                timerHandler.postDelayed(this, 1000);
            }
        };
        timerHandler.post(timerRunnable);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (timerHandler != null) {
            timerHandler.removeCallbacks(timerRunnable);
        }
        if (voiceManager != null) {
            voiceManager.stop();
        }
        if (soundManager != null) {
            soundManager.stopBackgroundMusic();
        }
    }
    
    // 挑战题目数据类
    public static class ChallengeQuestion {
        public int num1;
        public int num2;
        public int correctAnswer;
        public List<Integer> options;
    }
}
